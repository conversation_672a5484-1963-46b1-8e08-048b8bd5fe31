import os
import sys
import time
import yaml
import json
import logging
import threading
import uuid
import duckdb
from typing import Dict, Any, Optional, List, Tuple
from flask import Flask, request, jsonify

# 添加父目录到路径，以便导入其他模块
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.insert(0, parent_dir)

# 导入策略运行器
from live_strategy_runner import run_live_strategy

# 定义常量
PORTFOLIO_DIR = os.path.join(parent_dir, 'portfolio')
CUSTOM_STRATEGY_DB_FILE = os.path.join(parent_dir, 'custom_strategy_list.duckdb')
CUSTOM_DB_TABLE_NAME = 'custom_strategies'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('LiveStrategyServer')

# 辅助函数：获取策略YAML
def get_strategy_yaml(strategy_id: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """
    获取策略的YAML内容

    Args:
        strategy_id: 策略ID

    Returns:
        Tuple[Optional[str], Optional[str], Optional[str]]: (策略YAML内容, 策略名称, 策略类型)
        如果策略不存在，则返回 (None, None, None)
    """
    # 1. 尝试从系统策略目录获取
    yaml_path = os.path.join(PORTFOLIO_DIR, strategy_id, 'index.yaml')
    if os.path.exists(yaml_path):
        try:
            with open(yaml_path, 'r', encoding='utf-8') as f:
                yaml_content = f.read()
                # 解析YAML获取策略名称
                strategy_config = yaml.safe_load(yaml_content)
                strategy_name = strategy_config.get('strategy_name', strategy_id)
                return yaml_content, strategy_name, 'system'
        except Exception as e:
            logger.error(f"读取系统策略 {strategy_id} YAML失败: {e}")

    # 2. 尝试从自定义策略数据库获取
    try:
        con = duckdb.connect(CUSTOM_STRATEGY_DB_FILE, read_only=True)
        result = con.execute(
            f"SELECT content_yaml FROM {CUSTOM_DB_TABLE_NAME} WHERE strategy_id = ?",
            (strategy_id,)
        ).fetchone()
        con.close()

        if result and result[0]:
            yaml_content = result[0]
            # 解析YAML获取策略名称
            strategy_config = yaml.safe_load(yaml_content)
            strategy_name = strategy_config.get('strategy_name', strategy_id)
            return yaml_content, strategy_name, 'custom'
    except Exception as e:
        logger.error(f"从数据库获取自定义策略 {strategy_id} YAML失败: {e}")

    # 3. 如果都没有找到，返回None
    return None, None, None

# 创建Flask应用
app = Flask(__name__)

# 策略管理器
class LiveStrategyManager:
    def __init__(self):
        self.strategies = {}  # 存储策略实例
        self.threads = {}     # 存储策略线程
        self.status = {}      # 存储策略状态
        self.configs = {}     # 存储策略配置

    def deploy_strategy(self, live_id: str, source_strategy_id: str, strategy_yaml: str, strategy_name: str, account_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        部署策略

        Args:
            live_id: 实盘项目ID (UUID)
            source_strategy_id: 原策略ID
            strategy_yaml: 策略YAML内容
            strategy_name: 策略名称
            account_info: 账户信息
        """
        logger.info(f"开始部署策略: {live_id}, 原策略: {source_strategy_id}, 策略名称: {strategy_name}")

        # 检查策略是否已存在
        if live_id in self.threads and self.threads[live_id].is_alive():
            logger.warning(f"实盘项目已存在: {live_id}")
            return {"success": False, "message": f"实盘项目 {live_id} 已存在"}

        # 存储策略配置
        self.configs[live_id] = {
            "strategy_yaml": strategy_yaml,
            "source_strategy_id": source_strategy_id,
            "strategy_name": strategy_name,
            "account_info": account_info,
            "created_at": time.time()
        }

        # 更新策略状态
        self.status[live_id] = {
            "status": "deploying",
            "message": "正在部署策略",
            "updated_at": time.time()
        }

        # 创建并启动策略线程
        thread = threading.Thread(
            target=self._run_strategy_thread,
            args=(live_id, strategy_yaml, account_info)
        )
        thread.daemon = True
        thread.start()

        # 存储线程引用
        self.threads[live_id] = thread

        logger.info(f"策略部署成功: {live_id}, 策略名称: {strategy_name}")
        return {"success": True, "message": f"策略 {strategy_name} 已部署", "id": live_id, "name": strategy_name}

    def _run_strategy_thread(self, live_id: str, strategy_yaml: str, account_info: Dict[str, Any]):
        """在独立线程中运行策略"""
        try:
            # 更新策略状态
            self.status[live_id] = {
                "status": "starting",
                "message": "正在启动策略",
                "updated_at": time.time()
            }

            # 运行策略
            engine = run_live_strategy(live_id, strategy_yaml, account_info)

            if engine:
                # 存储引擎实例
                self.strategies[live_id] = engine

                # 更新策略状态
                self.status[live_id] = {
                    "status": "running",
                    "message": "策略运行中",
                    "updated_at": time.time()
                }

                # 保持线程运行
                while True:
                    time.sleep(1)
            else:
                # 更新策略状态
                self.status[live_id] = {
                    "status": "failed",
                    "message": "策略启动失败",
                    "updated_at": time.time()
                }
        except Exception as e:
            logger.error(f"实盘项目 {live_id} 运行出错: {e}", exc_info=True)

            # 更新策略状态
            self.status[live_id] = {
                "status": "error",
                "message": f"策略运行出错: {e}",
                "updated_at": time.time()
            }

    def start_strategy(self, live_id: str) -> Dict[str, Any]:
        """启动策略"""
        logger.info(f"开始启动实盘项目: {live_id}")

        # 检查策略是否存在
        if live_id not in self.configs:
            logger.warning(f"实盘项目不存在: {live_id}")
            return {"success": False, "message": f"实盘项目 {live_id} 不存在"}

        # 检查策略是否已在运行
        if live_id in self.threads and self.threads[live_id].is_alive() and self.status.get(live_id, {}).get("status") == "running":
            logger.warning(f"实盘项目已在运行: {live_id}")
            return {"success": False, "message": f"实盘项目 {live_id} 已在运行"}

        # 获取策略配置
        strategy_yaml = self.configs[live_id]["strategy_yaml"]
        account_info = self.configs[live_id]["account_info"]
        strategy_name = self.configs[live_id]["strategy_name"]

        # 创建并启动策略线程
        thread = threading.Thread(
            target=self._run_strategy_thread,
            args=(live_id, strategy_yaml, account_info)
        )
        thread.daemon = True
        thread.start()

        # 存储线程引用
        self.threads[live_id] = thread

        logger.info(f"实盘项目启动成功: {live_id}, 策略名称: {strategy_name}")
        return {"success": True, "message": f"策略 {strategy_name} 已启动", "id": live_id, "name": strategy_name}

    def stop_strategy(self, live_id: str) -> Dict[str, Any]:
        """停止策略"""
        logger.info(f"开始停止实盘项目: {live_id}")

        # 检查策略是否存在
        if live_id not in self.configs:
            logger.warning(f"实盘项目不存在: {live_id}")
            return {"success": False, "message": f"实盘项目 {live_id} 不存在"}

        # 检查策略是否在运行
        if live_id not in self.strategies:
            logger.warning(f"实盘项目未在运行: {live_id}")
            return {"success": False, "message": f"实盘项目 {live_id} 未在运行"}

        try:
            # 获取策略名称
            strategy_name = self.configs[live_id]["strategy_name"]

            # 停止引擎
            engine = self.strategies[live_id]
            engine.stop()

            # 移除引用
            del self.strategies[live_id]

            # 更新策略状态
            self.status[live_id] = {
                "status": "stopped",
                "message": "策略已停止",
                "updated_at": time.time()
            }

            logger.info(f"实盘项目停止成功: {live_id}, 策略名称: {strategy_name}")
            return {"success": True, "message": f"策略 {strategy_name} 已停止", "id": live_id, "name": strategy_name}
        except Exception as e:
            logger.error(f"停止实盘项目 {live_id} 失败: {e}", exc_info=True)

            # 更新策略状态
            self.status[live_id] = {
                "status": "error",
                "message": f"停止策略失败: {e}",
                "updated_at": time.time()
            }

            return {"success": False, "message": f"停止实盘项目 {live_id} 失败: {e}"}

    def delete_strategy(self, live_id: str) -> Dict[str, Any]:
        """删除策略"""
        logger.info(f"开始删除实盘项目: {live_id}")

        # 检查策略是否存在
        if live_id not in self.configs:
            logger.warning(f"实盘项目不存在: {live_id}")
            return {"success": False, "message": f"实盘项目 {live_id} 不存在"}

        # 检查策略是否在运行
        if live_id in self.strategies:
            logger.warning(f"实盘项目正在运行，无法删除: {live_id}")
            return {"success": False, "message": f"实盘项目 {live_id} 正在运行，请先停止策略"}

        try:
            # 获取策略名称
            strategy_name = self.configs[live_id]["strategy_name"]

            # 删除线程引用（如果存在）
            if live_id in self.threads:
                del self.threads[live_id]

            # 删除状态信息
            if live_id in self.status:
                del self.status[live_id]

            # 删除配置信息
            del self.configs[live_id]

            logger.info(f"实盘项目删除成功: {live_id}, 策略名称: {strategy_name}")
            return {"success": True, "message": f"策略 {strategy_name} 已删除", "id": live_id}
        except Exception as e:
            logger.error(f"删除实盘项目 {live_id} 失败: {e}", exc_info=True)
            return {"success": False, "message": f"删除实盘项目 {live_id} 失败: {e}"}

    def get_strategy_list(self) -> List[Dict[str, Any]]:
        """获取策略列表"""
        result = []

        for live_id, config in self.configs.items():
            status_info = self.status.get(live_id, {"status": "unknown", "message": "未知状态", "updated_at": 0})

            # 获取策略名称和原策略ID
            strategy_name = config.get("strategy_name", "未命名策略")
            source_strategy_id = config.get("source_strategy_id", "")

            # 解析策略YAML获取交易类型
            try:
                strategy_config = yaml.safe_load(config["strategy_yaml"])
                trading_type = strategy_config.get("trading_type", "unknown")
            except Exception as e:
                logger.error(f"解析策略 {live_id} YAML失败: {e}")
                trading_type = "unknown"

            # 构建策略信息
            strategy_info = {
                "id": live_id,
                "name": strategy_name,
                "display_name": f"实盘 - {strategy_name}",
                "source_strategy_id": source_strategy_id,
                "status": status_info["status"],
                "message": status_info["message"],
                "trading_type": trading_type,
                "created_at": config["created_at"],
                "updated_at": status_info["updated_at"]
            }

            result.append(strategy_info)

        return result

    def get_strategy_details(self, live_id: str) -> Optional[Dict[str, Any]]:
        """获取策略详情"""
        # 检查策略是否存在
        if live_id not in self.configs:
            logger.warning(f"实盘项目不存在: {live_id}")
            return None

        config = self.configs[live_id]
        status_info = self.status.get(live_id, {"status": "unknown", "message": "未知状态", "updated_at": 0})

        # 获取策略名称和原策略ID
        strategy_name = config.get("strategy_name", "未命名策略")
        source_strategy_id = config.get("source_strategy_id", "")

        # 解析策略YAML获取交易类型
        try:
            strategy_config = yaml.safe_load(config["strategy_yaml"])
            trading_type = strategy_config.get("trading_type", "unknown")
        except Exception as e:
            logger.error(f"解析策略 {live_id} YAML失败: {e}")
            trading_type = "unknown"

        # 构建策略详情
        strategy_details = {
            "id": live_id,
            "name": strategy_name,
            "display_name": f"实盘 - {strategy_name}",
            "source_strategy_id": source_strategy_id,
            "status": status_info["status"],
            "message": status_info["message"],
            "trading_type": trading_type,
            "created_at": config["created_at"],
            "updated_at": status_info["updated_at"],
            "config": {
                "strategy_yaml": config["strategy_yaml"],
                "account_info": {
                    "account_id": config["account_info"].get("account_id", ""),
                    "initial_capital": config["account_info"].get("initial_capital", 0),
                    "risk_settings": config["account_info"].get("risk_settings", {})
                }
            }
        }

        return strategy_details

# 创建策略管理器实例
strategy_manager = LiveStrategyManager()

# API路由
@app.route('/strategy/deploy', methods=['POST'])
def deploy_strategy():
    """部署策略"""
    try:
        data = request.json

        # 检查必要参数
        if not data:
            return jsonify({"success": False, "error": "缺少请求数据"}), 400

        source_strategy_id = data.get('strategyId')  # 原策略ID
        account_id = data.get('accountId')
        initial_capital = data.get('initialCapital')

        if not source_strategy_id or not account_id or not initial_capital:
            return jsonify({"success": False, "error": "缺少必要参数"}), 400

        # 获取策略YAML和名称
        strategy_yaml, strategy_name, _ = get_strategy_yaml(source_strategy_id)

        if not strategy_yaml or not strategy_name:
            return jsonify({"success": False, "error": f"无法获取策略 {source_strategy_id} 的配置信息"}), 404

        # 生成实盘项目ID (UUID)
        live_id = str(uuid.uuid4())

        # 构建账户信息
        account_info = {
            'account_id': account_id,
            'account_host': data.get('accountHost', '127.0.0.1'),
            'account_port': data.get('accountPort', 8888),
            'account_user': data.get('accountUser', 'user'),
            'account_pass': data.get('accountPass', 'pass'),
            'account_key': data.get('accountKey', ''),
            'initial_capital': initial_capital,
            'risk_settings': data.get('riskSettings', {
                'max_order_size': 100,
                'max_daily_trades': 20,
                'stop_loss_percent': 5
            })
        }

        # 部署策略
        result = strategy_manager.deploy_strategy(
            live_id=live_id,
            source_strategy_id=source_strategy_id,
            strategy_yaml=strategy_yaml,
            strategy_name=strategy_name,
            account_info=account_info
        )

        if result["success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 400
    except Exception as e:
        logger.error(f"部署策略失败: {e}", exc_info=True)
        return jsonify({"success": False, "error": f"部署策略失败: {e}"}), 500

@app.route('/strategy/live/start/<strategy_id>', methods=['POST'])
def start_strategy(strategy_id):
    """启动策略"""
    try:
        result = strategy_manager.start_strategy(strategy_id)

        if result["success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 400
    except Exception as e:
        logger.error(f"启动策略失败: {e}", exc_info=True)
        return jsonify({"success": False, "error": f"启动策略失败: {e}"}), 500

@app.route('/strategy/live/stop/<strategy_id>', methods=['POST'])
def stop_strategy(strategy_id):
    """停止策略"""
    try:
        result = strategy_manager.stop_strategy(strategy_id)

        if result["success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 400
    except Exception as e:
        logger.error(f"停止策略失败: {e}", exc_info=True)
        return jsonify({"success": False, "error": f"停止策略失败: {e}"}), 500

@app.route('/strategy/live/delete/<strategy_id>', methods=['DELETE'])
def delete_strategy(strategy_id):
    """删除策略"""
    try:
        result = strategy_manager.delete_strategy(strategy_id)

        if result["success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 400
    except Exception as e:
        logger.error(f"删除策略失败: {e}", exc_info=True)
        return jsonify({"success": False, "error": f"删除策略失败: {e}"}), 500

@app.route('/strategy/live', methods=['GET'])
def get_live_strategies():
    """获取实盘策略列表"""
    try:
        strategies = strategy_manager.get_strategy_list()
        return jsonify({"success": True, "data": strategies}), 200
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}", exc_info=True)
        return jsonify({"success": False, "error": f"获取策略列表失败: {e}"}), 500

@app.route('/strategy/live/<strategy_id>', methods=['GET'])
def get_live_strategy_details(strategy_id):
    """获取实盘策略详情"""
    try:
        strategy = strategy_manager.get_strategy_details(strategy_id)

        if strategy:
            return jsonify({"success": True, "data": strategy}), 200
        else:
            return jsonify({"success": False, "error": f"策略 {strategy_id} 不存在"}), 404
    except Exception as e:
        logger.error(f"获取策略详情失败: {e}", exc_info=True)
        return jsonify({"success": False, "error": f"获取策略详情失败: {e}"}), 500

if __name__ == "__main__":
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5003, debug=True)
