# 数据引擎日志配置文件
# 配置数据引擎和解析器的日志输出

# 根日志配置
root:
    async: false                    # 同步日志输出
    level: info                     # 日志级别：debug/info/warn/error
    sinks:
    # 文件日志输出
    -   type: daily_file_sink
        filename: DtLogs/DataEngine.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false
    
    # 控制台日志输出
    -   type: console_sink
        pattern: '[%m.%d %H:%M:%S - %^%-5l%$] %v'

# 解析器日志配置
parser:
    async: false                    # 同步日志输出
    level: info                     # 日志级别
    sinks:
    # 解析器专用日志文件
    -   type: daily_file_sink
        filename: DtLogs/Parser.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false

# 数据存储日志配置
storage:
    async: false                    # 同步日志输出
    level: info                     # 日志级别
    sinks:
    # 存储模块专用日志文件
    -   type: daily_file_sink
        filename: DtLogs/Storage.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false

# 广播器日志配置
broadcaster:
    async: false                    # 同步日志输出
    level: info                     # 日志级别
    sinks:
    # 广播器专用日志文件
    -   type: daily_file_sink
        filename: DtLogs/Broadcaster.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false
