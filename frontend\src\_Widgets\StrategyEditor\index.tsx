import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, DatePicker, Button, message, Spin, Select, Row, Col, List, Tabs, InputNumber, Checkbox } from 'antd';
import { CloseOutlined, PlusOutlined, ImportOutlined } from '@ant-design/icons';
import { EventBus } from '@/events/eventBus';
import { StrategyEvents, KeyboardEvents, MarketEvents } from '@/events/events';
import { MarketType } from '@/shared_types/market';
type StrategyDataForEdit = StrategyEvents.StrategyDataForEdit;
import dayjs from 'dayjs';
import yaml from 'js-yaml';
import KeyboardCoder from '../../_Widgets/KeyboardCoder';

// --- 增加内联样式以调整高度 ---
const styles = {
  tabsContainer: {
    display: 'flex',
    flexDirection: 'column' as 'column', // 类型断言
    height: '350px', // 设定一个固定高度，与右侧列表对齐
  },
  tabs: {
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column' as 'column',
  },
  ruleListHeader: {
    paddingTop: '4px',      // 减小上边距
    paddingBottom: '4px',   // 减小下边距
    minHeight: 'auto',
    fontSize: '13px',     // 可选：减小字体
  },
  ruleListItem: {
    paddingTop: '2px',      // 显著减小上边距
    paddingBottom: '2px',   // 显著减小下边距
  },
  ruleTextArea: {
    border: 'none',
    background: 'transparent',
    resize: 'none' as 'none',
    boxShadow: 'none',
    padding: '0', // 移除内部 padding
  },
};
// --- 样式定义结束 ---

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

interface RuleSet {
  formulas?: string[];
  at_least_count?: number;
}

interface StrategyYaml {
  strategy_name?: string;
  universe?: string[];
  backtest?: {
    stime?: string;
    etime?: string | null;
  };
  order_by?: {
    formula?: string;
  };
  top_n?: number;
  trading_type?: TradingType;
  buy_rules?: RuleSet;
  sell_rules?: RuleSet;
  data_freq?: string;
  bar_count?: number;
}

// 定义交易类型
type TradingType = 'stock' | 'etf' | 'option' | 'crypto';

const formatDateToYaml = (date: dayjs.Dayjs | null): string => {
  if (!date) return '';
  return date.format('YYYYMMDDHHmm');
};

const StrategyEditor: React.FC = () => {
  const [form] = Form.useForm();
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStrategyId, setCurrentStrategyId] = useState<string | null>(null);
  const [symbols, setSymbols] = useState<string[]>([]);
  const [currentYamlString, setCurrentYamlString] = useState<string | null>(null);
  const [orderByFormula, setOrderByFormula] = useState<string>('');
  const [topN, setTopN] = useState<number | null>(1);
  const [buyRulesFormulas, setBuyRulesFormulas] = useState<string[]>([]);
  const [buyRulesCount, setBuyRulesCount] = useState<number | null>(1);
  const [sellRulesFormulas, setSellRulesFormulas] = useState<string[]>([]);
  const [sellRulesCount, setSellRulesCount] = useState<number | null>(1);
  const [newBuyRule, setNewBuyRule] = useState('');
  const [newSellRule, setNewSellRule] = useState('');
  // 新增状态：交易品类
  const [tradingType, setTradingType] = useState<TradingType>('etf'); // 默认为 etf
  // 新增状态：数据周期和K线数量
  const [dataFreq, setDataFreq] = useState<string>('day');
  const [barCount, setBarCount] = useState<number>(50);
  // 新增状态：调仓间隔
  const [rebalanceInterval, setRebalanceInterval] = useState<string>('daily');

  // 导入备选品种相关状态
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importSymbolList, setImportSymbolList] = useState<MarketEvents.SymbolListItem[]>([]);
  const [selectedImportSymbols, setSelectedImportSymbols] = useState<string[]>([]);
  const [importListName, setImportListName] = useState('');

  const parseYamlDate = (dateStr: string | number | null | undefined): dayjs.Dayjs | null => {
    if (!dateStr) return null;
    const s = String(dateStr);
    if (!/^\d{12}$/.test(s) && !/^\d{8}$/.test(s)) {
      const parsed = dayjs(s);
      return parsed.isValid() ? parsed : null;
    }
    const year = parseInt(s.substring(0, 4), 10);
    const month = parseInt(s.substring(4, 6), 10) - 1;
    const day = parseInt(s.substring(6, 8), 10);
    const hour = s.length === 12 ? parseInt(s.substring(8, 10), 10) : 0;
    const minute = s.length === 12 ? parseInt(s.substring(10, 12), 10) : 0;
    const d = dayjs().year(year).month(month).date(day).hour(hour).minute(minute).second(0).millisecond(0);
    return d.isValid() ? d : null;
  };

  useEffect(() => {
    const handleShowEditor = (data: StrategyDataForEdit) => {
      console.log('[StrategyEditor] 接收到编辑事件, ID:', data.id);
      try {
        const parsedYaml = yaml.load(data.yaml) as StrategyYaml;
        console.log('[StrategyEditor] 解析后的 YAML:', parsedYaml);

        const strategyName = parsedYaml?.strategy_name || '';
        const universe = parsedYaml?.universe || [];
        const startDate = parseYamlDate(parsedYaml?.backtest?.stime);
        const endDate = parseYamlDate(parsedYaml?.backtest?.etime);
        const orderByFormulaValue = parsedYaml?.order_by?.formula || '';
        const topNValue = parsedYaml?.top_n ?? 1;
        const buyRulesData = parsedYaml?.buy_rules;
        const sellRulesData = parsedYaml?.sell_rules;
        // 读取 trading_type，默认为 'etf'
        const loadedTradingType = parsedYaml?.trading_type || 'etf';
        // 读取 data_freq 和 bar_count
        const loadedDataFreq = parsedYaml?.data_freq || 'day';
        const loadedBarCount = parsedYaml?.bar_count || 50;
        // 读取 rebalance_interval
        const loadedRebalanceInterval = parsedYaml?.rebalance_interval || 'daily';

        setCurrentStrategyId(data.id);
        setSymbols(universe);
        setCurrentYamlString(data.yaml);
        setOrderByFormula(orderByFormulaValue);
        setTopN(topNValue);
        setBuyRulesFormulas(buyRulesData?.formulas || []);
        setBuyRulesCount(buyRulesData?.at_least_count ?? 1);
        setSellRulesFormulas(sellRulesData?.formulas || []);
        setSellRulesCount(sellRulesData?.at_least_count ?? 1);
        // 设置交易品类状态
        setTradingType(loadedTradingType as TradingType);
        // 设置数据周期和K线数量状态
        setDataFreq(loadedDataFreq);
        setBarCount(loadedBarCount);
        // 设置调仓间隔状态
        setRebalanceInterval(loadedRebalanceInterval);

        form.setFieldsValue({
          name: strategyName,
          topN: topNValue,
          backtestRange: [startDate, endDate],
          buyRulesCount: buyRulesData?.at_least_count ?? 1,
          sellRulesCount: sellRulesData?.at_least_count ?? 1,
          // 设置表单的 tradingType 初始值
          tradingType: loadedTradingType,
          // 设置表单的 dataFreq 和 barCount 初始值
          dataFreq: loadedDataFreq,
          barCount: loadedBarCount,
          // 设置表单的 rebalanceInterval 初始值
          rebalanceInterval: loadedRebalanceInterval,
        });
        setIsVisible(true);

      } catch (e) {
        console.error('[StrategyEditor] 解析 YAML 失败:', e);
        message.error('解析策略配置失败，无法编辑。');
        setIsVisible(false);
      }
    };

    const subscription = EventBus.on(StrategyEvents.Types.SHOW_STRATEGY_EDITOR, handleShowEditor);

    return () => {
      subscription.unsubscribe();
    };
  }, [form]);

  useEffect(() => {
    const handleInputComplete = (payload: any) => {
      if (isVisible) {
        let formattedSymbol = ''; // 初始化为空字符串

        // 检查必要的 payload 字段是否存在
        if (payload && payload.market && payload.code) {
            if (payload.market === MarketType.CRYPTO) {
                // 加密货币特定格式
                formattedSymbol = `CRYPTO.CPT.${payload.code}`;
            } else if (payload.market === MarketType.STOCK) {
                // 股票特定格式 (保留 STK)
                const displayMarket = 'STK';
                formattedSymbol = `${payload.exchange || 'UNKNOWN'}.${displayMarket}.${payload.code}`;
            } else if (payload.exchange) {
                // 其他类型 (如 ETF)，使用 market 作为中间部分
                formattedSymbol = `${payload.exchange}.${payload.market}.${payload.code}`;
            } else {
                 console.warn('[StrategyEditor] Received payload missing exchange for non-crypto/stock symbol:', payload);
            }
        } else {
             console.warn('[StrategyEditor] Received incomplete payload for symbol formatting:', payload);
        }

        if (formattedSymbol) { // 仅当生成了有效的 formattedSymbol 才继续
        console.log('[StrategyEditor] 接收到品种代码输入完成，格式化为:', formattedSymbol);
            if (!symbols.includes(formattedSymbol)) {
          setSymbols(prevSymbols => [...prevSymbols, formattedSymbol]);
            }
        }
      }
    };
    const subscription = EventBus.on(KeyboardEvents.Types.INPUT_COMPLETE, handleInputComplete);
    return () => {
      subscription.unsubscribe();
    };
  }, [isVisible, symbols]);

  // 监听SYMBOLLIST_CHANGED事件，处理导入备选品种
  useEffect(() => {
    const handleSymbolListChanged = (payload: MarketEvents.SymbolListChanged) => {
      console.log('[StrategyEditor] 收到列表变化事件:', payload);

      // 当tag=2时，表示是发送给策略编辑器的列表
      if (payload.tag === 2) {
        // 处理导入的品种列表
        let symbolList: MarketEvents.SymbolListItem[] = [];

        if (Array.isArray(payload.theList)) {
          symbolList = payload.theList;
        } else if (typeof payload.theList === 'string') {
          try {
            symbolList = JSON.parse(payload.theList);
          } catch (e) {
            console.error('[StrategyEditor] 解析品种列表字符串失败:', e);
          }
        }

        console.log('[StrategyEditor] 导入品种列表:', symbolList);

        // 更新导入对话框状态
        setImportSymbolList(symbolList);
        setImportListName(payload.symbolListName);
        setSelectedImportSymbols([]); // 重置选中状态
        setImportModalVisible(true);
      }
    };

    const subscription = EventBus.on(MarketEvents.Types.SYMBOLLIST_CHANGED, handleSymbolListChanged);
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const handleRemoveSymbol = (symbolToRemove: string) => {
    setSymbols(prevSymbols => prevSymbols.filter(s => s !== symbolToRemove));
  };

  // 打开股票池管理面板，导入备选品种
  const handleOpenStockPoolManager = () => {
    console.log('[StrategyEditor] 打开股票池管理面板，导入备选品种');
    EventBus.emit(MarketEvents.Types.SHOW_STOCK_POOL_MANAGER, {
      visible: true,
      currentListName: '',
      tag: 2
    });
  };

  // 处理导入对话框的确认按钮
  const handleImportConfirm = () => {
    if (selectedImportSymbols.length === 0) {
      message.info('请至少选择一个品种');
      return;
    }

    // 从导入列表中筛选出选中的品种
    const selectedItems = importSymbolList.filter(item =>
      selectedImportSymbols.includes(item.symbol)
    );

    // 将选中的品种添加到策略的备选品种列表中
    const newSymbols = [...symbols];
    let addedCount = 0;

    selectedItems.forEach(item => {
      // 将品种代码中的 STOCK 转换为 STK
      const convertedSymbol = item.symbol.replace('.STOCK.', '.STK.');

      if (!newSymbols.includes(convertedSymbol)) {
        newSymbols.push(convertedSymbol);
        addedCount++;
        console.log(`[StrategyEditor] 导入品种: ${item.symbol} -> ${convertedSymbol}`);
      }
    });

    setSymbols(newSymbols);
    setImportModalVisible(false);

    message.success(`成功导入 ${addedCount} 个品种${addedCount < selectedImportSymbols.length ? '，其中有部分品种已存在' : ''}`);
  };

  // 处理导入对话框的取消按钮
  const handleImportCancel = () => {
    setImportModalVisible(false);
  };

  // 处理导入对话框中的全选/取消全选
  const handleSelectAllImportSymbols = (e: any) => {
    if (e.target.checked) {
      setSelectedImportSymbols(importSymbolList.map(item => item.symbol));
    } else {
      setSelectedImportSymbols([]);
    }
  };

  // 处理导入对话框中的单个品种选择
  const handleSelectImportSymbol = (symbol: string, checked: boolean) => {
    if (checked) {
      setSelectedImportSymbols(prev => [...prev, symbol]);
    } else {
      setSelectedImportSymbols(prev => prev.filter(s => s !== symbol));
    }
  };

  const handleAddRule = (type: 'buy' | 'sell') => {
    if (type === 'buy') {
      if (newBuyRule.trim() && !buyRulesFormulas.includes(newBuyRule.trim())) {
        setBuyRulesFormulas([...buyRulesFormulas, newBuyRule.trim()]);
        setNewBuyRule('');
      }
    } else {
      if (newSellRule.trim() && !sellRulesFormulas.includes(newSellRule.trim())) {
        setSellRulesFormulas([...sellRulesFormulas, newSellRule.trim()]);
        setNewSellRule('');
      }
    }
  };

  const handleRemoveRule = (type: 'buy' | 'sell', ruleToRemove: string) => {
    if (type === 'buy') {
      setBuyRulesFormulas(buyRulesFormulas.filter(rule => rule !== ruleToRemove));
    } else {
      setSellRulesFormulas(sellRulesFormulas.filter(rule => rule !== ruleToRemove));
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // 增加 tradingType、dataFreq、barCount、rebalanceInterval 到校验字段
      const values = await form.validateFields(['name', 'tradingType', 'topN', 'backtestRange', 'buyRulesCount', 'sellRulesCount', 'dataFreq', 'barCount', 'rebalanceInterval']);

      if (symbols.length === 0) {
         message.error('品种列表不能为空');
         setIsLoading(false);
         return;
      }
      const [startDate, endDate] = values.backtestRange || [null, null];
      if (startDate && endDate && startDate.isAfter(endDate)) {
        message.error('回测开始日期不能晚于结束日期');
        form.setFields([{ name: 'backtestRange', errors: ['开始日期不能晚于结束日期'] }]);
        setIsLoading(false);
        return;
      }
      if (!startDate) {
        message.error('必须有开始日期');
        form.setFields([{ name: 'backtestRange', errors: ['必须有开始日期'] }]);
        setIsLoading(false);
        return;
      }

      if (!currentStrategyId || currentYamlString === null) {
        message.error('内部错误：无法获取当前策略信息');
        setIsLoading(false);
        return;
      }

      let yamlObject: StrategyYaml = {};
      try {
        yamlObject = yaml.load(currentYamlString) as StrategyYaml;
      } catch (e) {
        console.error('[StrategyEditor] 保存时解析内存 YAML 失败:', e);
        message.error('保存失败：无法解析当前策略配置');
        setIsLoading(false);
        return;
      }

      yamlObject.strategy_name = values.name;
      yamlObject.universe = symbols;
      yamlObject.top_n = topN ?? undefined;
      // 保存 trading_type
      yamlObject.trading_type = tradingType;
      // 保存 data_freq 和 bar_count
      yamlObject.data_freq = dataFreq;
      yamlObject.bar_count = barCount;
      // 保存 rebalance_interval
      yamlObject.rebalance_interval = rebalanceInterval;

      if (!yamlObject.order_by) yamlObject.order_by = {};
      yamlObject.order_by.formula = orderByFormula || undefined;

      if (buyRulesFormulas.length > 0 || (buyRulesCount !== null && buyRulesCount !== 1)) {
        if (!yamlObject.buy_rules) yamlObject.buy_rules = {};
        yamlObject.buy_rules.formulas = buyRulesFormulas.length > 0 ? buyRulesFormulas : undefined;
        yamlObject.buy_rules.at_least_count = buyRulesCount ?? undefined;
      } else {
        delete yamlObject.buy_rules;
      }

      if (sellRulesFormulas.length > 0 || (sellRulesCount !== null && sellRulesCount !== 1)) {
        if (!yamlObject.sell_rules) yamlObject.sell_rules = {};
        yamlObject.sell_rules.formulas = sellRulesFormulas.length > 0 ? sellRulesFormulas : undefined;
        yamlObject.sell_rules.at_least_count = sellRulesCount ?? undefined;
      } else {
        delete yamlObject.sell_rules;
      }

      if (!yamlObject.backtest) yamlObject.backtest = {};
      const tmpstime = formatDateToYaml(startDate);
      yamlObject.backtest.stime = tmpstime || undefined;
      yamlObject.backtest.etime = formatDateToYaml(endDate) || undefined;

      if (yamlObject.order_by && Object.keys(yamlObject.order_by).length === 0) {
          delete yamlObject.order_by;
      }
      if (yamlObject.buy_rules && Object.keys(yamlObject.buy_rules).length === 0) {
          delete yamlObject.buy_rules;
      }
      if (yamlObject.sell_rules && Object.keys(yamlObject.sell_rules).length === 0) {
          delete yamlObject.sell_rules;
      }
      if (yamlObject.backtest && Object.keys(yamlObject.backtest).length === 0) {
          delete yamlObject.backtest;
      }

      const updatedYamlString = yaml.dump(yamlObject, { skipInvalid: true });
      console.log('[StrategyEditor] 更新后的 YAML:', updatedYamlString);

      EventBus.emit(StrategyEvents.Types.UPDATE_STRATEGY, {
        id: currentStrategyId,
        yaml: updatedYamlString,
      });

      setIsVisible(false);
      form.resetFields();
      setSymbols([]);
      setCurrentYamlString(null);
      setOrderByFormula('');
      setTopN(1);
      setBuyRulesFormulas([]);
      setBuyRulesCount(1);
      setSellRulesFormulas([]);
      setSellRulesCount(1);
      setNewBuyRule('');
      setNewSellRule('');
      // 重置 trading_type 状态
      setTradingType('etf');
      // 重置 data_freq 和 bar_count 状态
      setDataFreq('day');
      setBarCount(50);
      // 重置 rebalance_interval 状态
      setRebalanceInterval('daily');

    } catch (errorInfo) {
      console.log('[StrategyEditor] 表单校验失败:', errorInfo);
      message.error('请检查输入项是否正确');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setIsVisible(false);
    form.resetFields();
    setSymbols([]);
    setCurrentYamlString(null);
    setOrderByFormula('');
    setTopN(1);
    setBuyRulesFormulas([]);
    setBuyRulesCount(1);
    setSellRulesFormulas([]);
    setSellRulesCount(1);
    setNewBuyRule('');
    setNewSellRule('');
    // 重置 trading_type 状态
    setTradingType('etf');
    // 重置 data_freq 和 bar_count 状态
    setDataFreq('day');
    setBarCount(50);
    // 重置 rebalance_interval 状态
    setRebalanceInterval('daily');
  };

  const renderRuleList = (type: 'buy' | 'sell') => {
    const formulas = type === 'buy' ? buyRulesFormulas : sellRulesFormulas;
    const newRuleValue = type === 'buy' ? newBuyRule : newSellRule;
    const setNewRuleValue = type === 'buy' ? setNewBuyRule : setNewSellRule;
    const countValue = type === 'buy' ? buyRulesCount : sellRulesCount;
    const setCountValue = type === 'buy' ? setBuyRulesCount : setSellRulesCount;
    const formItemName = type === 'buy' ? 'buyRulesCount' : 'sellRulesCount';

    return (
      <>
        <Form.Item
          name={formItemName}
          label="最少满足条件数"
          rules={[{ type: 'number', min: 1, message: '至少为 1' }]}
          initialValue={countValue}
          style={{ marginBottom: '8px' }} // 减小下方间距
        >
          <InputNumber min={1} style={{ width: '100px' }} onChange={(value) => setCountValue(value)} />
        </Form.Item>
        <List
          // --- 应用样式到 List header ---
          header={<div style={styles.ruleListHeader}>规则公式列表 ({formulas.length})</div>}
          bordered
          dataSource={formulas}
          renderItem={item => (
            // --- 应用样式到 List.Item ---
            <List.Item
              style={styles.ruleListItem}
              actions={[
                <Button danger type="text" icon={<CloseOutlined />} onClick={() => handleRemoveRule(type, item)} size="small" />
              ]}
            >
              {/* --- 应用样式到内部 TextArea --- */}
              <Input.TextArea readOnly value={item} autoSize={{ minRows: 1, maxRows: 3 }} style={styles.ruleTextArea} />
            </List.Item>
          )}
          style={{ marginBottom: '16px' }}
          locale={{ emptyText: '暂无规则' }}
          size="small" // 尝试使用 antd 的 size 属性减小整体尺寸
        />
        <Input.Group compact>
          <Input.TextArea
            style={{ width: 'calc(100% - 60px)' }}
            placeholder="输入新的规则公式..."
            value={newRuleValue}
            onChange={(e) => setNewRuleValue(e.target.value)}
            autoSize={{ minRows: 1, maxRows: 3 }}
            size="small" // 配合 List size
          />
          <Button icon={<PlusOutlined />} onClick={() => handleAddRule(type)} style={{ width: '60px' }} size="small" />
        </Input.Group>
      </>
    );
  };

  return (
    <Modal
      title="编辑策略"
      open={isVisible}
      onOk={handleSave}
      onCancel={handleCancel}
      confirmLoading={isLoading}
      destroyOnClose
      maskClosable={false}
      width={800}
      zIndex={1000} // 设置较低的z-index，确保StockPoolWidget可以显示在上方
    >
      {isVisible && (
        <Form
          form={form}
          layout="vertical"
          name="strategy_editor_form"
        >
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                name="name"
                label="策略名称"
                rules={[{ required: true, message: '请输入策略名称' }]}
              >
                <Input placeholder="请输入策略名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              {/* 新增交易品类选择 */}
              <Form.Item
                name="tradingType"
                label="交易品类"
                rules={[{ required: true, message: '请选择交易品类' }]}
              >
                <Select
                  placeholder="选择交易品类"
                  onChange={(value) => setTradingType(value as TradingType)}
                >
                  <Select.Option value="stock">股票</Select.Option>
                  <Select.Option value="etf">ETF</Select.Option>
                  <Select.Option value="option">合约</Select.Option>
                  <Select.Option value="crypto">加密货币</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            {/* 调整布局，将 TopN 移到下面，给 Tabs 更多空间 */}
            {/* <Col span={8}>
              <Form.Item
                name="topN"
                label="一次持仓数量 (Top N)"
                rules={[{ required: true, message: '请输入持仓数量' }, { type: 'number', min: 1, message: '至少为 1' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} onChange={(value) => setTopN(value)} />
              </Form.Item>
            </Col> */}
          </Row>

          {/* --- 修改：调整 Row 和 Col 结构以控制 Tabs 高度 --- */}
          <Row gutter={16} style={{ marginBottom: '16px' }}>
            <Col span={10} style={styles.tabsContainer}>
              {/* --- 应用样式到 Tabs --- */}
              <Tabs defaultActiveKey="order" style={styles.tabs} tabBarStyle={{ marginBottom: '8px' }}>
                <TabPane tab="因子排序规则" key="order">
                  <Form.Item label="排序公式">
                    <Input.TextArea
                      rows={4}
                      placeholder="输入因子排序公式..."
                      value={orderByFormula}
                      onChange={(e) => setOrderByFormula(e.target.value)}
                    />
                  </Form.Item>
                  {/* 将 TopN 移到因子排序规则下方 */}
                  <Form.Item
                    name="topN"
                    label="一次持仓数量 (Top N)"
                    rules={[{ required: true, message: '请输入持仓数量' }, { type: 'number', min: 1, message: '至少为 1' }]}
                  >
                    <InputNumber min={1} style={{ width: '100%' }} onChange={(value) => setTopN(value)} />
                  </Form.Item>
                </TabPane>
                <TabPane tab="买入规则" key="buy">
                  {renderRuleList('buy')}
                </TabPane>
                <TabPane tab="卖出规则" key="sell">
                  {renderRuleList('sell')}
                </TabPane>
              </Tabs>
            </Col>
            <Col span={14}>
              {/* Container for positioning */}
              <div style={{ position: 'relative' }}>
                <div style={{ position: 'absolute', top: 0, right: 0, zIndex: 1 }}><KeyboardCoder inline /></div> {/* Positioned KeyboardCoder */}
                <Row style={{ marginBottom: '8px' }} align="middle">
                  <Col style={{ width: '120px', fontWeight: 500 }}>
                    已选品种 ({symbols.length}):
                  </Col>
                  <Col>
                    <Button
                      type="primary"
                      size="small"
                      onClick={handleOpenStockPoolManager}
                    >
                      导入品种
                    </Button>
                  </Col>
                </Row>
                <div style={{ height: '350px', overflowY: 'auto', border: '1px solid #d9d9d9', borderRadius: '4px', padding: '8px' }}>
                  {symbols.length > 0 ? (
                    <List
                      dataSource={symbols}
                      renderItem={item => (
                        <List.Item style={{ padding: '4px 0' }} // 稍微调整品种列表项间距
                          actions={[
                            <Button
                              type="text"
                              danger
                              size="small"
                              icon={<CloseOutlined />}
                              onClick={() => handleRemoveSymbol(item)}
                            />
                          ]}
                        >
                          {item}
                        </List.Item>
                      )}
                      size="small" // 品种列表也用 small
                    />
                  ) : (
                    <div style={{ textAlign: 'center', color: '#aaa', paddingTop: '20px' }}>暂无品种，请使用上方键盘输入添加</div>
                  )}
                </div>
              </div>
            </Col>
          </Row>
          {/* --- 调整结束 --- */}

          {/* 数据周期、K线数量和调仓间隔配置 */}
          <Row gutter={16} style={{ marginBottom: '16px' }}>
            <Col span={8}>
              <Form.Item
                name="dataFreq"
                label="数据周期"
                rules={[{ required: true, message: '请选择数据周期' }]}
              >
                <Select
                  placeholder="选择数据周期"
                  onChange={(value) => setDataFreq(value)}
                >
                  <Select.Option value="1m">1分钟</Select.Option>
                  <Select.Option value="5m">5分钟</Select.Option>
                  <Select.Option value="day">日线</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="barCount"
                label="K线数量"
                rules={[{ required: true, message: '请输入K线数量' }, { type: 'number', min: 10, message: '至少为 10' }]}
                tooltip="计算因子所需的K线数量，建议比公式中最大周期多10-20根"
              >
                <InputNumber
                  min={10}
                  max={1000}
                  style={{ width: '100%' }}
                  onChange={(value) => setBarCount(value || 50)}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="rebalanceInterval"
                label="调仓间隔"
                rules={[{ required: true, message: '请选择调仓间隔' }]}
              >
                <Select
                  placeholder="选择调仓间隔"
                  onChange={(value) => setRebalanceInterval(value)}
                >
                  <Select.Option value="5m">每5分钟</Select.Option>
                  <Select.Option value="15m">每15分钟</Select.Option>
                  <Select.Option value="1h">每小时</Select.Option>
                  <Select.Option value="daily">每日</Select.Option>
                  <Select.Option value="weekly">每周</Select.Option>
                  <Select.Option value="monthly">每月</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="backtestRange"
            label="回测范围"
          >
            <RangePicker style={{ width: '100%' }} showTime />
          </Form.Item>
        </Form>
      )}

      {/* 导入备选品种对话框 */}
      <Modal
        title={`导入备选品种 - ${importListName}`}
        open={importModalVisible}
        onOk={handleImportConfirm}
        onCancel={handleImportCancel}
        width={700}
        destroyOnClose
        zIndex={1001} // 设置比主对话框更高的z-index，确保它显示在主对话框上方
      >
        <div style={{ marginBottom: '10px' }}>
          <Checkbox
            onChange={handleSelectAllImportSymbols}
            checked={selectedImportSymbols.length === importSymbolList.length && importSymbolList.length > 0}
            indeterminate={selectedImportSymbols.length > 0 && selectedImportSymbols.length < importSymbolList.length}
          >
            全选/取消全选
          </Checkbox>
          <span style={{ marginLeft: '10px', color: '#999' }}>
            已选择 {selectedImportSymbols.length}/{importSymbolList.length} 个品种
          </span>
        </div>

        <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #f0f0f0', padding: '10px' }}>
          <List
            dataSource={importSymbolList}
            renderItem={item => (
              <List.Item style={{ padding: '4px 0' }}>
                <Checkbox
                  checked={selectedImportSymbols.includes(item.symbol)}
                  onChange={(e) => handleSelectImportSymbol(item.symbol, e.target.checked)}
                >
                  <span style={{ fontWeight: 'bold', marginRight: '8px' }}>{item.name}</span>
                  <span style={{ color: '#999' }}>{item.symbol.split('.').slice(2).join('.')}</span>
                </Checkbox>
              </List.Item>
            )}
            locale={{ emptyText: '暂无可导入的品种' }}
            size="small"
          />
        </div>
      </Modal>
    </Modal>
  );
};

export default StrategyEditor;