import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Drawer, Spin, Result, Typography, Descriptions, Table, Tag, Space, Divider, Empty } from 'antd';
import { Line as G2PlotLine } from '@antv/g2plot';
import { EventBus } from '@/events/eventBus';
import { StrategyEvents } from '@/events/events';
import { StrategyDetails, TradeRecord, EquityPoint } from '@/shared_types/strategy';
import styles from './index.module.less';
import { format } from 'date-fns'; // For date formatting
import type { TableColumnsType } from 'antd';
import yaml from 'js-yaml'; // Import YAML parser

const { Title, Paragraph, Text } = Typography;

// Helper to format numbers, handling potential null/undefined and 'inf'
const formatNumber = (value: number | string | null | undefined, decimals = 2, suffix = '') => {
  if (value === null || value === undefined) return '--';
  if (typeof value === 'string' && value.toLowerCase() === 'inf') return '∞';
  const num = Number(value);
  if (isNaN(num)) return '--';
  return `${num.toFixed(decimals)}${suffix}`;
};

// Define an interface for the parsed YAML structure (adjust based on actual YAML keys)
interface ParsedStrategyConfig {
  strategy_id?: string;
  strategy_name?: string;
  universe?: string[];
  data_freq?: string;
  // Add other fields from YAML if needed
}

const StrategyDetailView: React.FC = () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [strategyDetails, setStrategyDetails] = useState<StrategyDetails | null>(null);
  // Ref to track the ID of the latest request
  const currentStrategyIdRef = useRef<string | null>(null);

  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<G2PlotLine | null>(null);

  // Add useMemo to parse YAML when strategyDetails changes
  const parsedConfig = useMemo<ParsedStrategyConfig | null>(() => {
    console.log('[策略详情] useMemo: 尝试解析 YAML');
    if (strategyDetails?.strategy_config_yaml) {
      try {
        const parsed = yaml.load(strategyDetails.strategy_config_yaml);
        // Basic validation if it parsed into an object
        if (typeof parsed === 'object' && parsed !== null) {
             console.log("[策略详情] useMemo: YAML 解析成功:", parsed);
            return parsed as ParsedStrategyConfig;
        }
        console.warn("[策略详情] useMemo: YAML 解析结果不是对象:", parsed);
        return null;
      } catch (e) {
        console.error("[策略详情] useMemo: YAML 解析失败:", e);
        return null;
      }
    }
    console.log('[策略详情] useMemo: 没有 YAML 内容可解析');
    return null;
  }, [strategyDetails]);

  // Effect to handle showing the drawer and fetching data
  useEffect(() => {
    console.log("[策略详情] useEffect[]: 运行，注册 SHOW_STRATEGY_DETAILS 监听器");

    const showHandler = (payload: StrategyEvents.ShowStrategyDetailsPayload) => {
      // strategyId is the ID for this specific request/event
      const { strategyId } = payload;
      console.log(`[策略详情] showHandler: 收到显示事件, strategyId=${strategyId}`);

      // Immediately update the ref to mark this ID as the latest request
      currentStrategyIdRef.current = strategyId;
      console.log(`[策略详情] showHandler: currentStrategyIdRef updated to: ${currentStrategyIdRef.current}`);

      // Set state to show the drawer and loading indicator
      setVisible(true);
      setLoading(true);
      setError(null);
      setStrategyDetails(null); // Clear previous details
      console.log(`[策略详情] showHandler: 状态已设置: visible=true, loading=true, strategyDetails=null`);

      console.log(`[策略详情] showHandler: 触发 GET_STRATEGY_DETAILS 事件, strategyId=${strategyId}`);
      EventBus.emit(StrategyEvents.Types.GET_STRATEGY_DETAILS, {
        strategyId, // Pass the ID for this specific fetch request
        callback: (success, details, message) => {
          // Inside the callback, check if the ID for *this request* (strategyId from closure)
          // matches the ID of the *latest request* (currentStrategyIdRef.current).
          // This prevents updates from stale/old requests if the user quickly clicks another strategy.
          console.log(`[策略详情] 获取详情回调: success=${success}, requestStrategyId=${strategyId}, latestRequestedId=${currentStrategyIdRef.current}`);

          if (strategyId === currentStrategyIdRef.current) {
            console.log('[策略详情] 回调 ID 匹配 (Ref)，准备更新状态...');
            // Only update state if the fetched data belongs to the *latest* request
            setLoading(false);
            if (success && details) {
              console.log('[策略详情] 调用 setStrategyDetails with details:', details?.name);
              setStrategyDetails(details);
              console.log('[策略详情] setStrategyDetails 已调用');
            } else {
              console.error(`[策略详情] 获取详情失败或无数据，设置 error: ${message}`);
              setError(message || '加载策略详情失败');
            }
          } else {
             console.warn(`[策略详情] 回调 ID 不匹配 (Ref) (${strategyId} !== ${currentStrategyIdRef.current})，跳过状态更新。此数据已过时。`);
          }
        },
      });
    };

    console.log("[策略详情] 进行初始化。。。", );

    // Subscribe to the show event
    const subscription = EventBus.on(StrategyEvents.Types.SHOW_STRATEGY_DETAILS, showHandler);

    // Cleanup subscription on unmount
    return () => {
      console.log("[策略详情] useEffect[] cleanup: 组件卸载，取消 SHOW_STRATEGY_DETAILS 监听器。");
      subscription.unsubscribe();
    };
  }, []); // Keep dependency array empty, the ref correctly handles tracking the latest ID

  const handleClose = () => {
    console.log('[策略详情] handleClose: 关闭 Drawer，重置状态');
    setVisible(false);
    setLoading(false);
    setError(null);
    setStrategyDetails(null);
    currentStrategyIdRef.current = null; // Clear the ref on close
    // Optionally emit hide event
    EventBus.emit(StrategyEvents.Types.HIDE_STRATEGY_DETAILS, undefined);
  };

  // G2Plot Chart Configuration
  const chartConfigBase = {
    xField: 'date',
    yField: 'equity',
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
        // formatter: (text: string) => text.substring(5) // Keep MM-DD format
      },
      tickCount: 6,
    },
    yAxis: {
      label: {
        formatter: (v: number | string) => {
            const num = Number(v);
            if (isNaN(num)) return String(v);
            if (Math.abs(num) >= 1000000) return `${(num / 1000000).toFixed(1)}m`;
            if (Math.abs(num) >= 1000) return `${(num / 1000).toFixed(0)}k`;
            return `${num.toFixed(0)}`;
        },
      },
      min: null,
      max: null,
    },
    tooltip: {
        formatter: (datum: any) => ({ name: '净值', value: datum.equity?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || '--' })
    },
    smooth: true,
    lineStyle: { lineWidth: 2 },
    height: 300,
  };

  // Effect to manage G2Plot Chart Instance
  useEffect(() => {
    console.log(`[策略详情] chart useEffect[visible, strategyDetails]: visible=${visible}, strategyDetails is null?=${strategyDetails === null}`);
    const chartData = strategyDetails?.equity_curve_data || [];
    const canRenderChart = visible && chartContainerRef.current && chartData.length > 1;
    console.log(`[策略详情] chart useEffect: canRenderChart=${canRenderChart} (visible=${visible}, ref=${!!chartContainerRef.current}, dataLength=${chartData.length})`);

    if (canRenderChart) {
      if (!chartInstanceRef.current) {
        console.log('[策略详情] G2Plot: 创建图表实例');
        chartInstanceRef.current = new G2PlotLine(chartContainerRef.current, {
          ...chartConfigBase,
          data: chartData,
        });
        chartInstanceRef.current.render();
      } else {
        console.log('[策略详情] G2Plot: 更新图表实例数据');
        chartInstanceRef.current.changeData(chartData);
      }
    } else if (chartInstanceRef.current) {
        console.log('[策略详情] G2Plot: 销毁图表实例 (不可见或无数据)');
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
    }

    // Cleanup chart instance on component unmount or visibility change to false
    return () => {
      if (chartInstanceRef.current && !visible) {
         console.log('[策略详情] chart useEffect cleanup: 清理并销毁图表实例 (unmount/hide)');
         chartInstanceRef.current.destroy();
         chartInstanceRef.current = null;
      }
    };
  }, [visible, strategyDetails]);

  // Table Columns Configuration
  const tradeTableColumns: TableColumnsType<TradeRecord> = [
    {
      title: '时间',
      dataIndex: 'date',
      key: 'date',
      render: (text: string | null) => {
        if (!text) return '--';
        try {
            // Use parsed config's data_freq
            const freq = parsedConfig?.data_freq;
            const formatString = (freq && freq.toLowerCase() === 'day') ? 'yyyy-MM-dd' : 'yy-MM-dd HH:mm';
            return format(new Date(text), formatString);
        } catch (e) {
            console.error("Error formatting date:", text, e);
            return text; // Fallback to original text on error
        }
      },
      width: 130,
      fixed: 'left',
    },
    {
      title: '动作',
      dataIndex: 'action',
      key: 'action',
      render: (action: string) => (
        <Tag color={action === 'Buy' ? 'red' : action === 'Sell' ? 'green' : 'default'} style={{ margin: 0 }}>
          {action}
        </Tag>
      ),
      width: 65,
    },
    {
      title: '代码',
      dataIndex: 'symbol',
      key: 'symbol',
      width: 110,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      width: 120,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'right',
      render: (price: number) => formatNumber(price, 2),
      width: 90,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right',
      render: (amount: number) => formatNumber(amount, 0),
      width: 100,
    },
  ];

  // Render Content based on state
  const renderContent = () => {
    console.log('[策略详情] --- renderContent 开始 ---');
    console.log(`[策略详情] renderContent 状态: loading=${loading}, error=${error}, strategyDetails is null?=${strategyDetails === null}, name=${strategyDetails?.name}`);

    if (loading) {
      console.log('[策略详情] renderContent: 返回 Loading Spin');
      return <div className={styles.centerContent}><Spin size="large" tip="加载策略详情中..." /></div>;
    }
    if (error) {
      console.log('[策略详情] renderContent: 返回 Error Result:', error);
      return <div className={styles.centerContent}><Result status="error" title="加载失败" subTitle={error} /></div>;
    }
    if (!strategyDetails) {
      // If not loading and no error, but no details, show Empty state (could happen if fetch succeeds but returns no data after ID check)
      console.log('[策略详情] renderContent: 返回 Empty (strategyDetails is null, not loading)');
      return <div className={styles.centerContent}><Empty description="暂无策略详情数据或请求已过时" /></div>;
    }

    console.log('[策略详情] renderContent: strategyDetails 存在，准备解构和渲染详情...');

    // Destructure for easier access
    const {
        equity_curve_data, trade_history,
        annualized_return_pct, max_drawdown_pct, sharpe_ratio,
        total_trades, win_rate_pct, profit_factor,
        average_profit_per_trade, average_profit_winning_trade, average_loss_losing_trade,
        backtest_period_start, backtest_period_end
    } = strategyDetails;

    console.log(`[策略详情] renderContent: 解构后 equity_curve_data length = ${equity_curve_data?.length}, trade_history length = ${trade_history?.length}`);

    // Get desc and universe from parsed config
    const desc = parsedConfig?.strategy_name;
    const universe = parsedConfig?.universe;

    const hasChartData = equity_curve_data && equity_curve_data.length > 1;
    const hasTradeHistory = trade_history && trade_history.length > 0;

    console.log(`[策略详情] renderContent: 准备返回实际内容的 JSX (hasChartData=${hasChartData}, hasTradeHistory=${hasTradeHistory})`);

    return (
      <div className={styles.detailContent}>
        {/* 1. Header Info */}
        <Space direction="vertical" style={{ width: '100%' }} size="small">
          {desc && <Paragraph type="secondary" style={{ marginBottom: 8 }}>{desc}</Paragraph>}
          {universe && universe.length > 0 && (
            <Space wrap size={[4, 4]}>
                <Text strong>交易品种:</Text>
                {universe.map(item => <Tag key={item} style={{ margin: 0 }}>{item}</Tag>)}
            </Space>
          )}
          {strategyDetails.strategy_config_yaml && !parsedConfig && (
              <Text type="warning">无法解析策略配置信息。</Text>
          )}
        </Space>

        <Divider style={{ marginTop: 16, marginBottom: 16 }} />

        {/* 2. Equity Curve Chart */}
        <Title level={5} style={{ marginBottom: 16 }}>策略净值曲线</Title>
        <div ref={chartContainerRef} className={styles.chartContainer} style={{ height: `${chartConfigBase.height}px` }}>
            {!loading && !hasChartData && (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="无净值曲线数据" />
            )}
        </div>

        <Divider style={{ marginTop: 24, marginBottom: 16 }} />

        {/* 3. Performance Metrics */}
        <Title level={5} style={{ marginBottom: 16 }}>核心指标</Title>
        <Descriptions bordered size="small" column={{ xs: 1, sm: 2, md: 3 }}>
            <Descriptions.Item label="年化收益" labelStyle={{ width: '80px' }}>{formatNumber(annualized_return_pct, 1, '%')}</Descriptions.Item>
            <Descriptions.Item label="最大回撤" labelStyle={{ width: '80px' }}>{formatNumber(max_drawdown_pct, 1, '%')}</Descriptions.Item>
            <Descriptions.Item label="夏普比率" labelStyle={{ width: '80px' }}>{formatNumber(sharpe_ratio, 2)}</Descriptions.Item>
            <Descriptions.Item label="胜率" labelStyle={{ width: '80px' }}>{formatNumber(win_rate_pct, 1, '%')}</Descriptions.Item>
            <Descriptions.Item label="盈亏比">{formatNumber(profit_factor)}</Descriptions.Item>
            <Descriptions.Item label="总交易数">{total_trades ?? '--'}</Descriptions.Item>
            <Descriptions.Item label="平均收益">{formatNumber(average_profit_per_trade)}</Descriptions.Item>
            <Descriptions.Item label="平均盈利">{formatNumber(average_profit_winning_trade)}</Descriptions.Item>
            <Descriptions.Item label="平均亏损">{formatNumber(average_loss_losing_trade)}</Descriptions.Item>
            <Descriptions.Item label="开始日期">{backtest_period_start || '--'}</Descriptions.Item>
            <Descriptions.Item label="结束日期">{backtest_period_end || '--'}</Descriptions.Item>
        </Descriptions>

        <Divider style={{ marginTop: 24, marginBottom: 16 }} />

        {/* 4. Trade History */}
        <Title level={5} style={{ marginBottom: 16 }}>交易记录</Title>
        <Table
            columns={tradeTableColumns}
            dataSource={trade_history}
            rowKey={(record, index) => `${record.date}-${record.symbol}-${index}-${record.action}`} // More robust key
            pagination={false} // Disable pagination for simplicity, show all (up to 100)
            scroll={{ y: 300, x: 650 }} // Make table body scrollable vertically and horizontally if needed
            size="small"
            bordered
            locale={{ emptyText: hasTradeHistory ? '加载中...' : '无交易记录' }}
            className={styles.tradeTable}
        />
      </div>
    );
  };

  console.log(`[策略详情] 组件函数主体: 返回 Drawer (visible=${visible})`);

  // Use ref for title consistency, or strategyDetails name once loaded
  const titleStrategyId = currentStrategyIdRef.current;
  const displayTitle = parsedConfig?.strategy_name || strategyDetails?.name || titleStrategyId || '';

  return (
    <Drawer
      title={<Title level={4} style={{ margin: 0 }}>{`策略详情: ${displayTitle}`}</Title>}
      placement="right"
      onClose={handleClose}
      open={visible}
      width="85%" // Adjust width as needed
      closable={true}
      destroyOnClose={true} // Destroy component state when closed
      bodyStyle={{ padding: '20px' }}
      maskClosable={true} // Allow closing by clicking the mask
    >
      {renderContent()}
    </Drawer>
  );
};

export default StrategyDetailView;