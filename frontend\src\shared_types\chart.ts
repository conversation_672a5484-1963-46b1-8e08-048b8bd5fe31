import { Time } from 'lightweight-charts';
import { KLineInterval } from './market';
import { Symbol } from './market';

export interface ChartTimeRange {
    fromIndex: number;
    toIndex: number;
    fromX: number;
    toX: number;
    top: number;
    bottom: number;
  }
  
  export enum RangeSelectOperation {
    ANALYZE = 'analyze',
    ZOOM = 'zoom',
    EXPORT = 'export'
  } 

  // 定义点的接口
  export interface Point {
    time: Time; // 时间，通常是时间戳
    price: number; // 价格
  }

  export enum LinePointType {
    SinglePoint = 1,
    TwoPoints = 2, 
    ThreePoints = 3
  }

  export type LinePoints =
  | {
      pointsType: LinePointType.TwoPoints;
      startPoint: Point; // 起始点
      endPoint: Point;   // 结束点
    }
  | {
      pointsType: LinePointType.SinglePoint;
      point: Point; // 单个点
    }
  | {
      pointsType: LinePointType.ThreePoints;
      startPoint: Point; // 起始点
      endPoint: Point;   // 结束点
      posPoint: Point; // 位置点
    };

  // KLineChart画线数据结构
  export interface DrawingLineRecord {
    //userId: number;
    symbol: string;      // JSON字符串格式
    interval: string;    // K线周期
    overlays: any[];     // overlay对象数组
  }

  export interface DrawingLine {
    id: number;
    symbol: Symbol;
    interval: KLineInterval;
    overlays: any[];
  }

  // API请求响应
  export interface GetDrawingLinesResponse {
    success: boolean;
    data: DrawingLineRecord[];
  }

  export interface SaveDrawingLinesRequest {
    symbol: Symbol;
    interval: string;
    overlays: any[];     // overlay对象数组
  }

  export interface SaveDrawingLinesResponse {
    success: boolean;
  }

export interface RangeOperation {
  range: ChartTimeRange;
  operation: RangeSelectOperation;
  analysisParams?: {
    threshold: number;
    minBars: number;
  };
}

// 画线工具类型枚举
export enum DrawingToolType {
  NONE = '',
  SEGMENT = 'segment',
  RAY = 'ray',
  TREND_LINE = 'trendline',
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
  FIBONACCI = 'fibonacci',
  RECTANGLE = 'rectangle',
  MEASUREMENT = 'measurement',
  VERTICAL_SEGMENT = 'verticalSegment',
  HORIZONTAL_SEGMENT = 'horizontalSegment',
  PRICE_CHANNEL = 'priceChannelLine'
}
