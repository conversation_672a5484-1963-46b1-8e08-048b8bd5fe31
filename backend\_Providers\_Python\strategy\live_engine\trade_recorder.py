#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交易记录器
用于记录策略的交易记录
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

class TradeRecorder:
    """交易记录器"""
    
    def __init__(self, strategy_id: str):
        """
        初始化交易记录器
        
        Args:
            strategy_id: 策略ID
        """
        self.strategy_id = strategy_id
        self.logger = logging.getLogger(f'TradeRecorder_{strategy_id}')
        
        # 交易记录文件路径
        self.log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'live_logs')
        os.makedirs(self.log_dir, exist_ok=True)
        self.trades_file = os.path.join(self.log_dir, f'{strategy_id}_trades.json')
        
        # 加载现有交易记录
        self.trades = self._load_trades()
    
    def _load_trades(self) -> List[Dict[str, Any]]:
        """
        加载现有交易记录
        
        Returns:
            List[Dict[str, Any]]: 交易记录列表
        """
        if os.path.exists(self.trades_file):
            try:
                with open(self.trades_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"加载交易记录失败: {e}")
                return []
        else:
            return []
    
    def _save_trades(self):
        """保存交易记录"""
        try:
            with open(self.trades_file, 'w', encoding='utf-8') as f:
                json.dump(self.trades, f, ensure_ascii=False, indent=2)
            self.logger.info(f"交易记录已保存到 {self.trades_file}")
        except Exception as e:
            self.logger.error(f"保存交易记录失败: {e}")
    
    def record_trade(self, code: str, direction: str, price: float, volume: int, 
                     trade_time: Optional[str] = None) -> bool:
        """
        记录交易
        
        Args:
            code: 股票代码
            direction: 交易方向，'buy' 或 'sell'
            price: 交易价格
            volume: 交易数量
            trade_time: 交易时间，如果为None则使用当前时间
            
        Returns:
            bool: 是否成功记录
        """
        try:
            # 如果没有提供交易时间，使用当前时间
            if trade_time is None:
                trade_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 创建交易记录
            trade = {
                'time': trade_time,
                'code': code,
                'direction': direction,
                'price': price,
                'volume': volume,
                'amount': price * volume
            }
            
            # 添加到交易记录列表
            self.trades.append(trade)
            
            # 保存交易记录
            self._save_trades()
            
            self.logger.info(f"记录交易: {direction} {code} {volume}股 价格{price}")
            return True
        
        except Exception as e:
            self.logger.error(f"记录交易失败: {e}")
            return False
    
    def get_trades(self) -> List[Dict[str, Any]]:
        """
        获取所有交易记录
        
        Returns:
            List[Dict[str, Any]]: 交易记录列表
        """
        return self.trades
    
    def clear_trades(self) -> bool:
        """
        清空交易记录
        
        Returns:
            bool: 是否成功清空
        """
        try:
            self.trades = []
            self._save_trades()
            self.logger.info("交易记录已清空")
            return True
        except Exception as e:
            self.logger.error(f"清空交易记录失败: {e}")
            return False
