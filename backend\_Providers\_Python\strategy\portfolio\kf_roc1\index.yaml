# portfolio/config_kf_roc_example.yaml
# 基于卡尔曼滤波平滑 ROC(20) 的多因子策略示例配置

# --- 策略基础配置 ---
strategy_id: mf_kf_roc1 # 策略 ID (唯一)
strategy_name: 卡尔曼滤波1 # 策略实例 ID (唯一)
  
# --- 策略自定义参数 ---
# 这些参数会被传入策略类的 on_init 方法，通过 context.stra_get_cfg() 获取

trading_type: "etf" # 目前仅支持同一个市场内进行

universe:
  - SZSE.ETF.159934 # 黄金ETF
  - SSE.ETF.513100  # 纳指100ETF
  - SSE.ETF.510300  # 沪深300ETF
  - SZSE.ETF.159915 # 创业板ETF

# 基准线品种
benchmark: SSE.ETF.510050 # 上证50ETF

# 是否为股票模式
is_for_stk: true

# --- 排序规则 ---
# 注意：公式中的 roc 函数对应 factors/roc.py
#       如果需要卡尔曼滤波平滑，需要定义并使用 kf_roc 因子，例如 kf_roc(close, 20)
order_by:
  formula: "roc(close, 20)" # 使用原始 20 日 ROC 排序
  sort_direction: descending # 从高到低排序 (默认)

# --- 调仓与持仓 --- 
rebalance_interval: monthly # 调仓时间间隔: 'daily', 'weekly', 'monthly', '5d', '20d' etc.
top_n: 1                  # 持仓品种数量: 每次调仓后选择排名前 N 的标的
weighting_scheme: equal   # 权重分配模式: 'equal', 'signal_weighted' (对于 top_n=1 不重要)

# --- 因子计算相关 ---
data_freq: day   # 因子计算级别格式: 1m 5m 15m 30m 1h day week month
bar_count: 60    # 获取计算因子所需的K线数量 (roc(20) 需要至少 20+1=21根)

# --- 回测引擎特定配置 (运行回测时需要) ---
# 如果你使用 wtpy 的 run_backtest.py 或类似工具，这部分通常是必需的

  # 数据存储配置 (请根据你的实际路径修改)
data:
  path: ../../storage/ # <<<<====== 修改为你的数据存储根目录
  mode: csv

# 回测参数
backtest:
  stime: 202501010930 # 回测开始时间 (YYYYMMDDHHMM)
  etime:  # 回测结束时间 (YYYYMMDDHHMM)
  initial_capital: 500000 # 初始资金
  commission_rate: 0.0002 # 手续费率 (双边)
  slippage_rate: 0.0001   # 滑点比率 (双边)
  # price_match_mode: 1 # 价格撮合模式 (0:最新价, 1:次tick/bar开盘价, 默认为1)
  # fund_allocation: false # 是否使用资金分配模式 (多策略组合时用)

# --- 运行器配置 (可选，用于简化运行命令) ---
# runner:
#   name: WtBtRunner
#   sync_run: true # 是否同步执行 (看到日志输出)
#   output:
#     folder: ./outputs_bt/ # 输出文件夹
#     target_dir: mf_kf_roc_etf_monthly_top1 # 子目录，以策略ID命名