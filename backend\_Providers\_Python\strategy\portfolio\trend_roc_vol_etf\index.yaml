# Strategy: Trend + ROC + Volume Ratio ETF Rotation
# Description: Selects the top 1 ETF based on a combined score and rebalances daily.

strategy_id: "trend_roc_vr_etf_rotation" # 策略唯一标识，需与文件名一致，且不能包含特殊字符
strategy_name: "趋势+ROC+量比ETF轮动" # 策略显示名称

trading_type: "etf" # 目前仅支持同一个市场内进行

universe: # 品种池，格式：交易所代码：品种代码
  - SZSE.ETF.159509 # 纳指科技ETF
  - SSE.ETF.518880 # 黄金ETF
  - SZSE.ETF.159531 # 中证2000ETF
  - SSE.ETF.513100 # 纳指ETF
  - SSE.ETF.513520 # 日经ETF
  - SZSE.ETF.159857 # 光伏ETF
  - SSE.ETF.512100 # 中证1000ETF
  - SZSE.ETF.159667 # 工业母机ETF
  - SSE.ETF.513500 # 标普500ETF
  - SZSE.ETF.159915 # 创业板ETF
  - SSE.ETF.588000 # 科创50ETF
  - SZSE.ETF.159813 # 半导体ETF
  - SSE.ETF.562360 # 机器人ETF基金
  - SZSE.ETF.162719 # 石油LOF
  - SSE.ETF.513330 # 恒生互联网ETF

# 基准线品种
benchmark: SSE.ETF.510050 # 上证50ETF

is_for_stk: true

rebalance_interval: daily # 调仓周期: 每日

data_freq: day # 因子计算级别格式 1m 5m 15m 30m 1h day week month
# 计算所需K线数，需 >= max(26, 5, 10, 20) + 1 = 27
bar_count: 60    # 当前值 60 > 27，足够

# --- 排序规则 ---
order_by:
  # 复合评分公式: 趋势 * 0.4 + (ROC5+ROC10) * 0.2 + 量比 * 0.4 (隐式)
  # 注意: ma(volume,5)/ma(volume,20) 是量比(Volume Ratio)的一种近似计算
  formula: "trend_score(close, 26) * 0.4 + (roc(close, 5) + roc(close, 10)) * 0.2 + ma(volume, 5) / ma(volume, 20)"
  sort_direction: descending # 从高到低排序 (默认)

top_n: 1 # 持仓品种数量: 只选 1 只

weighting_scheme: equal # 权重分配模式 (因为 top_n=1，这个参数影响不大，但需配置)

# 数据存储配置 (请根据你的实际路径修改)
data:
  path: ../../storage/ # <<<<====== 修改为你的数据存储根目录
  mode: csv
# 回测参数
backtest:
  stime: 201501010930 # 回测开始时间 (YYYYMMDDHHMM)
  etime:  # 回测结束时间 (YYYYMMDDHHMM)
  initial_capital: 500000 # 初始资金
  commission_rate: 0.0002 # 手续费率 (双边)
  slippage_rate: 0.0001   # 滑点比率 (双边)