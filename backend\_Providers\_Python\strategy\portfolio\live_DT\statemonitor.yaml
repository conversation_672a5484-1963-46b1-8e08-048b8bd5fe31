# 状态监控配置文件
# 定义各个交易时段的监控规则

# 股票白天交易时段
SD0930:
    name: 股票白天交易
    schedule: true                  # 启用调度
    inittime: 915                   # 初始化时间 09:15
    closetime: 1505                 # 收盘时间 15:05
    proctime: 1520                  # 处理时间 15:20

# 股票夜盘交易时段（如果需要）
SN2100:
    name: 股票夜盘交易
    schedule: false                 # 暂不启用
    inittime: 2055                  # 初始化时间 20:55
    closetime: 230                  # 收盘时间 02:30
    proctime: 245                   # 处理时间 02:45

# ETF交易时段
ETF0930:
    name: ETF交易时段
    schedule: true                  # 启用调度
    inittime: 915                   # 初始化时间 09:15
    closetime: 1505                 # 收盘时间 15:05
    proctime: 1520                  # 处理时间 15:20

# 测试时段（用于开发调试）
TEST:
    name: 测试时段
    schedule: false                 # 默认不启用
    inittime: 800                   # 初始化时间 08:00
    closetime: 1800                 # 收盘时间 18:00
    proctime: 1805                  # 处理时间 18:05
