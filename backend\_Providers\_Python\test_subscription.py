#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试tdxserver的订阅功能
"""

import socketio
import time
import threading
import requests
import json

# 测试配置
TDX_SERVER_URL = "http://127.0.0.1:5004"
TDX_API_URL = "http://127.0.0.1:5003"

# 测试品种
TEST_SYMBOLS = [
    {"market": "sh", "symbol": "000001", "name": "上证指数"},
    {"market": "sz", "symbol": "000001", "name": "平安银行"},
    {"market": "sz", "symbol": "000002", "name": "万科A"},
]

class TestClient:
    def __init__(self, client_id):
        self.client_id = client_id
        self.sio = socketio.Client()
        self.connected = False
        self.received_data = []
        
        # 注册事件处理
        self.sio.on('connect', self.on_connect)
        self.sio.on('disconnect', self.on_disconnect)
        self.sio.on('connected', self.on_server_connected)
        self.sio.on('subscribed', self.on_subscribed)
        self.sio.on('unsubscribed', self.on_unsubscribed)
        self.sio.on('tick_data', self.on_tick_data)
        self.sio.on('error', self.on_error)
    
    def on_connect(self):
        print(f"[客户端{self.client_id}] 连接成功")
        self.connected = True
    
    def on_disconnect(self):
        print(f"[客户端{self.client_id}] 断开连接")
        self.connected = False
    
    def on_server_connected(self, data):
        print(f"[客户端{self.client_id}] 服务器确认连接: {data}")
    
    def on_subscribed(self, data):
        print(f"[客户端{self.client_id}] 订阅成功: {data}")
    
    def on_unsubscribed(self, data):
        print(f"[客户端{self.client_id}] 退订成功: {data}")
    
    def on_tick_data(self, data):
        symbol = data.get('symbol', 'unknown')
        tick_data = data.get('data', {})
        price = tick_data.get('price', 'N/A')
        timestamp = data.get('timestamp', 0)
        
        self.received_data.append(data)
        print(f"[客户端{self.client_id}] 收到tick数据: {symbol} 价格={price} 时间={timestamp}")
    
    def on_error(self, data):
        print(f"[客户端{self.client_id}] 错误: {data}")
    
    def connect(self):
        try:
            self.sio.connect(TDX_SERVER_URL)
            return True
        except Exception as e:
            print(f"[客户端{self.client_id}] 连接失败: {e}")
            return False
    
    def disconnect(self):
        if self.connected:
            self.sio.disconnect()
    
    def subscribe(self, market, symbol):
        if self.connected:
            self.sio.emit('subscribe_tick', {
                'market': market,
                'symbol': symbol
            })
    
    def unsubscribe(self, market, symbol):
        if self.connected:
            self.sio.emit('unsubscribe_tick', {
                'market': market,
                'symbol': symbol
            })

def get_subscription_stats():
    """获取订阅统计信息"""
    try:
        response = requests.get(f"{TDX_API_URL}/subscriptions", timeout=5)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"获取订阅统计失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"获取订阅统计异常: {e}")
        return None

def test_subscription_functionality():
    """测试订阅功能"""
    print("=" * 60)
    print("测试tdxserver订阅功能")
    print("=" * 60)
    
    # 检查服务器状态
    print("\n1. 检查服务器状态...")
    stats = get_subscription_stats()
    if stats:
        print(f"   服务器运行正常，当前订阅: {stats.get('total_symbols', 0)} 个品种")
    else:
        print("   服务器不可用，请确保tdxserver.py正在运行")
        return
    
    # 创建测试客户端
    print("\n2. 创建测试客户端...")
    clients = []
    for i in range(2):
        client = TestClient(i + 1)
        if client.connect():
            clients.append(client)
            time.sleep(0.5)
        else:
            print(f"   客户端{i + 1}连接失败")
    
    if not clients:
        print("   没有客户端连接成功")
        return
    
    print(f"   成功连接 {len(clients)} 个客户端")
    
    # 测试订阅
    print("\n3. 测试订阅功能...")
    for i, symbol_info in enumerate(TEST_SYMBOLS):
        market = symbol_info["market"]
        symbol = symbol_info["symbol"]
        name = symbol_info["name"]
        
        print(f"   订阅品种: {name} ({market}:{symbol})")
        
        # 让不同客户端订阅
        for j, client in enumerate(clients):
            if i == 0 or j == 0:  # 第一个品种所有客户端都订阅，其他品种只有第一个客户端订阅
                client.subscribe(market, symbol)
                time.sleep(0.2)
    
    # 等待订阅完成
    time.sleep(2)
    
    # 检查订阅状态
    print("\n4. 检查订阅状态...")
    stats = get_subscription_stats()
    if stats:
        print(f"   活跃品种: {len(stats.get('active_symbols', {}))}")
        print(f"   客户端数: {stats.get('total_clients', 0)}")
        for symbol, count in stats.get('active_symbols', {}).items():
            print(f"   {symbol}: {count} 个订阅")
    
    # 等待接收数据
    print("\n5. 等待接收tick数据...")
    print("   (等待10秒观察数据推送)")
    time.sleep(10)
    
    # 统计接收到的数据
    print("\n6. 统计接收数据...")
    for client in clients:
        print(f"   客户端{client.client_id}: 收到 {len(client.received_data)} 条tick数据")
    
    # 测试退订
    print("\n7. 测试退订功能...")
    if clients:
        client = clients[0]
        symbol_info = TEST_SYMBOLS[0]
        market = symbol_info["market"]
        symbol = symbol_info["symbol"]
        name = symbol_info["name"]
        
        print(f"   客户端1退订: {name} ({market}:{symbol})")
        client.unsubscribe(market, symbol)
        time.sleep(1)
    
    # 检查退订后状态
    print("\n8. 检查退订后状态...")
    stats = get_subscription_stats()
    if stats:
        print(f"   活跃品种: {len(stats.get('active_symbols', {}))}")
        for symbol, count in stats.get('active_symbols', {}).items():
            print(f"   {symbol}: {count} 个订阅")
    
    # 清理
    print("\n9. 清理连接...")
    for client in clients:
        client.disconnect()
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_subscription_functionality()
