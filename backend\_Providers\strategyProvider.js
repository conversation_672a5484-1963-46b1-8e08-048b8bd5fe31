const { sequelize } = require('../database');
const { Shapes: ShapesSchema, ShapeDetails: ShapeDetailsSchema, defineAssociations } = require('../shared_types/strategy');
const config = require('../config.json'); // 读取配置文件
const axios = require('axios'); // 用于发送HTTP请求

// ---- Strategy Server API Client Setup ----
// 从配置中获取策略服务地址和端口
const strategyServiceHost = config.strategy_service.host === '0.0.0.0' ? 'localhost' : config.strategy_service.host;
const strategyServicePort = config.strategy_service.port;
// 构建基础 URL (假设 Python API 都在 /api 路径下)
const STRATEGY_API_BASE_URL = `http://${strategyServiceHost}:${strategyServicePort}/`;

// 简单的axios实例，可以后续添加超时等配置
const apiClient = axios.create({
  baseURL: STRATEGY_API_BASE_URL,
  timeout: 10000, // 设置10秒超时
});

console.log(`[StrategyProvider] Strategy Server API Base URL: ${STRATEGY_API_BASE_URL}`);
// ---- End Setup ----

// 初始化模型
const Shapes = sequelize.define('shapes', ShapesSchema, {
  tableName: 'shapes',
  timestamps: false
});

const ShapeDetails = sequelize.define('shapeDetails', ShapeDetailsSchema, {
  tableName: 'shape_details',
  timestamps: false
});

// 设置模型关联
defineAssociations(Shapes, ShapeDetails);

// 同步模型到数据库
sequelize.sync().then(() => {
  console.log('Strategy models synchronized successfully');
}).catch(err => {
  console.error('Failed to sync strategy models:', err);
});

class StrategyProvider {
  /**
   * 保存形态配置
   * @param {string} name - 形态名称
   * @param {number} userId - 用户ID
   * @param {Array} klines - 形态K线数据
   * @param {Array} shapeDetailsList - 形态详细配置列表
   * @returns {Promise} 保存结果
   */
  async saveShapeConfig(name, userId, klines, shapeDetailsList) {
    const t = await sequelize.transaction();

    try {
      // 检查用户是否已有同名形态配置
      const existingShape = await Shapes.findOne({
        where: {
          name,
          userId
        }
      });

      if (existingShape) {
        throw new Error(`形态名称 "${name}" 已存在，请使用其他名称`);
      }

      // 保存主表数据
      const shape = await Shapes.create({
        name,
        userId,
        klineData: klines,
        createdAt: new Date()
      }, { transaction: t });

      // 保存从表数据
      const shapeId = shape.shapeId;
      const detailsPromises = shapeDetailsList.map(detail => {
        const detailData = {
          shapeId,
          indicatorType: detail.indicatorType,
          indicatorParam: detail.indicatorParam,
          lineName: detail.lineName,
          values: detail.values,
          weight: detail.weight
        };
        console.log('创建从表记录:', detailData);
        return ShapeDetails.create(detailData, { transaction: t });
      });

      await Promise.all(detailsPromises);
      await t.commit();

      return {
        success: true,
        data: {
          shapeId,
          name,
          userId,
          createdAt: shape.createdAt,
          klineData: klines
        }
      };
    } catch (error) {
      await t.rollback();
      console.error('保存形态配置失败:', error);
      throw error;
    }
  }

  /** 
   * 删除形态配置
   * @param {string} shapeConfigName - 形态配置名称
   * @param {number} userId - 用户ID
   * @returns {Promise} 删除结果
   */
  async deleteShapeConfig(shapeConfigName, userId) {
    const t = await sequelize.transaction();

    try {
      // 检查用户是否已有同名形态配置
      const existingShape = await Shapes.findOne({  
        where: {
          name: shapeConfigName,
          userId
        }
      });

      if (!existingShape) { 
        await t.rollback();
        return {
          success: false,
          error: `形态配置名称 "${shapeConfigName}" 不存在`
        };
      }

      // 删除形态配置
      await Shapes.destroy({ where: { shapeId: existingShape.shapeId }, transaction: t });  
      await ShapeDetails.destroy({ where: { shapeId: existingShape.shapeId }, transaction: t });

      await t.commit();

      return {
        success: true,  
        message: '形态配置删除成功'
      };
    } catch (error) {
      await t.rollback();
      console.error('删除形态配置失败:', error);
      return {
        success: false,
        error: error.message || '删除形态配置时发生未知错误'
      };
    }
  } 

  /**
   * 获取用户的所有形态配置
   * @param {number} userId - 用户ID
   * @returns {Promise} 形态配置列表
   */
  async getUserShapes(userId) {
    try {
      const userShapes = await Shapes.findAll({
        where: { userId },
        include: [{
          model: ShapeDetails,
          attributes: ['indicatorType', 'indicatorParam', 'lineName', 'values', 'weight']
        }]
      });

      return {
        success: true,
        data: userShapes
      };
    } catch (error) {
      console.error('获取用户形态配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取指定形态的详细配置
   * @param {string} shapeName - 形态名称
   * @returns {Promise} 形态详细配置
   */
  async getShapeDetails(shapeName) {
    try {
      console.log('[StrategyProvider] 开始获取形态详情，形态名称:', shapeName);
      
      const shape = await Shapes.findOne({
        where: { name: shapeName },
        include: [{
          model: ShapeDetails,
          attributes: ['indicatorType', 'indicatorParam', 'lineName', 'values', 'weight']
        }]
      });

      if (!shape) {
        throw new Error('形态配置不存在');
      }

      console.log('[StrategyProvider] 获取到形态数据:', {
        shapeId: shape.shapeId,
        name: shape.name,
        detailsCount: shape.shapeDetails?.length || 0
      });

      return {
        success: true,
        data: shape.shapeDetails
      };
    } catch (error) {
      console.error('获取形态详细配置失败:', error);
      throw error;
    }
  }

  // ---- New Methods for Strategy Server Interaction ----

  /**
   * 获取系统和用户自定义策略列表
   * @param {string[]} [customStrategyIds=[]] - 用户拥有的自定义策略名称列表
   * @returns {Promise<object>} 包含策略列表的结果对象 { success: boolean, data?: any, error?: string }
   */
  async listStrategies(customStrategyIds = []) {
    console.log(`[StrategyProvider] Calling Strategy Server to list strategies (including ${customStrategyIds.length} custom names)`);
    const apiUrl = '/strategy/list'; // Python endpoint accepts GET
    const payload = { custom_names: customStrategyIds }; // Send custom names in body

    try {
      const fullUrl = `${apiClient.defaults.baseURL}${apiUrl.substring(1)}`;
      console.log(`[StrategyProvider] Requesting Combined List URL: GET ${fullUrl}`);

      // --- 修改：始终使用 POST 请求，并在 body 中传递 custom_names --- 
      const response = await apiClient.get(`${apiUrl}?customStrategyIds=${customStrategyIds.join(',')}`);
      // --- 修改结束 ---

      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        console.log(`[StrategyProvider] Successfully received combined list of ${response.data.data.length} strategies.`);
        return { success: true, data: response.data.data };
      } else {
        const errorMsg = response.data?.error || 'Failed to list strategies (unexpected format from Python)';
        console.error('[StrategyProvider] Python server returned non-success or unexpected format:', response.data);
        return { success: false, error: errorMsg };
      }
    } catch (error) {
      console.error('[StrategyProvider] Error calling combined listStrategies API:', error.message);
      const errorMessage = error.response?.data?.error || error.message || 'Error connecting to strategy server';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取指定策略的回测结果
   * @param {string} strategyName - 策略名称 (系统策略名或自定义策略的UUID)
   * @returns {Promise<object>} 包含回测结果的数据对象 { success: boolean, data?: any, error?: string }
   */
  async getBacktestResult(strategyName) {
    console.log(`[StrategyProvider] Calling Strategy Server to get backtest result for: ${strategyName}`);
    if (!strategyName) {
      return { success: false, error: 'Strategy name is required' };
    }
    try {
      // 调用 Node.js Handler 对应的路径是 /api/strategy/getresult?name=...
      const apiUrl = `/strategy/backtest_result?name=${encodeURIComponent(strategyName)}`; // Node.js API Path
      const fullUrl = `${apiClient.defaults.baseURL}${apiUrl.substring(1)}`; // Construct full URL for logging
      console.log(`[StrategyProvider] Requesting URL (via Node.js Proxy): GET ${fullUrl}`); // Log the full URL
      const response = await apiClient.get(apiUrl);
      console.log(`[StrategyProvider] Successfully fetched backtest result for ${strategyName}.`);
      // 修正: 检查 response.data.success 并从 response.data.data 获取数据
      if (response.data && response.data.success) {
        return { success: true, data: response.data.data };
      } else {
        console.error(`[StrategyProvider] Strategy server returned non-success for getting result of ${strategyName}:`, response.data);
        return { success: false, error: response.data.error || `Failed to get result for ${strategyName} (unexpected format)` };
      }
    } catch (error) {
      console.error(`[StrategyProvider] Error calling getBacktestResult API for ${strategyName}:`, error.message);
      // 修正: 尝试从 error.response.data.error 获取错误信息
      const errorMessage = error.response?.data?.error || error.message || 'Error connecting to strategy server';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 运行策略回测
   * @param {string} strategyId - 策略名称 (系统策略名或自定义策略的UUID)
   * @param {string} [strategyYaml] - 可选，自定义策略的YAML内容
   * @returns {Promise<object>} 包含回测启动信息的结果对象 { success: boolean, message?: string, error?: string }
   */
  async runBacktest(strategyId, strategyYaml = '') {
    console.log(`[StrategyProvider] Calling Strategy Server to run backtest for: ${strategyId}`);
    if (!strategyId) {
      return { success: false, error: 'Strategy name is required' };
    }
    try {
      // 调用 Node.js Handler 对应的路径是 POST /api/strategy/backtest
      const apiUrl = '/strategy/run_backtest'; // Node.js API Path
      const payload = {
        id: strategyId, // Handler 期望 body 中有 name
        strategy_yaml: strategyYaml 
      };
      const fullUrl = `${apiClient.defaults.baseURL}${apiUrl.substring(1)}`; // Construct full URL for logging
      console.log(`[StrategyProvider] Requesting URL (via Node.js Proxy): POST ${fullUrl} with payload:`, payload); // Log URL and payload
      const response = await apiClient.post(apiUrl, payload);
      console.log(`[StrategyProvider] Successfully requested backtest for ${strategyId}.`);
      // 修正: 检查 response.data.success 并使用 response.data.message 或 response.data.data
      if (response.data && response.data.success) {
        // 注意: Python 端成功时会返回 { success: true, message: "...", data: {...回测结果...} }
        // Provider 这里暂时只返回 message，如果需要回测结果，可以修改为返回 response.data.data
        
        // --- 新增：打印接收到的回测数据 --- 
        // console.log(`[StrategyProvider] Received successful backtest data for ${strategyName}:`, response.data.data);
        // --- 新增结束 ---
        
        // --- 保持之前的逻辑：返回包含 message 和 data 的对象给 Handler --- 
        return { 
            success: true, 
            message: response.data.message || 'Backtest completed successfully', 
            data: response.data.data // 确保 data 被返回
        };
      } else {
        console.error(`[StrategyProvider] Strategy server returned non-success for running backtest ${strategyId}:`, response.data);
        return { success: false, error: response.data.error || `Failed to run backtest for ${strategyId} (unexpected format)` };
      }
    } catch (error) {
      console.error(`[StrategyProvider] Error calling runBacktest API for ${strategyId}:`, error.message);
      // 修正: 尝试从 error.response.data.error 获取错误信息
      const errorMessage = error.response?.data?.error || error.message || 'Error connecting to strategy server';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取指定策略的详细信息
   * @param {string} strategyName - 策略名称
   * @returns {Promise<object>} 包含策略详细信息的结果对象 { success: boolean, data?: any, error?: string, status?: number }
   */
  async getStrategyDetails(strategyName) {
    const encodedStrategyName = encodeURIComponent(strategyName); // Ensure strategy name is URL-safe
    // 假设 Python 服务的详细信息接口是 /strategy/details/{strategy_id} (无 /api 前缀)
    // 注意: Python 服务的真实路径可能需要根据实际情况调整
    const pythonApiUrl = `/strategy/details/${encodedStrategyName}`; // Python Service API Path (relative to base URL)
    const pythonFullUrl = `${STRATEGY_API_BASE_URL}${pythonApiUrl}`; // Direct Python URL (移除 /api 前缀)
    console.log(`[StrategyProvider] Directly Calling Python Service URL: GET ${pythonFullUrl}`);

    try {
      // 直接用 axios 调用 Python 服务
      // 注意: 使用了全局定义的 STRATEGY_API_BASE_URL 和 axios (或 apiClient)
      const response = await axios.get(pythonFullUrl, {
        timeout: apiClient.defaults.timeout // Use same timeout as apiClient
      });

      console.log(`[StrategyProvider] Successfully fetched strategy details for ${strategyName} from Python service.`);
      // 假设 Python 服务直接返回 { success: boolean, data: ..., error: ... } 结构或直接返回数据
      if (response.data && typeof response.data === 'object') {
         // 直接返回 Python 服务的响应体
         // 如果 Python 服务有 success 字段，直接返回
         if (response.data.success !== undefined) {
             // 确保返回 success, data/error 结构
             return { 
               success: response.data.success, 
               data: response.data.data, 
               error: response.data.error 
             };
         } else {
            // 如果 Python 服务不返回 success 字段，则认为成功并包装数据
            return { success: true, data: response.data };
         }
      } else {
         // 如果 Python 服务返回非 JSON 或意外格式 (例如纯文本), 也认为是成功的，但数据可能需要前端处理
         console.warn(`[StrategyProvider] Unexpected response format from Python service for ${strategyName}:`, response.data);
         return { success: true, data: response.data };
      }
    } catch (error) {
      console.error(`[StrategyProvider] Error calling getStrategyDetails API for ${strategyName}:`, error.message);
      const status = error.response?.status; // 获取 HTTP 状态码
      const errorMessage = error.response?.data?.error || // 尝试获取 Python 服务的结构化错误信息
                           (typeof error.response?.data === 'string' ? error.response.data : null) || // 尝试获取 Python 返回的纯文本错误
                           error.message ||
                           'Error connecting to strategy service or retrieving details';
      // 返回包含 status 的错误对象，方便 Handler 判断
      return { success: false, error: errorMessage, status: status }; 
    }
  }

  /**
   * 创建一个新的自定义策略
   * @param {string} strategyYaml - 新策略的YAML配置内容
   * @param {string} userId - 创建者用户ID
   * @returns {Promise<object>} 包含新策略名称的结果对象 { success: boolean, new_strategy_id?: string, error?: string }
   */
  async createStrategy(strategyYaml, userId) {
    console.log(`[StrategyProvider] Calling Strategy Server to create strategy for user: ${userId}`);
    if (!strategyYaml) {
      return { success: false, error: 'Strategy YAML content is required' };
    }
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    try {
      const apiUrl = '/strategy/create'; // Node.js API Path (assuming it mirrors Python)
      const payload = { strategy_yaml: strategyYaml, user_id: userId };
      const fullUrl = `${apiClient.defaults.baseURL}${apiUrl.substring(1)}`;
      console.log(`[StrategyProvider] Requesting URL: POST ${fullUrl}`);

      const response = await apiClient.post(apiUrl, payload);

      console.log('[StrategyProvider] Successfully requested strategy creation.');
      // Python 服务成功时返回 { success: true, new_strategy_id: "uuid" }
      if (response.data && response.data.success) {
        return { success: true, new_strategy_id: response.data.new_strategy_id };
      } else {
        console.error('[StrategyProvider] Strategy server returned non-success for strategy creation:', response.data);
        return { success: false, error: response.data.error || 'Failed to create strategy (unexpected format)' };
      }
    } catch (error) {
      console.error('[StrategyProvider] Error calling createStrategy API:', error.message);
      const errorMessage = error.response?.data?.error || error.message || 'Error connecting to strategy server';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 复制一个现有策略（系统或自定义）
   * @param {string} sourceStrategyId - 要复制的源策略名称
   * @param {string} userId - 执行复制操作的用户ID
   * @param {string} newName - 新策略的名称
   * @returns {Promise<object>} 包含新策略名称的结果对象 { success: boolean, new_strategy_id?: string, error?: string }
   */
  async copyStrategy(sourceStrategyId, userId, newName) {
    console.log(`[StrategyProvider] Calling Strategy Server to copy strategy: ${sourceStrategyId} for user: ${userId}`);
    if (!sourceStrategyId) {
      return { success: false, error: 'Source strategy name is required' };
    }
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    try {
      const apiUrl = '/strategy/copy'; // Node.js API Path (assuming it mirrors Python)
      const payload = { source_strategy_id: sourceStrategyId, user_id: userId, new_name: newName };
      const fullUrl = `${apiClient.defaults.baseURL}${apiUrl.substring(1)}`;
      console.log(`[StrategyProvider] Requesting URL: POST ${fullUrl}`);

      const response = await apiClient.post(apiUrl, payload);

      console.log(`[StrategyProvider] Successfully requested strategy copy for: ${sourceStrategyId}.`);
      // Python 服务成功时返回 { success: true, new_strategy_id: "uuid" }
      if (response.data && response.data.success) {
        return { success: true, new_strategy_id: response.data.new_strategy_id };
      } else {
        console.error(`[StrategyProvider] Strategy server returned non-success for copying strategy ${sourceStrategyId}:`, response.data);
        return { success: false, error: response.data.error || 'Failed to copy strategy (unexpected format)' };
      }
    } catch (error) {
      console.error(`[StrategyProvider] Error calling copyStrategy API for ${sourceStrategyId}:`, error.message);
      const errorMessage = error.response?.data?.error || error.message || 'Error connecting to strategy server';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 更新一个现有的自定义策略
   * @param {string} strategyId - 要更新的策略名称 (通常是UUID)
   * @param {string} strategyYaml - 新的策略YAML配置内容
   * @returns {Promise<object>} 包含操作结果的对象 { success: boolean, message?: string, error?: string }
   */
  async updateStrategy(strategyId, strategyYaml) {
    console.log(`[StrategyProvider] Calling Strategy Server to update strategy: ${strategyId}`);
    if (!strategyId) {
      return { success: false, error: 'Strategy name is required' };
    }
    if (!strategyYaml) {
      return { success: false, error: 'Strategy YAML content is required' };
    }

    try {
      const apiUrl = '/strategy/update'; // Node.js API Path (assuming it mirrors Python)
      const payload = { strategy_id: strategyId, strategy_yaml: strategyYaml };
      const fullUrl = `${apiClient.defaults.baseURL}${apiUrl.substring(1)}`;
      console.log(`[StrategyProvider] Requesting URL: PUT ${fullUrl}`);

      const response = await apiClient.put(apiUrl, payload);

      console.log(`[StrategyProvider] Successfully requested strategy update for: ${strategyId}.`);
      // Python 服务成功时返回 { success: true, message: "..." }
      if (response.data && response.data.success) {
        return { success: true, message: response.data.message || 'Strategy updated successfully' };
      } else {
        console.error(`[StrategyProvider] Strategy server returned non-success for updating strategy ${strategyId}:`, response.data);
        return { success: false, error: response.data.error || 'Failed to update strategy (unexpected format)' };
      }
    } catch (error) {
      console.error(`[StrategyProvider] Error calling updateStrategy API for ${strategyId}:`, error.message);
      const errorMessage = error.response?.data?.error || error.message || 'Error connecting to strategy server';
      return { success: false, error: errorMessage };
    }
  }
}

// 创建并导出实例
const strategyProvider = new StrategyProvider();
module.exports = { strategyProvider };
