<!--
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<!DOCTYPE html>
<html lang="en" >
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="keywords" content="kline time-line candlestick stock chart canvas k线 行情 蜡烛图 分时图 技术指标 图表"/>
    <meta name="description" content="shape test"/>
    <script type="text/javascript" src="../../../dist/klinecharts.min.js"></script>
    <script type="text/javascript" src="../../js/dataSource.js"></script>
    <link rel="stylesheet" type="text/css" href="../../css/chart.css"/>
    <title>Overlay override</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="chart"></div>
    <script>
      window.onload = function () {
        // Register overlay
        klinecharts.registerOverlay({
          name: 'circle',
          needDefaultPointFigure: true,
          needDefaultXAxisFigure: true,
          needDefaultYAxisFigure: true,
          totalStep: 3,
          createPointFigures: function ({ overlay, coordinates }) {
            if (coordinates.length === 2) {
              const xDis = Math.abs(coordinates[0].x - coordinates[1].x)
              const yDis = Math.abs(coordinates[0].y - coordinates[1].y)
              const radius = Math.sqrt(xDis * xDis + yDis * yDis)
              return [
                {
                  key: 'circle',
                  type: 'circle',
                  attrs: {
                    ...coordinates[0],
                    r: radius
                  },
                  styles: {
                    style: 'stroke_fill'
                  }
                },
                {
                  key: 'text',
                  type: 'text',
                  attrs: {
                    ...coordinates[0],
                    text: overlay.extendData
                  }
                }
              ]
            }
            return []
          }
        })
        
        var chart = klinecharts.init('chart')
        chart.applyNewData(generated())
        // Create overlay
        chart.createOverlay({
          name: 'circle',
          extendData: 'Override overlay',
          styles: { text: { color: 'rgba(100, 10, 200, .3)' } },
          onDrawEnd: function ({ overlay }) {
            // Listen to the completion of drawing and overwrite the attribute
            chart.overrideOverlay({
              id: overlay.id,
              lock: true,
              extendData: 'Update override overlay',
              styles: { color: 'rgba(10, 230, 100, .3)' }
            })
          }
        })
      }
    </script>
  </body>
</html>
