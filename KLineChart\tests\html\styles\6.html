<!--
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<!DOCTYPE html>
<html lang="en" >
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="keywords" content="kline time-line candlestick stock chart canvas k线 行情 蜡烛图 分时图 技术指标 图表"/>
    <meta name="description" content="shape test"/>
    <script type="text/javascript" src="../../../dist/klinecharts.min.js"></script>
    <script type="text/javascript" src="../../js/dataSource.js"></script>
    <link rel="stylesheet" type="text/css" href="../../css/chart.css"/>
    <title>Styles -- Tooltip</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="chart"></div>
    <script>
      window.onload = function () {
        var chart = klinecharts.init('chart', {
          styles: {
            candle: {
              tooltip: {
                showType: 'rect',
                custom: function (data) {
                  return [
                    { title: 'open', value: data.open },
                    { title: 'high', value: { text: data.high, color: 'red' } },
                    { title: { text: 'low', color: 'green' }, value: { text: data.low, color: 'green' } },
                    { title: { text: 'close', color: 'blue' }, value: data.close }
                  ]
                }
              }
            },
            indicator: {
              tooltip: {
                showType: 'rect'
              }
            }
          }
        })
        chart.createIndicator('MA', false, { id: 'candle_pane' })
        chart.createIndicator('EMA', true, { id: 'candle_pane' })
        chart.createIndicator('VOL')
        chart.createIndicator('MACD')
        chart.applyNewData(generated(), true)
      }
    </script>
  </body>
</html>