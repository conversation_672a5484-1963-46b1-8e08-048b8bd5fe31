.indicator-panel {
  .ant-card-body {
    padding: 8px;
    height: 100%;
  }

  .ant-tabs {
    height: 100%;

    .ant-tabs-content {
      height: calc(100% - 32px);
      margin-top: 8px;
    }

    .ant-tabs-tabpane {
      height: 100%;
    }

    .ant-tabs-nav {
      margin-bottom: 0;

      .ant-tabs-tab {
        padding: 4px 8px;
        background: #fafafa;

        &.ant-tabs-tab-active {
          background: #fff;
        }

        &:hover {
          color: var(--ant-primary-color);
        }
      }
    }
  }

  .indicator-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 8px;
    background: #fff;
    border-radius: 0 0 2px 2px;
  }

  .indicator-values {
    display: flex;
    gap: 16px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    color: var(--text-color);
    font-size: 12px;
  }

  .indicator-chart {
    flex: 1;
    min-height: 0;
  }
} 