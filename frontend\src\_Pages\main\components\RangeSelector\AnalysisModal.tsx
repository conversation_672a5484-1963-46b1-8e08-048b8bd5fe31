import React, { useEffect, useState, useRef } from 'react';
import { Modal, Checkbox, Input, Slider, message, Radio } from 'antd';
import { useAtom } from 'jotai';
import { mainIndicatorsAtom, subIndicatorsAtom, klineAtom } from '@/store/state';
import { IndicatorWrapper } from '@/_Modules/Indicators/IndicatorWrapper';
import { EventBus } from '@/events/eventBus';
import { MarketEvents } from '@/events/events';
import { ShapeConfigItem } from '@/shared_types/shape';
import { createChart, IChartApi, ColorType, Time } from 'lightweight-charts';
import { useTheme } from '@/models/useTheme';

interface AnalysisModalProps {
  visible: boolean;
  onOk: (shapes: ShapeConfigItem[], name: string) => void;
  onCancel: () => void;
  kLineCount: number;
  startIndex: number;
  endIndex: number;
}

const AnalysisModal: React.FC<AnalysisModalProps> = ({ visible, onOk, onCancel, kLineCount, startIndex, endIndex }) => {
  const [selectedIndicators, setSelectedShapeConfigs] = useState<ShapeConfigItem[]>([]);
  const [shapeName, setShapeName] = useState('');
  const [rangeKlines, setRangeKlines] = useState<any[]>([]);
  const { theme, isDarkMode } = useTheme();
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);

  const [mainIndicators] = useAtom(mainIndicatorsAtom);
  const [subIndicators] = useAtom(subIndicatorsAtom);
  const [kline] = useAtom(klineAtom);

  const indicators = [...mainIndicators, ...subIndicators];

  // 转换K线数据到图表库需要的格式
  const convertToChartData = (data: any[]): { time: Time; open: number; high: number; low: number; close: number; }[] => {
    return data.map(item => ({
      time: item.time as Time,
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close
    }));
  };

  // 专门监听对话框可见性变化，处理图表的创建和销毁
  useEffect(() => {
    if (visible) {
      console.log('对话框已打开，准备初始化图表');
      
      // 强制延迟一小段时间再初始化图表，确保Modal已完全展示
      const timer = setTimeout(() => {
        // 清理任何现有图表
        if (chartRef.current) {
          chartRef.current.remove();
          chartRef.current = null;
        }
        
        if (!chartContainerRef.current) return;
        
        console.log('开始创建图表实例，数据索引:', startIndex, endIndex);
        
        // 创建新的图表实例
        const container = chartContainerRef.current;
        const chart = createChart(container, {
          width: container.clientWidth,
          height: 250,
          layout: {
            background: { type: ColorType.Solid, color: isDarkMode ? '#1E1E1E' : '#FFFFFF' },
            textColor: isDarkMode ? '#D9D9D9' : '#191919',
            attributionLogo: false,
          },
          grid: {
            vertLines: { color: isDarkMode ? '#2B2B43' : '#E6E6E6' },
            horzLines: { color: isDarkMode ? '#2B2B43' : '#E6E6E6' },
          },
          timeScale: {
            borderColor: isDarkMode ? '#2B2B43' : '#E6E6E6',
          },
          rightPriceScale: {
            borderColor: isDarkMode ? '#2B2B43' : '#E6E6E6',
          },
        });
        
        chartRef.current = chart;
        
        // 添加K线图系列
        const candlestickSeries = chart.addCandlestickSeries({
          upColor: isDarkMode ? '#f23645' : '#ef5350',
          downColor: isDarkMode ? '#089981' : '#26a69a',
          borderUpColor: isDarkMode ? '#f23645' : '#ef5350',
          borderDownColor: isDarkMode ? '#089981' : '#26a69a',
          wickUpColor: isDarkMode ? '#f23645' : '#ef5350',
          wickDownColor: isDarkMode ? '#089981' : '#26a69a',
        });
        
        // 设置K线数据
        if (kline && kline.data && startIndex >= 0 && endIndex >= 0 && startIndex <= endIndex) {
          const selectedData = kline.data.slice(startIndex, endIndex + 1);
          console.log('选中的K线数据长度:', selectedData.length);
          
          if (selectedData.length > 0) {
            const chartData = convertToChartData(selectedData);
            candlestickSeries.setData(chartData);
            chart.timeScale().fitContent();
            
            // 准备区域K线数据用于保存
            const simplifiedKlines = selectedData.map(item => ({
              time: item.time,
              open: item.open,
              high: item.high,
              low: item.low,
              close: item.close
            }));
            setRangeKlines(simplifiedKlines);
            console.log('已准备区域K线数据:', simplifiedKlines.length, '条记录');
          }
        }
      }, 100);
      
      return () => clearTimeout(timer);
    } else {
      // 对话框关闭时，立即清理图表资源
      if (chartRef.current) {
        console.log('对话框关闭，清理图表资源');
        chartRef.current.remove();
        chartRef.current = null;
      }
    }
  }, [visible, kline, startIndex, endIndex, isDarkMode]);

  useEffect(() => {
    console.log('当前选择的K线数量:', kLineCount);
    if (visible && kLineCount < 10) {
      message.warning('选择的时间范围内至少需要10根K线。');
    }
  }, [visible, kLineCount]);

  const handleIndicatorChange = (indicatorWrapper: IndicatorWrapper, checked: boolean) => {
    const indicator = indicatorWrapper.indicator;

    // 获取当前选择的线
    const selectedLine = selectedIndicators.find(item => item.type === indicator.type)?.selectedLine || indicator.lines[0].name;

    if (checked) {
      // 从 IndicatorWrapper 获取指标当前选择线的所有值
      const allValues = indicatorWrapper.getValues()[selectedLine];
      console.log('指标所有值:', allValues);
      
      // 使用索引获取选中范围内的值
      const rangeValues = allValues.slice(startIndex, endIndex + 1);
      console.log('选中范围内的值:', rangeValues, '开始索引:', startIndex, '结束索引:', endIndex);
      
      // 归一化数值到 0-1 范围
      const minValue = Math.min(...rangeValues);
      const maxValue = Math.max(...rangeValues);
      console.log('最小值:', minValue, '最大值:', maxValue);

      const normalizedValues = rangeValues.map((value: number) => 
        (value - minValue) / (maxValue - minValue)
      );
      console.log('归一化后的值:', normalizedValues);

      // 计算新指标的权重
      const currentIndicators = [...selectedIndicators];
      
      let newWeight = 0;
      if (currentIndicators.length === 0) {
        // 如果是第一个指标，权重设为1
        newWeight = 1;
      } else {
        // 如果已有其他指标，则所有指标（包括新指标）权重平均分配
        newWeight = 1 / (currentIndicators.length + 1);
        // 调整现有指标的权重
        currentIndicators.forEach(ind => {
          ind.weight = newWeight;
        });
      }

      const newConfig: ShapeConfigItem = {
        type: indicator.type,
        selectedLine: selectedLine,
        params: indicatorWrapper.getParams() || {},
        weight: newWeight,
        values: normalizedValues,
      };

      setSelectedShapeConfigs([...currentIndicators, newConfig]);
    } else {
      // 删除指标时重新分配权重
      const remainingIndicators = selectedIndicators.filter(item => item.type !== indicator.type);
      
      if (remainingIndicators.length === 1) {
        // 如果只剩一个指标，设置其权重为1
        remainingIndicators[0].weight = 1;
      } else if (remainingIndicators.length > 1) {
        // 获取被删除指标的权重
        const deletedWeight = selectedIndicators.find(item => item.type === indicator.type)?.weight || 0;
        
        if (deletedWeight > 0) {
          // 只有当被删除的指标权重大于0时，才需要重新分配
          const totalRemainingWeight = remainingIndicators.reduce((sum, ind) => sum + ind.weight, 0);
          if (totalRemainingWeight > 0) {
            const scale = 1 / totalRemainingWeight;
            remainingIndicators.forEach(ind => {
              ind.weight = ind.weight * scale;
            });
          }
        }
      }

      setSelectedShapeConfigs(remainingIndicators);
    }
  };

  const handleLineChange = (indicatorIndex: number, lineName: string) => {
    if (indicatorIndex < 0 || indicatorIndex >= selectedIndicators.length) return;

    const newIndicators = [...selectedIndicators];
    newIndicators[indicatorIndex].selectedLine = lineName;
    setSelectedShapeConfigs(newIndicators);
  };

  const handleWeightChange = (index: number, newValue: number) => {
    const newIndicators = [...selectedIndicators];
    const oldValue = newIndicators[index].weight;
    const diff = newValue - oldValue;

    // 如果只有一个指标，直接设置为1
    if (newIndicators.length === 1) {
      newIndicators[index].weight = 1;
      setSelectedShapeConfigs(newIndicators);
      return;
    }

    // 计算其他指标的总权重
    const otherWeightsSum = newIndicators.reduce((sum, ind, i) =>
      i !== index ? sum + ind.weight : sum, 0);

    // 调整其他指标的权重，保持总和为1
    newIndicators[index].weight = newValue;
    const scale = (1 - newValue) / otherWeightsSum;

    newIndicators.forEach((ind, i) => {
      if (i !== index) {
        ind.weight = ind.weight * scale;
      }
    });

    setSelectedShapeConfigs(newIndicators);
  };

  return (
    <Modal
      title="分析选中区域"
      open={visible}
      onOk={() => {
        // 打印形态配置
        console.log('形态配置:', {
          shapes: selectedIndicators,
          name: shapeName,
        });

        // 触发保存事件
        EventBus.emit(MarketEvents.Types.SAVE_SHAPECONFIGS, {
          shapeConfig: {
            shapeConfigItems: selectedIndicators,
            name: shapeName,
            klineData: rangeKlines,
          },
        });
        onOk(selectedIndicators, shapeName);
      }}
      onCancel={onCancel}
      width={800}
    >
      {/* K线图显示区域 */}
      <div style={{ marginBottom: '20px' }}>
        <h4>选中区域K线图预览</h4>
        <div 
          ref={chartContainerRef} 
          style={{ 
            width: '100%', 
            height: '250px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            overflow: 'hidden'
          }}
        />
        <div style={{ 
          marginTop: '8px', 
          padding: '8px', 
          backgroundColor: '#f5f5f5', 
          border: '1px solid #e8e8e8',
          borderRadius: '4px',
          fontSize: '14px',
          display: 'flex',
          justifyContent: 'space-between'
        }}>
          <div><strong>K线数量:</strong> {kLineCount}</div>
          <div><strong>开始索引:</strong> {startIndex}</div>
          <div><strong>结束索引:</strong> {endIndex}</div>
          <div><strong>K线数据:</strong> {rangeKlines.length > 0 ? '已准备' : '未准备'}</div>
        </div>
      </div>

      <div style={{ display: 'flex' }}>
        <div style={{ flex: 1, marginRight: '20px' }}>
          <h4>可选指标</h4>
          {indicators.map((indicatorWrapper, index) => {
            const indicator = indicatorWrapper.indicator;
            // 创建更唯一的key，结合类型和索引
            const uniqueKey = `${indicator.type}_${index}`;

            return (
              <div key={uniqueKey} style={{ marginBottom: '10px' }}>
                <Checkbox
                  onChange={e => handleIndicatorChange(indicatorWrapper, e.target.checked)}
                >
                  {indicator.type}
                </Checkbox>
                <Radio.Group
                  value={selectedIndicators.find(item => item.type === indicator.type)?.selectedLine}
                  onChange={e => handleLineChange(indicators.indexOf(indicatorWrapper), e.target.value)}
                >
                  {indicator.lines.map((line, lineIndex) => (
                    <Radio key={`${line.name}_${lineIndex}`} value={line.name}>
                      {line.name}
                    </Radio>
                  ))}
                </Radio.Group>
                <div style={{ marginTop: '5px', marginLeft: '20px' }}>
                  {indicatorWrapper.getParams() && Object.entries(indicatorWrapper.getParams()).map(([key, value], paramIndex) => {
                    console.log(`参数: ${key} = ${value}`);
                    return (
                      <div key={`${key}_${paramIndex}`}>
                        <strong>{key}:</strong> {value.toString()}
                      </div>
                    );
                  })}
                </div>

                {/* k线走势图，仅针对有k线数据作为参数的指标 */}

              </div>
            );
          })}
        </div>
        <div style={{ flex: 1 }}>
          <h4>选中形态</h4>
          {selectedIndicators.map((item, index) => (
            <div key={item.type} style={{ marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
              <span>{item.type}</span>
              <Slider
                min={0}
                max={1}
                step={0.01}
                value={item.weight}
                onChange={value => handleWeightChange(index, value)}
                style={{ marginLeft: '10px', flex: 1 }}
              />
              <span style={{ marginLeft: '10px', minWidth: '40px', textAlign: 'right' }}>
                {(item.weight || 0.5).toFixed(2)}
              </span>
            </div>
          ))}
        </div>
      </div>
      <Input
        placeholder="形态组合名称"
        value={shapeName}
        onChange={e => setShapeName(e.target.value)}
        style={{ marginTop: '20px' }}
      />
    </Modal>
  );
};

export default AnalysisModal;