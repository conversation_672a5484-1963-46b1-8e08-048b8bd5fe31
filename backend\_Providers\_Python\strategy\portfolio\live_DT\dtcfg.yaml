# 中心数据引擎配置文件
# 为实盘交易提供统一的行情数据服务

basefiles:
    commodity: ../../common/stk_comms.json      # 品种信息文件
    contract: contracts.json                    # 合约信息文件（动态生成，位于当前目录）
    holiday: ../../common/holidays.json         # 节假日信息文件
    session: ../../common/stk_sessions.json     # 交易时段信息文件
    utf-8: true                                  # 使用UTF-8编码

# UDP广播器配置 - 用于在局域网内转发行情数据
broadcaster:
    active: true                    # 启用广播功能
    bport: 3997                     # UDP查询端口
    broadcast:
    -   host: ***************       # 广播地址
        port: 9001                  # 广播端口
        type: 2                     # 数据类型

# 共享内存转发配置 - 适合本机内进程间数据共享
shmcaster:
    active: true                    # 启用共享内存转发
    path: ./exchange.membin         # 内存映射文件路径

# 行情解析器配置文件
parsers: mdparsers.yaml

# 状态监控配置文件  
statemonitor: statemonitor.yaml

# 数据存储配置
writer:
    module: WtDataStorage           # 数据存储模块
    async: true                     # 异步落地（股票推荐异步，期货推荐同步）
    groupsize: 100                  # 日志分组大小
    path: ../../storage             # 数据存储路径
    savelog: true                   # 保存tick到csv
    disabletick: false              # 保存tick数据
    disablemin1: false              # 保存1分钟K线数据
    disablemin5: false              # 保存5分钟K线数据
    disableday: false               # 保存日K线数据
    disabletrans: false             # 保存股票L2逐笔成交数据
    disableordque: false            # 保存股票L2委托队列数据
    disableorddtl: false            # 保存股票L2逐笔委托数据
