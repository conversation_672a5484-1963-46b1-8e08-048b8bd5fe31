/**
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at

 * http://www.apache.org/licenses/LICENSE-2.0

 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type Nullable from '../../common/Nullable'

import FigureImp, { type FigureTemplate, type FigureConstructor, type FigureInnerConstructor } from '../../component/Figure'

import circle from './circle'
import line from './line'
import polygon from './polygon'
import rect from './rect'
import text from './text'
import arc from './arc'
import path from './path'

const figures: Record<string, FigureInnerConstructor> = {}

const extensions = [circle, line, polygon, rect, text, arc, path]
extensions.forEach((figure: FigureTemplate) => {
  figures[figure.name] = FigureImp.extend(figure)
})

function getSupportedFigures (): string[] {
  return Object.keys(figures)
}

function registerFigure<A = unknown, S = unknown> (figure: FigureTemplate<A, S>): void {
  figures[figure.name] = FigureImp.extend(figure)
}

function getInnerFigureClass (name: string): Nullable<FigureInnerConstructor> {
  return figures[name] ?? null
}

function getFigureClass<A = unknown, S = unknown> (name: string): Nullable<FigureConstructor<A, S>> {
  return figures[name] ?? null
}

export { getSupportedFigures, getFigureClass, getInnerFigureClass, registerFigure }
