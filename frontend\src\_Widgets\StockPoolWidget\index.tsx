import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  Tabs,
  Card,
  Row,
  Col,
  Table,
  Button,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Tag,
  Empty,
  Dropdown,
  Menu,
  Tooltip,
  message,
  Spin,
  Drawer,
  Popconfirm,
  Switch,
  Upload,
  Divider,
  List,
  Checkbox,
  Result
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  ShareAltOutlined,
  SettingOutlined,
  StarOutlined,
  EllipsisOutlined,
  LoadingOutlined,
  ImportOutlined,
  ExportOutlined,
  OrderedListOutlined
} from '@ant-design/icons';
import { useAtom } from 'jotai';
import { selectedSymbolAtom } from '@/store/state';
import { EventBus } from '@/events/eventBus';
import { MarketEvents, ChartEvents } from '@/events/events';
import { Symbol } from '@/shared_types/market';
import poolDispatcher from '@/_Dispatchers/poolDispatcher';
import { getUserInfo, getToken } from '@/utils/auth'; // <-- 导入 getUserInfo 和 getToken
import httpDispatcher from '@/_Dispatchers/HttpDispatcher'; // <-- 导入 httpDispatcher
import { poolCache } from '@/utils/PoolCache'; // <-- 导入 poolCache
import { validateSymbolList, validateSymbolListObject, parseAndValidateInput } from './validateSymbolList';
import { PoolIndexJson, CategoryStructure, PoolIdentifier } from './types'; // <-- Import new types
import { completeAShareCodes } from './aShareCompleter'; // <-- Import completer function
import './index.less';
import { useParams } from 'react-router-dom';

const { TextArea } = Input;
const { Title, Text } = Typography;

// --- IndexedDB Constants ---
const DB_NAME = 'QuantQuartPoolDB';
const DB_VERSION = 1;
const STORE_NAME = 'symbol_pool_index';

// 定义股票池类型和接口
interface StockItem {
  id: string;
  name: string; // 显示名称，例如"中国平安"
  symbol: string; // 完整代码，例如"SSE.STOCK.601318"
  code: string; // 仅代码部分，例如"601318"
  tags?: string[]; // 标签
  addedAt?: number; // 添加时间
  lastViewed?: number; // 最后查看时间
  poolName?: string; // 所属池子名称
  data?: any; // 附加信息数据
}

// 提取代码部分辅助函数
const extractCodeFromSymbol = (symbolString: string): string => {
  if (!symbolString || typeof symbolString !== 'string') return symbolString;
  const parts = symbolString.split('.');
  if (parts.length >= 3) {
    return parts.slice(2).join('.');
  }
  return symbolString;
};

// 解析符号字符串为Symbol对象
const parseSymbolString = (symbolString: string, name: string): Symbol | null => {
  if (!symbolString || typeof symbolString !== 'string') return null;
  const parts = symbolString.split('.');
  if (parts.length >= 3) {
    return {
      exchange: parts[0] as any,
      market: parts[1] as any,
      code: parts.slice(2).join('.'),
      name: name
    };
  }
  return null;
};

// 股票列表编辑面板组件
interface StockListEditorProps {
  visible: boolean;
  onClose: () => void;
  // pool prop now represents structure + items
  pool: { id: number; name: string; items: StockItem[] } | null;
  onSave: (poolId: number, updatedItems: StockItem[]) => Promise<boolean>;
}

const StockListEditor: React.FC<StockListEditorProps> = ({
  visible,
  onClose,
  pool,
  onSave
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [stocks, setStocks] = useState<StockItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [searchText, setSearchText] = useState<string>('');

  // AI提示词
  const aiPromptText = `把上述股票列表数据，按照下列规范输出为 JSON 数据格式，
1、代码格式，交易所简写.品类.代码，其中品类是 STOCK/ETF/FUTURE/FOREX/CRYPTO
2、输出为JSON数组，其中项目的格式：{symbol:全代码, name:品种名称, data: {可选}}
3、可选data说明，如果没有指定不用输出data字段，如果特别指定，则按照所指定的样式放置在data字段内部作为自定义项目`;

  // 复制AI提示词到剪贴板
  const copyAIPrompt = () => {
    navigator.clipboard.writeText(aiPromptText).then(() => {
      message.success('已复制AI提示词到剪贴板');
    }).catch(() => {
      message.error('复制失败，请手动复制');
    });
  };

  // 初始化股票列表
  useEffect(() => {
    if (pool && visible) {
      // 直接使用传入的 pool.items
      setStocks(pool.items || []);
      // 重置内部状态
      setSelectedItems([]);
      setInputValue('');
      setSearchText('');
    }
  }, [pool, visible]);

  // 过滤后的股票列表
  const filteredStocks = useMemo(() => {
    if (!searchText) return stocks;

    return stocks.filter(stock =>
      stock.name.toLowerCase().includes(searchText.toLowerCase()) ||
      stock.symbol.toLowerCase().includes(searchText.toLowerCase()) ||
      stock.code.toLowerCase().includes(searchText.toLowerCase())
    );
  }, [stocks, searchText]);

  // 处理输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
  };

  // 解析并添加股票
  const handleAddStocks = () => {
    if (!inputValue || !inputValue.trim()) {
      message.info('请输入股票信息');
      return;
    }

    const validationResult = parseAndValidateInput(inputValue);

    if (!validationResult.isValid || !validationResult.data) {
      message.error(validationResult.error || '输入内容无效或格式错误');
      return;
    }

    let itemsToAdd: { symbol: string; name: string;[key: string]: any }[] = [];

    if (validationResult.type === 'json') {
      itemsToAdd = validationResult.data as { symbol: string; name: string;[key: string]: any }[];
      console.log('[股票列表编辑] 解析为标准 JSON 列表', itemsToAdd.length);
    } else if (validationResult.type === 'a_share_codes') {
      const codes = validationResult.data as string[];
      console.log('[股票列表编辑] 解析为 A 股代码串', codes.length);
      itemsToAdd = completeAShareCodes(codes);
      console.log('[股票列表编辑] 代码补全结果', itemsToAdd.length);
    } else {
      message.error('无法处理的输入类型'); // Should not happen if validation logic is correct
      return;
    }

    if (itemsToAdd.length === 0) {
      message.info('未找到有效的股票信息可添加');
      return;
    }

    // --- Deduplication and State Update Logic (remains similar) ---
    const currentSymbols = new Set(stocks.map(s => s.symbol));
    const addedStocks: StockItem[] = [];
    const skippedStocks: string[] = [];

    itemsToAdd.forEach((item, index) => {
      // Basic check (already partially done in validation/completion, but good to double check)
      if (!item.symbol || !item.name) {
        console.warn(`[股票列表编辑] 第 ${index + 1} 项缺少 symbol 或 name:`, item);
        skippedStocks.push(`第 ${index + 1} 项缺少 symbol 或 name`);
        return;
      }
      if (currentSymbols.has(item.symbol)) {
        console.warn(`[股票列表编辑] 股票已存在，跳过: ${item.symbol}`);
        skippedStocks.push(`股票 ${item.symbol} 已存在`);
        return;
      }

      const code = item.code || extractCodeFromSymbol(item.symbol); // Extract code if not provided
      addedStocks.push({
        id: `new-${Date.now()}-${index}`, // Temporary ID
        name: item.name,
        symbol: item.symbol,
        code: code,
        data: item.data, // 添加data字段
        tags: item.tags || [], // Include other potential fields from JSON input
        addedAt: Date.now(),
      });
      currentSymbols.add(item.symbol); // Add to set to prevent duplicates within the input itself
    });

    if (skippedStocks.length > 0) {
      message.warning(`部分股票被跳过: ${skippedStocks.slice(0, 3).join(', ')}${skippedStocks.length > 3 ? '...' : ''}`);
    }

    if (addedStocks.length > 0) {
      setStocks(prevStocks => [...prevStocks, ...addedStocks]);
      setInputValue(''); // Clear input on successful partial/full add
      message.success(`成功添加 ${addedStocks.length} 只股票到编辑列表`);
    } else if (skippedStocks.length === 0 && itemsToAdd.length > 0) {
      // This case means all valid input items were duplicates
      message.info('所有输入的有效股票都已存在于列表中');
    } else if (addedStocks.length === 0 && itemsToAdd.length > 0) {
      // This case means all input items were skipped for other reasons (missing fields etc)
      // Error messages were likely shown already
    } else if (itemsToAdd.length === 0) {
      // Handled earlier, but as a fallback
      message.info('没有有效的股票信息可添加');
    }

  };

  // 删除单个股票
  const handleDeleteStock = (stockId: string) => {
    setStocks(prevStocks => prevStocks.filter(stock => stock.id !== stockId));
    setSelectedItems(prev => prev.filter(id => id !== stockId)); // Also remove from selection
  };

  // 批量删除股票
  const handleBatchDelete = () => {
    if (selectedItems.length === 0) {
      message.info('请先选择要删除的股票');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedItems.length} 只股票吗？`,
      onOk: () => {
        setStocks(prevStocks =>
          prevStocks.filter(stock => !selectedItems.includes(stock.id))
        );
        setSelectedItems([]);
      }
    });
  };

  // 保存所有更改
  const handleSaveAll = async () => {
    if (!pool) return;

    setLoading(true);
    try {
      // 使用传入的 onSave 回调，传递 pool.id 和当前 stocks 状态
      const success = await onSave(pool.id, stocks);
      if (success) {
        message.success('保存成功');
        onClose(); // 关闭模态框
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      console.error('[股票列表编辑] 保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '股票名称/代码',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: StockItem) => (
        // Display name and code on the same line with spacing
        <Space size="small">
          <Text strong>{text}</Text>
          <Text type="secondary">({record.code})</Text> { /* Display only code part */}
        </Space>
      )
    },
    {
      title: '附加信息',
      key: 'info',
      render: (_: any, record: StockItem) => (
        <>
          {record.data ? (
            <Text>
              {typeof record.data === 'object'
                ? JSON.stringify(record.data)
                : String(record.data)}
            </Text>
          ) : record.tags && record.tags.length > 0 ? (
            <span>
              {record.tags.map(tag => (
                <Tag key={tag} color="blue">{tag}</Tag>
              ))}
            </span>
          ) : (
            <Text type="secondary">无</Text>
          )}
        </>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 60,
      render: (_: any, record: StockItem) => (
        <Button
          type="text"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteStock(record.id)}
        />
      )
    }
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys: selectedItems,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedItems(selectedRowKeys as string[]);
    }
  };

  return (
    <Modal
      title={pool ? `编辑股票列表 - ${pool.name}` : '编辑股票列表'}
      open={visible}
      onCancel={onClose}
      width={900}
      zIndex={1100} // 增加z-index，确保显示在其他对话框上面
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="delete"
          danger
          disabled={selectedItems.length === 0}
          onClick={handleBatchDelete}
        >
          删除选中项
        </Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSaveAll}
          loading={loading}
        >
          保存更改
        </Button>
      ]}
    >
      <div className="stock-list-editor">
        <Row gutter={16}>
          {/* 左侧股票列表 */}
          <Col span={12}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
              <Text type="secondary">
                {`共 ${stocks.length} 只股票${searchText ? `，当前显示 ${filteredStocks.length} 只` : ''}`}
              </Text>
              <Input.Search
                placeholder="搜索名称/代码"
                allowClear
                style={{ width: 150 }}
                onChange={e => setSearchText(e.target.value)}
                onSearch={setSearchText} // Trigger search on enter/click
              />
            </div>

            <Table
              rowKey="id" // Stocks still have temporary IDs
              rowSelection={rowSelection}
              columns={columns}
              dataSource={filteredStocks}
              size="small"
              pagination={{ pageSize: 10, size: 'small', showSizeChanger: false }} // Simplified pagination
              scroll={{ y: 400 }}
              loading={loading} // Use internal loading state
              locale={{ emptyText: '暂无股票' }}
            />
          </Col>

          {/* 右侧添加区域 */}
          <Col span={12}>
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* 上部输入区 */}
              <div style={{ flex: 1, marginBottom: 16 }}>
                <div style={{ marginBottom: 8, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text type="secondary">粘贴JSON数组: [{'{'}'symbol":"代码", "name":"名称"{'}'}], ...]</Text>
                  <Tooltip title="复制AI提示词">
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={copyAIPrompt}
                      size="small"
                    />
                  </Tooltip>
                </div>
                <TextArea
                  value={inputValue}
                  onChange={handleInputChange}
                  placeholder={'请粘贴JSON格式的股票数据，例如：\n[\n  {"symbol": "SSE.STOCK.600519", "name": "贵州茅台"},\n  {"symbol": "SZSE.STOCK.000001", "name": "平安银行"}\n]'}
                  style={{ height: 350, resize: 'none' }}
                />
              </div>

              {/* 下部添加按钮 */}
              <div>
                <Button
                  type="primary"
                  block
                  size="large"
                  onClick={handleAddStocks}
                  disabled={!inputValue.trim()} // Disable if input is empty
                >
                  添加至列表
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </Modal>
  );
};

// --- Extracted Initialization Function --- Moved from useEffect ---

/**
 * Opens the IndexedDB, ensures the object store exists,
 * loads system and user data, and ensures default user category/pool exist.
 * @returns A Promise resolving with { sysData: PoolIndexJson, userData: PoolIndexJson }
 */
export async function initializeStockPoolData(): Promise<{ sysData: PoolIndexJson; userData: PoolIndexJson; db: IDBDatabase | null }> {
  console.log('[股票池DB Init] Initializing IndexedDB and loading data...');
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);
    let dbInstance: IDBDatabase | null = null;

    request.onupgradeneeded = (event) => {
      console.log('[股票池DB Init] Upgrade needed or DB creation.');
      const db = (event.target as IDBOpenDBRequest).result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        console.log(`[股票池DB Init] Creating object store: ${STORE_NAME}`);
        db.createObjectStore(STORE_NAME, { keyPath: 'name' });
      }
    };

    request.onerror = (event) => {
      const error = (event.target as IDBOpenDBRequest).error;
      console.error('[股票池DB Init] Database error:', error);
      message.error('数据库初始化失败: ' + error?.message);
      reject(new Error('数据库初始化失败'));
    };

    request.onsuccess = (event) => {
      console.log('[股票池DB Init] Database opened successfully.');
      dbInstance = (event.target as IDBOpenDBRequest).result;

      // Function to read data and perform checks
      const readAndCheckData = (currentDb: IDBDatabase) => {
        return new Promise<{ sysData: PoolIndexJson; userData: PoolIndexJson; db: IDBDatabase | null }>((innerResolve, innerReject) => {
          // 确保 poolDispatcher 已初始化
          poolDispatcher.initialize();

          // 从后端获取系统和用户池索引数据
          const fetchBackendData = async () => {
            try {
              console.log('[股票池DB Init] 从后端获取系统池索引数据...');
              let sysBackendData: PoolIndexJson = [];
              let userBackendData: PoolIndexJson = [];

              // 获取系统池索引
              try {
                // 使用 poolDispatcher 获取系统池索引
                sysBackendData = await poolDispatcher.getPoolIndex(true);
                if (sysBackendData && Array.isArray(sysBackendData) && sysBackendData.length > 0) {
                  console.log(`[股票池DB Init] 成功从后端获取系统池索引: ${sysBackendData.length} 个分类`);
                } else {
                  console.warn('[股票池DB Init] 后端返回的系统池索引无效或为空，使用空数组');
                  sysBackendData = [];
                }
              } catch (sysError) {
                console.error('[股票池DB Init] 获取系统池索引失败:', sysError);
                sysBackendData = [];
              }

              // 获取用户池索引
              try {
                // 使用 poolDispatcher 获取用户池索引
                userBackendData = await poolDispatcher.getPoolIndex(false);
                if (userBackendData && Array.isArray(userBackendData) && userBackendData.length > 0) {
                  console.log(`[股票池DB Init] 成功从后端获取用户池索引: ${userBackendData.length} 个分类`);
                } else {
                  console.warn('[股票池DB Init] 后端返回的用户池索引无效或为空，使用空数组');
                  userBackendData = [];
                }
              } catch (userError) {
                console.error('[股票池DB Init] 获取用户池索引失败:', userError);
                userBackendData = [];
              }

              // 读取本地数据
              const tx = currentDb.transaction(STORE_NAME, 'readwrite');
              const store = tx.objectStore(STORE_NAME);

              // 获取本地系统池索引
              const getSysRequest = store.get('system');
              let localSysData: PoolIndexJson = [];

              getSysRequest.onsuccess = (e) => {
                const result = (e.target as IDBRequest).result;
                if (!result) {
                  console.log('[股票池DB Init] 本地没有系统池索引数据');
                  localSysData = [];
                } else {
                  localSysData = Array.isArray(result.json) ? result.json : [];
                  console.log(`[股票池DB Init] 从本地加载系统池索引: ${localSysData.length} 个分类`);
                }
              };

              // 获取本地用户池索引
              const getUserRequest = store.get('user');
              let localUserData: PoolIndexJson = [];

              getUserRequest.onsuccess = (e) => {
                const result = (e.target as IDBRequest).result;
                if (!result) {
                  console.log('[股票池DB Init] 本地没有用户池索引数据');
                  localUserData = [];
                } else {
                  localUserData = Array.isArray(result.json) ? result.json : [];
                  console.log(`[股票池DB Init] 从本地加载用户池索引: ${localUserData.length} 个分类`);
                }
              };

              tx.oncomplete = async () => {
                console.log('[股票池DB Init] 本地数据读取完成，开始比较本地和后端数据');

                // 检查系统池索引是否需要更新
                const sysNeedsUpdate = sysBackendData.length > 0 &&
                  (localSysData.length === 0 || JSON.stringify(localSysData) !== JSON.stringify(sysBackendData));

                // 检查用户池索引是否需要更新
                const userNeedsUpdate = userBackendData.length > 0 &&
                  (localUserData.length === 0 || JSON.stringify(localUserData) !== JSON.stringify(userBackendData));

                // 如果需要更新，写入本地数据库
                if (sysNeedsUpdate || userNeedsUpdate) {
                  console.log('[股票池DB Init] 检测到数据不一致，更新本地数据');
                  const updateTx = currentDb.transaction(STORE_NAME, 'readwrite');
                  const updateStore = updateTx.objectStore(STORE_NAME);

                  if (sysNeedsUpdate) {
                    console.log('[股票池DB Init] 更新本地系统池索引');
                    updateStore.put({ name: 'system', json: sysBackendData });
                    localSysData = sysBackendData;
                  }

                  if (userNeedsUpdate) {
                    console.log('[股票池DB Init] 更新本地用户池索引');
                    updateStore.put({ name: 'user', json: userBackendData });
                    localUserData = userBackendData;
                  }

                  updateTx.oncomplete = () => {
                    console.log('[股票池DB Init] 本地数据更新完成');
                  };

                  updateTx.onerror = (e) => {
                    console.error('[股票池DB Init] 更新本地数据失败:', (e.target as IDBTransaction).error);
                    message.error('更新本地数据失败');
                  };
                } else {
                  console.log('[股票池DB Init] 本地数据与后端一致，无需更新');
                }

                // 确保用户数据中有"常用"分类
                let finalUserData = localUserData;
                let needsFinalWrite = false;

                // 检查"常用"分类
                const commonCategoryName = '常用';
                let commonCategoryIndex = finalUserData.findIndex(cat => cat.categoryname === commonCategoryName);

                if (commonCategoryIndex === -1) {
                  console.log(`[股票池DB Init] 创建默认分类: "${commonCategoryName}"`);
                  const newCommonCategory: CategoryStructure = { categoryname: commonCategoryName, lists: [] };
                  finalUserData.push(newCommonCategory);
                  commonCategoryIndex = finalUserData.length - 1;
                  needsFinalWrite = true;
                }

                // 检查"常用"分类下是否有"浏览历史"池子
                const historyPoolName = '浏览历史';
                const commonCategory = finalUserData[commonCategoryIndex];
                const historyPoolIndex = commonCategory.lists.findIndex(pool => pool.poolname === historyPoolName);

                if (historyPoolIndex === -1) {
                  console.log(`[股票池DB Init] 在"常用"分类下创建"${historyPoolName}"池子`);

                  // 创建"浏览历史"池子
                  try {
                    // 先检查后端是否已经有这个池子
                    const existingPool = await poolDispatcher.getSymbolList(historyPoolName, false);

                    if (existingPool && (existingPool as any).poolId) {
                      // 如果后端已经有这个池子，添加到本地
                      commonCategory.lists.push({
                        poolid: (existingPool as any).poolId,
                        poolname: historyPoolName
                      });
                      needsFinalWrite = true;
                      console.log(`[股票池DB Init] 添加已存在的"${historyPoolName}"池子到"常用"分类`);
                    } else {
                      // 如果后端没有这个池子，创建一个新的
                      const newPool = await poolDispatcher.createPool({
                        name: historyPoolName,
                        symbols_json: [],
                        is_system: false
                      });

                      if (newPool && newPool.id) {
                        commonCategory.lists.push({
                          poolid: newPool.id,
                          poolname: historyPoolName
                        });
                        needsFinalWrite = true;
                        console.log(`[股票池DB Init] 创建并添加"${historyPoolName}"池子到"常用"分类`);
                      } else {
                        console.error(`[股票池DB Init] 创建"${historyPoolName}"池子失败`);
                      }
                    }
                  } catch (error) {
                    console.error(`[股票池DB Init] 处理"${historyPoolName}"池子时出错:`, error);
                  }
                }

                if (needsFinalWrite) {
                  console.log('[股票池DB Init] 写入更新后的用户数据...');
                  const writeDefaultsTx = currentDb.transaction(STORE_NAME, 'readwrite');
                  const writeDefaultsStore = writeDefaultsTx.objectStore(STORE_NAME);
                  writeDefaultsStore.put({ name: 'user', json: finalUserData });

                  writeDefaultsTx.oncomplete = () => {
                    console.log('[股票池DB Init] 用户数据写入完成');
                    innerResolve({ sysData: localSysData, userData: finalUserData, db: currentDb });
                  };

                  writeDefaultsTx.onerror = (e_write) => {
                    console.error('[股票池DB Init] 写入用户数据失败:', (e_write.target as IDBTransaction).error);
                    message.error('无法保存默认用户数据');
                    innerResolve({ sysData: localSysData, userData: finalUserData, db: currentDb });
                  };
                } else {
                  innerResolve({ sysData: localSysData, userData: finalUserData, db: currentDb });
                }
              };

              tx.onerror = (e) => {
                console.error('[股票池DB Init] 读取本地数据失败:', (e.target as IDBTransaction).error);
                message.error('读取本地数据失败');
                innerReject(new Error('读取本地数据失败'));
              };
            } catch (error) {
              console.error('[股票池DB Init] 获取后端数据过程中出错:', error);
              innerReject(error);
            }
          };

          // 执行数据获取和比较
          fetchBackendData();
        });
      };

      readAndCheckData(dbInstance).then(resolve).catch(reject);
    };
  });
}

// --- End of Extracted Function ---

/**
 * 股票池管理组件
 */
interface StockPoolWidgetProps {
  tag: number;
}

const StockPoolWidget: React.FC<StockPoolWidgetProps> = ({ tag }) => {

  // 需要增加一个props，用于传递tag

  // --- Refactored State Management ---
  const [activeTabKey, setActiveTabKey] = useState<'system' | 'user'>('system');
  const [sysJsonData, setSysJsonData] = useState<PoolIndexJson | null>(null); // System structure
  const [userJsonData, setUserJsonData] = useState<PoolIndexJson | null>(null); // User structure
  const [selectedCategoryName, setSelectedCategoryName] = useState<string | null>(null); // Name of the selected category
  // 移除 selectedPoolId 状态，不再在面板中维护选中的池子ID
  const [currentPoolItems, setCurrentPoolItems] = useState<StockItem[]>([]); // 当前正在编辑的池子的项目
  const [loadingPoolItems, setLoadingPoolItems] = useState<boolean>(false); // Loading state for pool items
  const [isDbReady, setIsDbReady] = useState<boolean>(false); // Flag for IndexedDB readiness
  const [initializationError, setInitializationError] = useState<string | null>(null); // State for initialization error

  // 存储所有池子的内容，用于显示数量和快速编辑
  const [allPoolContents, setAllPoolContents] = useState<{[key: string]: StockItem[]}>({});
  const [loadingAllPools, setLoadingAllPools] = useState<boolean>(false); // 加载所有池子内容的状态

  // 记录已同步的池子，避免重复从后端获取
  const [syncedPools, setSyncedPools] = useState<Set<string>>(new Set());

  const dbRef = useRef<IDBDatabase | null>(null); // Ref to hold the DB instance

  // --- Existing State (potentially keep or modify) ---
  // const [editModalVisible, setEditModalVisible] = useState(false); // Keep for adding single stock (if needed?) - REMOVED, use StockListEditor
  const [editPoolModalVisible, setEditPoolModalVisible] = useState(false); // Keep for add/edit pool name
  // const [editingPool, setEditingPool] = useState<StockPool | null>(null); // Modify to store PoolIdentifier | null

  const [editingPoolInfo, setEditingPoolInfo] = useState<{
    poolId: number;
    poolName: string;
    isSystem: boolean;
  } | null>(null);

  // 使用 editingPoolInfo 替代 selectedPoolId 和 editingPoolId
  const [, setSelectedSymbol] = useAtom(selectedSymbolAtom); // Keep
  // const [form] = Form.useForm(); // Keep for add/edit stock modal (if needed) - REMOVED
  const [poolForm] = Form.useForm(); // Keep for add/edit pool modal
  const [loading, setLoading] = useState<boolean>(false); // General loading state (e.g., for pool/category actions)
  // const [loadingPools, setLoadingPools] = useState<boolean>(false); // REMOVED (pool list is local)
  // const [loadingPool, setLoadingPool] = useState<boolean>(false); // Renamed to loadingPoolItems
  // const [editingStockId, setEditingStockId] = useState<string | null>(null); // REMOVED (managed within StockListEditor)
  // const [stockForm] = Form.useForm(); // REMOVED
  // const [batchEditVisible, setBatchEditVisible] = useState<boolean>(false); // REMOVED (integrated into StockListEditor)
  // const [batchEditValue, setBatchEditValue] = useState<string>(""); // REMOVED
  // const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]); // REMOVED (selection managed in StockListEditor)
  const [editCategoryModalVisible, setEditCategoryModalVisible] = useState(false); // Keep
  // const [editingCategory, setEditingCategory] = useState<StockCategory | null>(null); // Modify to store name: string | null
  const [editingCategoryName, setEditingCategoryName] = useState<string | null>(null); // Name of category being edited/created
  const [categoryForm] = Form.useForm(); // Keep
  const [stockListEditorVisible, setStockListEditorVisible] = useState<boolean>(false); // Keep

  // --- User Info (Keep) ---
  const userInfo = getUserInfo();
  const isAdmin = userInfo?.role === 'admin';
  console.log('[股票池权限] 用户信息:', userInfo, ' 是否管理员:', isAdmin);

  // --- Ensure poolDispatcher is initialized (Keep) ---
  useEffect(() => {
    poolDispatcher.initialize();
  }, []);

  // --- Component Initialization using the extracted function ---
  useEffect(() => {
    let localDbRef: IDBDatabase | null = null;

    const loadData = async () => {
      try {
        console.log('[股票池 Widget] Calling initializeStockPoolData...');
        const { sysData, userData, db } = await initializeStockPoolData();
        console.log('[股票池 Widget] Initialization complete. Setting state...');
        setSysJsonData(sysData);
        setUserJsonData(userData);
        dbRef.current = db; // Store the DB reference returned by the init function
        localDbRef = db; // Keep a local copy for cleanup
        setIsDbReady(true);
        setInitializationError(null); // Clear any previous error

        // Auto-select first category after data is loaded
        const initialData = activeTabKey === 'system' ? sysData : userData;
        if (initialData && initialData.length > 0) {
          setSelectedCategoryName(initialData[0].categoryname);
        } else {
          setSelectedCategoryName(null);
        }

      } catch (error: any) {
        console.error('[股票池 Widget] Initialization failed:', error);
        setInitializationError(error.message || '初始化数据时发生未知错误');
        setIsDbReady(false); // Ensure DB is marked as not ready on error
        // dbRef might be null or invalid here, handled by checks elsewhere
      }
    };

    loadData();

    // Cleanup function
    return () => {
      if (localDbRef) {
        console.log('[股票池 Widget] Cleanup: Closing database connection from initialization.');
        localDbRef.close();
        dbRef.current = null; // Clear the ref as well
        setIsDbReady(false); // Reset ready state on unmount
      }
    };
  }, []); // Run only on mount



  // --- Auto-save JSON data to IndexedDB on change (Modified dependency check) ---
  useEffect(() => {
    // Only save if DB is ready and data is not null (meaning initial load succeeded)
    if (!isDbReady || !dbRef.current || sysJsonData === null || userJsonData === null) return;

    const saveData = (key: 'system' | 'user', data: PoolIndexJson) => {
      // Check dbRef again inside saveData in case it becomes null between effect runs
      if (!dbRef.current) {
        console.error(`[股票池DB Save] Save attempt failed for ${key}: DB connection lost.`);
        // Maybe try to re-initialize or show a persistent error?
        // For now, just prevent saving.
        return;
      }
      console.log(`[股票池DB Save] Saving ${key} data...`);
      try {
        const tx = dbRef.current.transaction(STORE_NAME, 'readwrite');
        const store = tx.objectStore(STORE_NAME);
        store.put({ name: key, json: data });

        tx.oncomplete = () => {
          console.log(`[股票池DB Save] Successfully saved ${key} data.`);
        };
        tx.onerror = (e) => {
          console.error(`[股票池DB Save] Error saving ${key} data:`, (e.target as IDBTransaction).error);
          message.error(`保存 ${key} 数据失败`);
        };
      } catch (error) {
        console.error(`[股票池DB Save] Exception during save transaction for ${key}:`, error);
        message.error(`保存 ${key} 数据时发生异常`);
      }
    };

    // Pass the non-null data to saveData
    if (sysJsonData !== null) saveData('system', sysJsonData);
    if (userJsonData !== null) saveData('user', userJsonData);

  }, [sysJsonData, userJsonData, isDbReady]); // Depend on data and readiness

  // --- REMOVED fetchCategories ---
  // --- REMOVED fetchPoolsByCategory ---

  // --- 处理股票池数据的辅助函数 ---
  const processPoolData = (data: any, poolId: number, poolName: string): StockItem[] => {
    console.log(`[股票池] 处理池 ${poolName} 数据开始，原始数据:`, data);

    // 如果数据是字符串，尝试解析为JSON
    if (typeof data === 'string') {
      try {
        data = JSON.parse(data);
        console.log(`[股票池] 池 ${poolName} 数据是字符串，已解析为:`, data);
      } catch (e) {
        console.error(`[股票池] 池 ${poolName} 数据是字符串但解析失败:`, e);
      }
    }

    if (!data || !Array.isArray(data)) {
      console.warn(`[股票池] 处理池 ${poolName} 数据失败，数据不是数组:`, data);
      return [];
    }

    let items: StockItem[] = [];
    try {
      items = data.map((item: any, index: number) => {
        console.log(`[股票池] 处理池 ${poolName} 项目 ${index}:`, item);

        const code = item.code || extractCodeFromSymbol(item.symbol || '');
        // 确保必要字段存在
        if (!item.symbol || !item.name) {
          console.warn(`[股票池] 池 ${poolName} 中索引 ${index} 的项目缺少 symbol 或 name`, item);
        }

        const processedItem = {
          id: item.id || `item-${poolId}-${index}`,
          name: item.name || code || '未知名称',
          symbol: item.symbol || '未知代码',
          code: code,
          data: item.data, // 添加data字段
          tags: item.tags || [],
          addedAt: item.addedAt || Date.now(),
          lastViewed: item.lastViewed,
          poolName: poolName
        };

        console.log(`[股票池] 池 ${poolName} 项目 ${index} 处理结果:`, processedItem);
        return processedItem;
      });

      // 过滤掉验证失败的项目
      const beforeFilterCount = items.length;
      items = items.filter(item => item.symbol !== '未知代码' && item.name !== '未知名称');
      const afterFilterCount = items.length;

      if (beforeFilterCount !== afterFilterCount) {
        console.warn(`[股票池] 池 ${poolName} 过滤掉 ${beforeFilterCount - afterFilterCount} 个无效项目`);
      }

      console.log(`[股票池] 处理池 ${poolName} 数据完成，共 ${items.length} 个项目`);

    } catch (e) {
      console.error(`[股票池] 解析池 ${poolName} 的项目时出错:`, e);
    }
    return items;
  };

  // --- 获取单个池子的详细内容 ---
  const fetchPoolDetail = async (poolId: number, poolName: string) => {
    // 检查是否已经在加载池子的项目
    if (loadingPoolItems) return;

    // 根据当前标签确定是否为系统列表
    const isSystemList = activeTabKey === 'system';
    const cacheKey = `${poolName}_${isSystemList ? 'system' : 'user'}`;

    console.log(`[股票池] 获取池详情: ID=${poolId}, 名称=${poolName}`);
    setLoadingPoolItems(true);
    setCurrentPoolItems([]); // 立即清除之前的项目

    try {
      // 检查是否已经同步过该池子
      if (syncedPools.has(cacheKey) && allPoolContents[cacheKey] && allPoolContents[cacheKey].length > 0) {
        console.log(`[股票池] 使用已同步的池子数据: ${poolName}`);
        setCurrentPoolItems(allPoolContents[cacheKey]);
        setLoadingPoolItems(false);
        return;
      }

      // 如果没有同步过，从后端获取
      const listData = await poolDispatcher.getSymbolList(poolName, isSystemList);

      if (listData && listData.data) {
        console.log(`[股票池] 获取到的原始数据:`, listData.data);
        if (listData.data.length > 0) {
          console.log(`[股票池] 第一项数据示例:`, listData.data[0]);
          console.log(`[股票池] 第一项是否包含data字段:`, listData.data[0].data !== undefined);
        }

        const items = processPoolData(listData.data, poolId, poolName);
        console.log(`[股票池] 成功获取 ${items.length} 个项目，池: ${poolName}`);
        if (items.length > 0) {
          console.log(`[股票池] 处理后第一项数据示例:`, items[0]);
          console.log(`[股票池] 处理后第一项是否包含data字段:`, items[0].data !== undefined);
        }

        // 更新当前选中的池子内容
        setCurrentPoolItems(items);

        // 同时更新所有池子内容的缓存
        setAllPoolContents(prev => ({
          ...prev,
          [cacheKey]: items
        }));

        // 标记为已同步
        setSyncedPools(prev => {
          const newSet = new Set(prev);
          newSet.add(cacheKey);
          return newSet;
        });
      } else {
        console.warn(`[股票池] 池 ${poolName} 未返回数据`);
        setCurrentPoolItems([]);
      }
    } catch (error) {
      console.error(`[股票池] 获取池 ${poolName} 的项目失败:`, error);
      message.error(`加载 ${poolName} 的股票列表失败`);
      setCurrentPoolItems([]);
    } finally {
      setLoadingPoolItems(false);
    }
  };

  // --- 获取分类下所有池子的内容 ---
  const fetchAllPoolsInCategory = async (categoryName: string) => {
    if (!categoryName || !currentJsonData || loadingAllPools) return;

    const category = currentJsonData.find(cat => cat.categoryname === categoryName);
    if (!category || category.lists.length === 0) return;

    console.log(`[股票池] 开始获取分类 ${categoryName} 下的所有池子内容，共 ${category.lists.length} 个`);
    setLoadingAllPools(true);

    const isSystemList = activeTabKey === 'system';

    console.log('[股票池] 1， allPoolContents：', allPoolContents);

    const newPoolContents = { ...allPoolContents };
    const newSyncedPools = new Set(syncedPools);

    // 特殊处理浏览历史池子
    const hasHistoryPool = category.lists.some(pool => pool.poolname === '浏览历史');
    if (hasHistoryPool) {
      console.log(`[股票池] 分类 ${categoryName} 包含浏览历史池子，强制从缓存获取最新数据`);

      // 先清除浏览历史的同步标记，确保重新获取
      const historyPoolCacheKey = `浏览历史_${isSystemList ? 'system' : 'user'}`;
      newSyncedPools.delete(historyPoolCacheKey);

      // 不要强制同步浏览历史到后端，避免覆盖本地数据
      // 而是在获取浏览历史数据时优先使用本地缓存
    }

    // 并行获取所有池子的内容
    const fetchPromises = category.lists.map(async (pool) => {
      try {
        const cacheKey = `${pool.poolname}_${isSystemList ? 'system' : 'user'}`;

        // 检查是否已经同步过该池子，但浏览历史始终重新获取
        if (pool.poolname !== '浏览历史' && syncedPools.has(cacheKey) && newPoolContents[cacheKey] && newPoolContents[cacheKey].length > 0) {
          console.log(`[股票池] 使用已同步的池子数据: ${pool.poolname}`);
          return;
        }

        console.log(`[股票池] 获取池内容: ${pool.poolname}`);

        // 对于浏览历史，总是从 poolCache 获取，不从后端获取
        if (pool.poolname === '浏览历史') {
          try {
            console.log(`[股票池] 尝试直接从 poolCache 获取浏览历史`);
            const cachedItems = await poolCache.getPoolItems(pool.poolname, isSystemList);

            console.log(`[股票池] 从 poolCache 获取浏览历史结果:`, cachedItems);

            // 即使缓存为空，也不从后端获取，避免覆盖本地数据
            const items = processPoolData(cachedItems || [], pool.poolid, pool.poolname);
            console.log(`[股票池] 处理后的浏览历史项目数量: ${items.length}`);

            // 无论是否为空，都更新本地缓存
            newPoolContents[cacheKey] = items;
            newSyncedPools.add(cacheKey);

            // 如果浏览历史为空，尝试强制刷新 poolCache
            if (items.length === 0) {
              console.log(`[股票池] 浏览历史为空，尝试强制刷新 poolCache`);
              try {
                // 强制刷新 poolCache
                await poolCache.initialize(true); // 传入 true 表示强制刷新

                // 再次尝试获取
                const refreshedItems = await poolCache.getPoolItems(pool.poolname, isSystemList);
                if (refreshedItems && refreshedItems.length > 0) {
                  console.log(`[股票池] 刷新后成功获取浏览历史，共 ${refreshedItems.length} 项`);
                  const refreshedProcessedItems = processPoolData(refreshedItems, pool.poolid, pool.poolname);
                  newPoolContents[cacheKey] = refreshedProcessedItems;
                }
              } catch (refreshError) {
                console.error(`[股票池] 刷新 poolCache 失败:`, refreshError);
              }
            }

            return; // 无论成功与否，都不从后端获取
          } catch (cacheError) {
            console.error(`[股票池] 从 poolCache 获取浏览历史失败:`, cacheError);
            // 即使出错，也不从后端获取，避免覆盖本地数据
            newPoolContents[cacheKey] = [];
            newSyncedPools.add(cacheKey);
            return;
          }
        }

        // 如果从缓存获取失败或不是浏览历史，从 poolDispatcher 获取
        const listData = await poolDispatcher.getSymbolList(pool.poolname, isSystemList);
        console.log(`[股票池] 从 poolDispatcher.getSymbolList 获取的数据:`, listData);

        if (listData && listData.data) {
          console.log(`[股票池] 池 ${pool.poolname} 的 listData.data:`, listData.data);
          console.log(`[股票池] 池 ${pool.poolname} 的 listData.data 类型:`, typeof listData.data);
          console.log(`[股票池] 池 ${pool.poolname} 的 listData.data 是否数组:`, Array.isArray(listData.data));

          const items = processPoolData(listData.data, pool.poolid, pool.poolname);
          console.log(`[股票池] 成功获取 ${items.length} 个项目，池: ${pool.poolname}`);
          if (items.length > 0) {
            console.log(`[股票池] 处理后的第一项数据:`, items[0]);
          }

          newPoolContents[cacheKey] = items;

          // 标记为已同步
          newSyncedPools.add(cacheKey);
        } else {
          console.warn(`[股票池] 池 ${pool.poolname} 未返回数据`);
          console.log(`[股票池] listData:`, listData);
          newPoolContents[cacheKey] = [];
        }
      } catch (error) {
        console.error(`[股票池] 获取池 ${pool.poolname} 的内容失败:`, error);
        // 不显示错误消息，避免多个错误消息同时显示
      }
    });

    try {
      await Promise.all(fetchPromises);

      console.log('[股票池] 设置所有池子内容 newPoolContents：', newPoolContents);

      setAllPoolContents(newPoolContents);
      setSyncedPools(newSyncedPools);
    } catch (error) {
      console.error(`[股票池] 获取分类 ${categoryName} 下的池子内容时出错:`, error);
    } finally {
      setLoadingAllPools(false);
    }
  };

  // --- Get current JSON data based on active tab ---
  const currentJsonData = useMemo(() => {
    if (!isDbReady) return []; // Return empty array if DB is not ready
    return activeTabKey === 'system' ? sysJsonData : userJsonData;
  }, [activeTabKey, sysJsonData, userJsonData, isDbReady]);

  // --- Helper to update JSON state ---
  const updateJsonData = (newData: PoolIndexJson) => {
    if (activeTabKey === 'system') {
      setSysJsonData(newData);
    } else {
      setUserJsonData(newData);
    }

    // 当池子结构发生变化时，清除同步标记
    // 这样下次获取时会从后端获取最新数据
    setSyncedPools(new Set());
  };

  // --- 监听 SYMBOLLIST_UPDATED 事件，当列表内容更新时刷新数据 ---
  useEffect(() => {
    // 监听列表更新事件
    const unsubscribeUpdated = EventBus.on(MarketEvents.Types.SYMBOLLIST_UPDATED, (payload: MarketEvents.SymbolListUpdatedPayload) => {
      console.log(`[股票池] 收到列表更新事件，名称: ${payload.listName}, 是否系统: ${payload.isSystemList}`);

      // 检查当前选中的分类是否包含更新的列表
      if (currentJsonData && selectedCategoryName) {
        const category = currentJsonData.find(cat => cat.categoryname === selectedCategoryName);
        if (category) {
          const poolExists = category.lists.some(pool => pool.poolname === payload.listName);

          if (poolExists && payload.isSystemList === (activeTabKey === 'system')) {
            console.log(`[股票池] 更新的列表 ${payload.listName} 在当前选中的分类 ${selectedCategoryName} 中，更新数据`);

            // 更新缓存中的数据
            if (payload.theList && Array.isArray(payload.theList)) {
              const cacheKey = `${payload.listName}_${payload.isSystemList ? 'system' : 'user'}`;
              const pool = category.lists.find(p => p.poolname === payload.listName);

              if (pool) {
                const items = processPoolData(payload.theList, pool.poolid, payload.listName);
                console.log(`[股票池] 处理后的列表项目数量: ${items.length}`);

                // 更新 allPoolContents
                setAllPoolContents(prev => ({
                  ...prev,
                  [cacheKey]: items
                }));

                // 如果当前正在编辑这个池子，也更新 currentPoolItems
                if (editingPoolInfo && editingPoolInfo.poolName === payload.listName) {
                  setCurrentPoolItems(items);
                }

                // 标记为已同步
                setSyncedPools(prev => {
                  const newSet = new Set(prev);
                  newSet.add(cacheKey);
                  return newSet;
                });
              }
            }
          }
        }
      }
    });

    return () => {
      unsubscribeUpdated.unsubscribe();
    };
  }, [currentJsonData, selectedCategoryName, activeTabKey, editingPoolInfo, processPoolData]);

  // --- Auto-select first category when data loads or tab changes ---
  useEffect(() => {
    if (isDbReady && currentJsonData) {
      // If a category is selected but doesn't exist in the current list, reset it.
      if (selectedCategoryName && !currentJsonData.some(cat => cat.categoryname === selectedCategoryName)) {
        setSelectedCategoryName(null);
        setCurrentPoolItems([]); // Clear items
      }
      // If no category is selected and the list is not empty, select the first one.
      else if (!selectedCategoryName && currentJsonData.length > 0) {
        const firstCategory = currentJsonData[0].categoryname;
        setSelectedCategoryName(firstCategory);
        setCurrentPoolItems([]); // Clear items

        // 获取第一个分类下所有池子的内容
        fetchAllPoolsInCategory(firstCategory);
      }
      // If the list becomes empty, reset selection.
      else if (currentJsonData.length === 0) {
        setSelectedCategoryName(null);
        setCurrentPoolItems([]);
      }
      // 如果已经选择了分类，获取该分类下所有池子的内容
      else if (selectedCategoryName) {
        fetchAllPoolsInCategory(selectedCategoryName);
      }
    }
  }, [currentJsonData, isDbReady, selectedCategoryName, activeTabKey]); // Add activeTabKey dependency


  // 移除自动选择第一个池子的逻辑
  // 移除根据 selectedPoolId 变化加载池子内容的逻辑


  // --- REMOVED filteredPools useMemo ---
  // --- REMOVED currentPool useMemo (derive when needed) ---

  // --- REMOVED handleAddStock (use StockListEditor) ---
  // --- REMOVED handleSubmitStock ---
  // --- REMOVED handleDeleteStock ---

  // --- Refactored Category CRUD ---
  const handleAddCategory = () => {
    setEditingCategoryName(null); // Indicate creation mode
    categoryForm.resetFields();
    setEditCategoryModalVisible(true);
  };

  const handleEditCategory = (categoryName: string) => {
    setEditingCategoryName(categoryName);
    categoryForm.setFieldsValue({ name: categoryName, description: '' }); // Description not stored locally
    setEditCategoryModalVisible(true);
  };

  const handleSubmitCategory = async () => {
    if (!currentJsonData) return;
    try {
      const values = await categoryForm.validateFields();
      const newName = values.name.trim();

      if (!newName) {
        message.error("分类名称不能为空");
        return;
      }

      let updatedJsonData = [...currentJsonData]; // Create a new array copy

      if (editingCategoryName) {
        // Edit existing category
        if (newName !== editingCategoryName && updatedJsonData.some(c => c.categoryname === newName)) {
          message.error(`分类 "${newName}" 已存在`);
          return;
        }
        const index = updatedJsonData.findIndex(c => c.categoryname === editingCategoryName);
        if (index > -1) {
          updatedJsonData[index] = { ...updatedJsonData[index], categoryname: newName };
          // If editing the currently selected category, update the selection state
          if (selectedCategoryName === editingCategoryName) {
            setSelectedCategoryName(newName);
          }
          console.log(`[股票池] Category renamed from "${editingCategoryName}" to "${newName}"`);
        } else {
          console.error(`[股票池] Cannot find category "${editingCategoryName}" to edit.`);
          message.error("找不到要编辑的分类");
          setEditCategoryModalVisible(false);
          return;
        }
      } else {
        // Create new category
        if (updatedJsonData.some(c => c.categoryname === newName)) {
          message.error(`分类 "${newName}" 已存在`);
          return;
        }
        updatedJsonData.push({ categoryname: newName, lists: [] });
        console.log(`[股票池] Category created: "${newName}"`);
        // Optionally select the newly created category
        setSelectedCategoryName(newName);
      }

      updateJsonData(updatedJsonData); // Update state, which triggers DB save
      setEditCategoryModalVisible(false);
      message.success(editingCategoryName ? '分类名称已更新' : '分类已创建');

    } catch (error) {
      console.error("[股票池] 分类表单验证或处理失败:", error);
      // Antd form validation errors are typically handled visually
    }
  };

  const handleDeleteCategory = async (categoryName: string) => {
    if (!currentJsonData) return;

    const categoryToDelete = currentJsonData.find(c => c.categoryname === categoryName);
    if (!categoryToDelete) return;

    const poolsToDelete = categoryToDelete.lists; // Get list of pools in this category

    Modal.confirm({
      title: `确认删除分类 "${categoryName}"`,
      content: `此操作将删除分类及其包含的 ${poolsToDelete.length} 个股票池（列表）。此操作不可撤销！`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        setLoading(true); // Indicate global loading
        try {
          // 1. Delete associated pools (symbol lists) from the backend
          let backendDeletionSuccess = true;
          const isSystemList = activeTabKey === 'system';
          console.log(`[股票池] Deleting ${poolsToDelete.length} pools from backend for category ${categoryName}...`);
          for (const pool of poolsToDelete) {
            try {
              const success = await poolDispatcher.deletePool(pool.poolid, pool.poolname, isSystemList);
              if (!success) {
                message.error(`删除后端列表 "${pool.poolname}" (ID: ${pool.poolid}) 失败，中止操作`);
                backendDeletionSuccess = false;
                break; // Stop deletion process if one fails
              }
              console.log(`[股票池] Successfully deleted backend list: ${pool.poolname} (${pool.poolid})`);
            } catch (poolError) {
              console.error(`[股票池] Error deleting backend list ${pool.poolname} (${pool.poolid}):`, poolError);
              message.error(`删除后端列表 "${pool.poolname}" (ID: ${pool.poolid}) 时出错，中止操作`);
              backendDeletionSuccess = false;
              break;
            }
          }

          // 2. If all backend deletions were successful, remove category from local JSON
          if (backendDeletionSuccess) {
            const updatedJsonData = currentJsonData.filter(c => c.categoryname !== categoryName);
            updateJsonData(updatedJsonData); // Update state -> triggers DB save

            // Reset selection if the deleted category was selected
            if (selectedCategoryName === categoryName) {
              setSelectedCategoryName(null);
              setCurrentPoolItems([]);
            }
            message.success(`分类 "${categoryName}" 及其股票池已删除`);
            console.log(`[股票池] Category "${categoryName}" deleted locally.`);
          }
        } catch (error) {
          console.error(`[股票池] Error during category deletion process for "${categoryName}":`, error);
          message.error(`删除分类 "${categoryName}" 过程中发生错误`);
        } finally {
          setLoading(false);
        }
      }
    });
  };


  // --- Refactored Pool CRUD ---
  const handleAddPool = () => {
    if (!selectedCategoryName) {
      message.info("请先选择一个分类");
      return;
    }
    setEditingPoolInfo(null); // Indicate creation mode
    poolForm.resetFields();
    // Remove symbols_json from the form as it's handled by StockListEditor
    poolForm.setFieldsValue({ symbols_json: undefined });
    setEditPoolModalVisible(true);
  };

  const handleEditPool = (pool: PoolIdentifier) => {
    setEditingPoolInfo({ poolId: pool.poolid, poolName: pool.poolname, isSystem: activeTabKey === 'system' });
    poolForm.setFieldsValue({ name: pool.poolname, symbols_json: undefined }); // Only allow editing name here
    setEditPoolModalVisible(true);
  };

  // Handles creating a new pool (list) or just editing its name locally
  const handleSubmitPool = async () => {
    if (!currentJsonData) return;

    try {
      const values = await poolForm.validateFields();
      const poolName = values.name.trim();
      if (!poolName) {
        message.error("股票池名称不能为空");
        return;
      }

      setLoading(true);

      if (editingPoolInfo) {
        // --- Editing existing pool's name (locally only) ---
        const { poolId: poolIdToEdit, poolName: oldName } = editingPoolInfo;
        const categoryName = selectedCategoryName; // 使用当前选中的分类名称

        // Find the category and the pool within it
        let categoryFound = false;
        let nameConflict = false;
        const updatedJsonData = currentJsonData.map(cat => {
          if (cat.categoryname === categoryName) {
            categoryFound = true;
            // Check for name conflict within the same category, excluding the item being edited
            if (cat.lists.some(p => p.poolname === poolName && p.poolid !== poolIdToEdit)) {
              nameConflict = true;
              return cat; // Return unchanged category if conflict
            }
            // Update the name if no conflict
            return {
              ...cat,
              lists: cat.lists.map(p =>
                p.poolid === poolIdToEdit ? { ...p, poolname: poolName } : p
              )
            };
          }
          return cat;
        });

        if (!categoryFound) {
          message.error(`找不到分类 "${categoryName}"`);
        } else if (nameConflict) {
          message.error(`名称 "${poolName}" 在分类 "${categoryName}" 中已存在`);
        } else {
          updateJsonData(updatedJsonData); // Update state -> triggers DB save
          message.success(`股票池名称已更新为 "${poolName}"`);
          setEditPoolModalVisible(false);
          console.log(`[股票池] Pool ID ${poolIdToEdit} name updated locally from "${oldName}" to "${poolName}"`);
        }

      } else {
        // --- Creating new pool ---
        if (!selectedCategoryName) {
          message.error("无法确定当前分类，无法创建");
          setLoading(false); // Stop loading before return
          return;
        }

        // Check for name conflict in the selected category
        const selectedCat = currentJsonData.find(c => c.categoryname === selectedCategoryName);
        if (selectedCat && selectedCat.lists.some(p => p.poolname === poolName)) {
          message.error(`名称 "${poolName}" 在分类 "${selectedCategoryName}" 中已存在`);
          setLoading(false); // Stop loading
          return;
        }

        // --- Parse and validate symbols_json --- New Logic ---
        let symbolsList = [];
        const symbolsJsonString = values.symbols_json?.trim();
        if (symbolsJsonString) {
          try {
            symbolsList = JSON.parse(symbolsJsonString);
            if (!validateSymbolListObject(symbolsList)) { // Use the validator
              message.error('品种列表格式不正确或包含无效项，请检查后重试');
              setLoading(false);
              return;
            }
            console.log(`[股票池] 创建池子时，验证通过 ${symbolsList.length} 个品种`);
          } catch (e) {
            console.error("[股票池] 解析输入的品种列表JSON失败:", e);
            message.error('品种列表格式无效，必须是正确的JSON数组');
            setLoading(false);
            return;
          }
        } else {
          console.log("[股票池] 创建池子时未提供品种列表，将创建空列表");
        }
        // --- End of New Logic ---

        // Create the list on the backend first
        const isSystemList = activeTabKey === 'system';
        console.log(`[股票池] Creating new backend list: Name="${poolName}", IsSystem=${isSystemList}, Items=${symbolsList.length}`);
        try {
          // Use createPool which internally calls updateSymbolList
          const newPoolResponseData = await poolDispatcher.createPool({
            name: poolName,
            symbols_json: symbolsList, // Pass the parsed and validated list
            is_system: isSystemList,
            description: '' // Optional description
          });

          if (newPoolResponseData && newPoolResponseData.id && newPoolResponseData.listName) {
            const newPoolId = newPoolResponseData.id;
            const newPoolName = newPoolResponseData.listName; // Use name from backend response
            console.log(`[股票池] Backend list created: ID=${newPoolId}, Name=${newPoolName}`);

            // Add to local JSON
            const updatedJsonData = currentJsonData.map(cat => {
              if (cat.categoryname === selectedCategoryName) {
                // Add the new pool identifier to the lists array
                return {
                  ...cat,
                  lists: [...cat.lists, { poolid: newPoolId, poolname: newPoolName }]
                };
              }
              return cat;
            });

            updateJsonData(updatedJsonData); // Update state -> triggers DB save
            message.success(`成功创建股票池 "${newPoolName}"`);
            setEditPoolModalVisible(false);

          } else {
            console.error('[股票池] 创建列表请求成功，但后端返回数据无效:', newPoolResponseData);
            message.error('创建股票池失败，后端返回数据异常');
          }
        } catch (createError) {
          console.error("[股票池] 创建后端列表失败:", createError);
          message.error('创建股票池失败');
        }
      }

    } catch (validateError) {
      console.error("[股票池] Pool form validation failed:", validateError);
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePool = async (poolId: number, poolName: string) => {
    if (!currentJsonData || !selectedCategoryName) return;

    const categoryName = selectedCategoryName; // Assume deletion happens from the selected category

    Modal.confirm({
      title: `确认删除股票池 "${poolName}"`,
      content: '此操作将从后端和本地分类中移除此列表，不可撤销。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        setLoading(true);
        try {
          // 1. Delete from backend
          const isSystemList = activeTabKey === 'system';
          console.log(`[股票池] Deleting backend list: ID=${poolId}, Name=${poolName}, IsSystem=${isSystemList}`);
          const backendSuccess = await poolDispatcher.deletePool(poolId, poolName, isSystemList);

          if (backendSuccess) {
            console.log(`[股票池] Backend list deleted: ${poolName} (${poolId})`);
            // 2. Remove from local JSON
            const updatedJsonData = currentJsonData.map(cat => {
              if (cat.categoryname === categoryName) {
                return {
                  ...cat,
                  lists: cat.lists.filter(p => p.poolid !== poolId)
                };
              }
              return cat;
            });
            updateJsonData(updatedJsonData); // Update state -> triggers DB save
            message.success(`股票池 "${poolName}" 已删除`);
            console.log(`[股票池] Pool ${poolName} (${poolId}) deleted locally from category ${categoryName}.`);

          } else {
            message.error(`删除后端列表 "${poolName}" 失败`);
          }
        } catch (error) {
          console.error(`[股票池] Error deleting pool ${poolName} (${poolId}):`, error);
          message.error(`删除股票池 "${poolName}" 时出错`);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // --- Refactored handleSaveStockList (passed to StockListEditor) ---
  const handleSaveStockList = async (poolId: number, updatedItems: StockItem[]) => {
    // Find the pool name from JSON (needed for backend call if API requires it, though updatePool likely uses ID)
    // For updateSymbolList, name is needed. Let's assume we need it.
    let poolName = '';
    const currentData = activeTabKey === 'system' ? sysJsonData : userJsonData;
    currentData?.forEach(cat => {
      const foundPool = cat.lists.find(p => p.poolid === poolId);
      if (foundPool) {
        poolName = foundPool.poolname;
      }
    });

    if (!poolName) {
      console.error(`[股票池] Cannot find pool name for ID ${poolId} in local JSON.`);
      message.error('无法找到池子信息，无法保存');
      return false;
    }

    console.log(`[股票池] Saving ${updatedItems.length} items to backend list: ID=${poolId}, Name=${poolName}`);
    setLoading(true); // Use global loading or StockListEditor's internal loading? Let's use global for now.
    try {
      // Prepare items for backend: ensure no temporary IDs, potentially clean data
      const itemsToSend = updatedItems.map(({ id, ...rest }) => ({
        ...rest, // Send name, symbol, code, tags etc.
        // Omit temporary frontend ID ('id') if backend generates its own
      }));

      // Decide which dispatcher method to use. Let's use updateSymbolList as it seems more aligned with current backend structure.
      const isSystemList = activeTabKey === 'system';
      // Retrieve description if needed, otherwise pass empty or current description
      const description = ''; // Assuming description is not managed here

      // Use updateSymbolList to save the list content
      const responseData = await poolDispatcher.updateSymbolList({
        name: poolName,       // Use the correct list name
        symbols_json: itemsToSend,    // The list data
        is_system: isSystemList,
        description: description
      });

      if (responseData) { // Check if response indicates success (adjust based on actual API response)
        console.log(`[股票池] Backend list ${poolName} updated successfully.`);
        // Optionally, re-fetch the list items to get backend-generated IDs if necessary
        // For now, assume local state is sufficient after save.
        // 如果当前正在编辑的池子被保存，更新其内容状态
        if (editingPoolInfo && editingPoolInfo.poolId === poolId) {
          setCurrentPoolItems(updatedItems); // Update local items state to reflect saved state
        }

        // 发送事件通知 SymbolList 组件更新
        console.log(`[股票池] 发送列表变化事件: ${poolName}, isSystemList=${isSystemList}`);
        EventBus.emit(MarketEvents.Types.SYMBOLLIST_CHANGED, {
          symbolListName: poolName,
          theList: itemsToSend, // 直接传递对象数组，SymbolList 组件已经修改为接受对象数组
          description: description,
          isSystemList: isSystemList,
          tag: tag
        });

        return true; // Indicate success to StockListEditor
      } else {
        console.error(`[股票池] Backend update for list ${poolName} failed.`);
        message.error(`保存股票列表 "${poolName}" 失败`);
        return false; // Indicate failure
      }
    } catch (error) {
      console.error(`[股票池] Error saving stock list ${poolName} (${poolId}) to backend:`, error);
      message.error(`保存股票列表 "${poolName}" 失败`);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // --- Refactored Rendering Logic ---

  // Render Category Cards
  const renderCategoryCards = () => {
    if (!isDbReady || !currentJsonData) {
      return (
        <div style={{ textAlign: 'center', padding: '16px' }}>
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} tip="加载分类数据..." />
        </div>
      );
    }

    const canManageCategories = isAdmin || activeTabKey === 'user';

    if (currentJsonData.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Empty description={`暂无${activeTabKey === 'system' ? '系统' : '用户'}分类`} />
          {canManageCategories && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddCategory}
              style={{ marginTop: 16 }}
            >
              创建分类
            </Button>
          )}
        </div>
      );
    }

    return (
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
          <Typography.Text type="secondary">
            {`${currentJsonData.length} 个分类`}
          </Typography.Text>
          {/* Edit/Delete buttons are now on the cards themselves */}
        </div>

        <Row gutter={[12, 12]}>
          {/* Existing category cards */}
          {currentJsonData.map(category => (
            <Col xs={12} sm={8} md={6} lg={4} key={category.categoryname}>
              <Card
                // Use categoryname for selection and active state
                className={`category-card ${selectedCategoryName === category.categoryname ? 'active' : ''}`}
                // Set selectedCategoryName on click
                onClick={() => setSelectedCategoryName(category.categoryname)}
                size="small"
                style={{
                  borderRadius: '4px',
                  transition: 'all 0.3s',
                  boxShadow: selectedCategoryName === category.categoryname ? '0 2px 8px rgba(0, 0, 0, 0.15)' : 'none',
                  border: selectedCategoryName === category.categoryname ? '1px solid #1890ff' : '1px solid #f0f0f0',
                  cursor: 'pointer' // Add cursor pointer
                }}
                // Add actions for edit/delete
                actions={canManageCategories ? [
                  <Tooltip title="编辑分类名称" key="edit">
                    <EditOutlined onClick={(e) => { e.stopPropagation(); handleEditCategory(category.categoryname); }} />
                  </Tooltip>,
                  <Tooltip title="删除分类" key="delete">
                    <DeleteOutlined onClick={(e) => { e.stopPropagation(); handleDeleteCategory(category.categoryname); }} />
                  </Tooltip>,
                ] : undefined}
              >
                <Typography.Title level={5} style={{ margin: 0, fontSize: '14px' }}>
                  {/* Use appropriate icon based on tab */}

                  {category.categoryname}
                </Typography.Title>
                <div style={{ color: 'rgba(0, 0, 0, 0.45)', marginTop: 4, fontSize: '12px' }}>
                  {/* Display pool count from category.lists */}
                  {category.lists.length} 个股票池
                </div>
              </Card>
            </Col>
          ))}

          {/* Add Category Button */}
          {canManageCategories && (
            <Col xs={12} sm={8} md={6} lg={4} key="__add_category__">
              <Button
                type="dashed"
                onClick={handleAddCategory}
                style={{
                  width: '100%', // Take full width of the column
                  height: '70px', // Match approx height of category cards
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  padding: '8px',
                  fontSize: '12px'
                }}
              >
                创建分类
              </Button>
            </Col>
          )}
        </Row>
      </div>
    );
  };

  // Render Pool List Table
  const renderPoolList = () => {
    if (!isDbReady) {
      return <div style={{ textAlign: 'center', padding: '20px' }}><Spin tip="加载中..." /></div>;
    }
    if (!selectedCategoryName) {
      return (
        <div className="pool-empty" style={{ padding: '20px' }}>
          <Empty description="请先在上方选择一个分类" />
        </div>
      );
    }

    // Find the selected category object from the current JSON data
    const category = currentJsonData?.find(cat => cat.categoryname === selectedCategoryName);
    if (!category) {
      // This case should ideally be handled by the selection logic, but as a fallback:
      console.warn(`[股票池] Selected category "${selectedCategoryName}" not found in current data.`);
      return (
        <div className="pool-empty" style={{ padding: '20px' }}>
          <Empty description={`无法找到分类 "${selectedCategoryName}"`} />
        </div>
      );
    }

    const categoryPools = category.lists || []; // Get pools for the selected category
    const canManagePools = isAdmin || activeTabKey === 'user'; // Can edit pools in user tab, or if admin


    // Define Pool Table Columns (adjusted)
    const poolColumns = [
      {
        title: '名称',
        dataIndex: 'poolname',
        key: 'name',
        render: (text: string, record: PoolIdentifier) => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Typography.Text
              strong
              style={{
                fontSize: '16px',
                cursor: 'pointer',
                transition: 'color 0.3s'
              }}
              className="pool-name-link"
              onClick={(e) => {
                e.stopPropagation(); // 防止触发行点击

                // 切换 SymbolList 中的列表
                const isSystemList = activeTabKey === 'system';
                console.log(`[股票池] 发送切换列表事件: ${record.poolname}, isSystemList=${isSystemList}`);
                EventBus.emit(MarketEvents.Types.REQUEST_SYMBOLLIST_DATA, {
                  symbolListName: record.poolname,
                  isSystemList: isSystemList,
                  tag: tag
                });

                // 关闭股票池管理面板
                EventBus.emit(MarketEvents.Types.SHOW_STOCK_POOL_MANAGER, {
                  visible: false,
                  currentListName: record.poolname,
                  // 把 prop 接收进来的tag 传递给 StockPoolWidget
                  tag: 0
                });
              }}
            >
              {text}
            </Typography.Text>
            {canManagePools && (
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                className="hover-visible-button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditPool(record);
                }}
                style={{ marginLeft: 8 }}
              />
            )}
          </div>
        )
      },
      {
        title: '股票数量',
        key: 'count',
        width: 80,
        align: 'right' as const,
        render: (_: any, record: PoolIdentifier) => {
          const isSystemList = activeTabKey === 'system';
          const cacheKey = `${record.poolname}_${isSystemList ? 'system' : 'user'}`;
          const poolItems = allPoolContents[cacheKey];

          // 如果有缓存数据，显示缓存数据的长度
          if (poolItems) {
            return <Typography.Text>{poolItems.length}</Typography.Text>;
          }

          // 如果正在加载所有池子，显示加载中
          if (loadingAllPools) {
            return <Spin size="small" />;
          }

          // 如果没有缓存数据，显示占位符
          return <Typography.Text type="secondary">-</Typography.Text>;
        }
      },
      {
        title: '操作',
        key: 'action',
        width: 80,
        align: 'center' as const, // Align center
        render: (_: any, record: PoolIdentifier) => {
          if (!canManagePools) return null; // Hide actions if not allowed

          return (
            <div className="table-actions-cell">
              <Space>
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  className="hover-visible-button"

                  onClick={(e) => {
                    e.stopPropagation();
                    console.log(`[股票池] 点击编辑按钮: ID=${record.poolid}, 名称=${record.poolname}, 分类=${selectedCategoryName}`);

                    // 设置完整的编辑池信息
                    setEditingPoolInfo({
                      poolId: record.poolid,
                      poolName: record.poolname,
                      isSystem: activeTabKey === 'system'
                    });

                    setStockListEditorVisible(true);
                  }}

                  title="编辑列表内容"
                />
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  danger
                  className="hover-visible-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeletePool(record.poolid, record.poolname);
                  }}
                  title="删除列表"
                />
              </Space>
            </div>
          );
        }
      },
    ];


    if (categoryPools.length === 0) {
      return (
        <div className="pool-empty" style={{ padding: '20px' }}>
          <Empty description="此分类下暂无股票池" />
          {canManagePools && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              style={{ marginTop: 16 }}
              onClick={handleAddPool}
              disabled={!selectedCategoryName} // Disable if no category selected
            >
              添加股票池
            </Button>
          )}
        </div>
      );
    }

    return (
      <div style={{ padding: '0 12px 12px' }}>
        <div style={{ marginBottom: 12, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography.Text type="secondary" style={{ fontSize: '13px' }}>
            {`${categoryPools.length} 个股票池`}
          </Typography.Text>
          {canManagePools && (
            <Button
              type="primary"
              size="small"
              icon={<PlusOutlined />}
              onClick={handleAddPool}
              disabled={!selectedCategoryName} // Disable if no category selected
            >
              新建股票池
            </Button>
          )}
        </div>

        <Table
          dataSource={categoryPools}
          columns={poolColumns}
          rowKey="poolid" // Use poolid as the unique key
          pagination={false}
          className="stock-pool-table"
          onRow={(record: PoolIdentifier) => ({
            onClick: () => {
              console.log(`[股票池] Row clicked, pool: ${record.poolname}`);

              // 切换 SymbolList 中的列表
              const isSystemList = activeTabKey === 'system';
              console.log(`[股票池] 发送切换列表事件: ${record.poolname}, isSystemList=${isSystemList}`);
              EventBus.emit(MarketEvents.Types.REQUEST_SYMBOLLIST_DATA, {
                symbolListName: record.poolname,
                isSystemList: isSystemList,
                tag: tag
              });

              // 关闭股票池管理面板
              EventBus.emit(MarketEvents.Types.SHOW_STOCK_POOL_MANAGER, {
                visible: false,
                currentListName: record.poolname,
                tag: 0
              });
            },
            style: { cursor: 'pointer' } // Indicate rows are clickable
          })}
          size="small"
          locale={{ emptyText: '暂无股票池' }}
          // 不再需要高亮选中行
          rowClassName={() => ''}
          style={{
            borderRadius: '4px',
            overflow: 'hidden',
            border: '1px solid #f0f0f0'
          }}
        />
      </div>
    );
  };

  // --- Get the structure for the StockListEditor ---
  const currentPoolForEditor = useMemo(() => {
    if (!editingPoolInfo) return null;

    const { poolId, poolName, isSystem } = editingPoolInfo;

    // 直接构建缓存键，不需要查找
    const cacheKey = `${poolName}_${isSystem ? 'system' : 'user'}`;
    console.log(`[股票池] 编辑器使用缓存键: ${cacheKey}`);
    const cachedItems = allPoolContents[cacheKey];

    // 检查缓存数据中是否包含data字段
    if (cachedItems && cachedItems.length > 0) {
      console.log(`[股票池] 缓存数据中的第一项:`, cachedItems[0]);
      console.log(`[股票池] 缓存数据中是否包含data字段:`, cachedItems.some(item => item.data !== undefined));
      console.log(`[股票池] 缓存数据中包含data字段的项目数量:`, cachedItems.filter(item => item.data !== undefined).length);
    }

    // 如果没有缓存，触发获取
    if (!cachedItems && !loadingPoolItems) {
      console.log(`[股票池] 编辑器打开时，没有缓存数据，尝试获取: ID=${poolId}, 名称=${poolName}`);
      setTimeout(() => {
        fetchPoolDetail(poolId, poolName);
      }, 0);
      return {
        id: poolId,
        name: poolName,
        items: [] // 返回空数组，等待数据加载
      };
    }

    return {
      id: poolId,
      name: poolName,
      items: cachedItems || []
    };
  }, [editingPoolInfo, allPoolContents, loadingPoolItems]);


  // --- Main Component Return to handle initialization error ---
  if (initializationError) {
    return (
      <div className="stock-pool-container" style={{ padding: '20px', textAlign: 'center' }}>
        <Result
          status="error"
          title="数据加载失败"
          subTitle={`无法加载股票池数据：${initializationError}`}
        />
      </div>
    );
  }

  // --- Main Component Return (Normal case) ---
  return (
    <div className="stock-pool-container">
      <div className="pool-header">
        <Tabs
          activeKey={activeTabKey}
          onChange={(key) => {
            setActiveTabKey(key as 'system' | 'user');
            setSelectedCategoryName(null); // Reset category selection on tab change
            setCurrentPoolItems([]); // Clear items
          }}
          tabBarStyle={{ marginBottom: 0 }}
          type="card"
          size="small"
        >
          <Tabs.TabPane tab="系统" key="system" disabled={!isDbReady} />
          <Tabs.TabPane tab="用户" key="user" disabled={!isDbReady} />
        </Tabs>
      </div>

      <div className="pool-content">
        {!isDbReady ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
            <Spin tip="初始化数据..." size="large" />
          </div>
        ) : loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
            <Spin tip="处理中..." size="large" />
          </div>
        ) : (
          <>
            <div style={{ padding: '12px' }}>
              {renderCategoryCards()}
            </div>
            {loadingPoolItems ? (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                <Spin tip="加载股票列表..." />
              </div>
            ) : (
              renderPoolList()
            )}
          </>
        )}
      </div>

      {/* --- Modals --- */}

      {/* Add/Edit Pool Name Modal (Simplified -> Restored TextArea for creation) */}
      <Modal
        title={editingPoolInfo ? `编辑股票池 "${editingPoolInfo.poolName}" 名称` : "添加新股票池"}
        open={editPoolModalVisible}
        onOk={handleSubmitPool}
        onCancel={() => setEditPoolModalVisible(false)}
        confirmLoading={loading} // Use global loading state
        destroyOnClose // Reset form state when modal closes
        width={600} // Increase width slightly for the textarea
      >
        <Form form={poolForm} layout="vertical" initialValues={{ name: editingPoolInfo?.poolName }}>
          <Form.Item
            name="name"
            label="股票池名称"
            rules={[{ required: true, message: '请输入股票池名称' }]}
          >
            <Input placeholder={editingPoolInfo ? '' : '例如：我的选股'} />
          </Form.Item>
          {/* --- Re-added TextArea for creation mode --- */}
          {!editingPoolInfo && (
            <Form.Item
              name="symbols_json"
              label={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                  <span>初始品种列表 (可选)</span>
                  <Tooltip title="复制AI提示词">
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      size="small"
                      onClick={(e) => {
                        e.preventDefault();
                        const aiPromptText = `把以上股票品种，按照下列规范输出为 JSON 数据格式，
1、代码格式，交易所简写.品类.代码，其中品类是 STOCK/ETF/FUTURE/FOREX/CRYPTO
2、输出为JSON数组，其中项目的格式：{symbol:全代码, name:品种名称}
3、注意交易所缩写必须是行业标准，例如 SSE、SZSE，而不是 SH、SZ这些`;
                        navigator.clipboard.writeText(aiPromptText).then(() => {
                          message.success('已复制AI提示词到剪贴板');
                        }).catch(() => {
                          message.error('复制失败，请手动复制');
                        });
                      }}
                    />
                  </Tooltip>
                </div>
              }
              extra={
                <div style={{ lineHeight: '1.2', fontSize: '12px', marginTop: '4px' }}>
                  <div>格式: JSON数组, 例: [{'{'}'symbol':'SSE.STOCK.600519', 'name':'贵州茅台'{'}'}]</div>
                  <div>代码格式请参考编辑列表时的提示</div>
                </div>
              }
            >
              <Input.TextArea
                rows={8} // Adjust rows as needed
                placeholder='粘贴符合格式的JSON数组，或留空以创建空列表'
              />
            </Form.Item>
          )}
          {/* --- End of Re-added TextArea --- */}
        </Form>
      </Modal>

      {/* Add/Edit Category Modal (Simplified) */}
      <Modal
        title={editingCategoryName ? `编辑分类 "${editingCategoryName}"` : "创建新分类"}
        open={editCategoryModalVisible}
        onOk={handleSubmitCategory}
        onCancel={() => setEditCategoryModalVisible(false)}
        confirmLoading={loading}
        destroyOnClose
      >
        <Form form={categoryForm} layout="vertical" initialValues={{ name: editingCategoryName }}>
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="例如：我的自选" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Stock List Editor (Modified props) */}
      <StockListEditor
        visible={stockListEditorVisible}
        onClose={() => setStockListEditorVisible(false)}
        // Pass combined structure and items
        pool={currentPoolForEditor}
        // Pass the updated save handler
        onSave={handleSaveStockList}
      />

      {/* Global Styles (Keep) */}
      <style dangerouslySetInnerHTML={{
        __html: `
        .stock-pool-table .ant-table-row:hover .hover-visible-button {
          opacity: 1;
        }
        .hover-visible-button {
          opacity: 0;
          transition: opacity 0.3s;
        }
        .table-actions-cell {
          text-align: center;
          white-space: nowrap;
        }
        .pool-name-link:hover {
          color: #1890ff;
        }
        /* 确保操作列中的按钮在hover时显示 */
        .stock-pool-table .ant-table-row:hover .table-actions-cell .hover-visible-button {
          opacity: 1;
        }
        .category-card {
            position: relative; /* Needed for absolute positioning of actions */
            min-height: 70px; /* Ensure a minimum height */
        }
         .category-card .ant-card-actions {
            position: absolute;
            bottom: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.85); /* Semi-transparent background */
            border-radius: 4px 0 4px 0;
            padding: 2px 4px;
            display: none; /* Hidden by default */
        }
        .category-card:hover .ant-card-actions {
            display: block; /* Show on hover */
        }
         .category-card .ant-card-actions > li {
             margin: 0 4px; /* Spacing between icons */
         }
         .category-card .ant-card-actions > li > span {
             font-size: 14px; /* Icon size */
             color: rgba(0, 0, 0, 0.65);
         }
         .category-card .ant-card-actions > li > span:hover {
             color: #1890ff;
         }
         /* Style for selected pool name */
         .selected-pool-name {
             color: #1890ff;
         }
         /* Ensure selected row style overrides hover */
         .ant-table-tbody > tr.selected-row > td {
             background: #e6f7ff !important;
         }
         .ant-table-tbody > tr.selected-row:hover > td {
             background: #dcf4ff !important; /* Slightly different hover for selected */
        }
        `
      }} />
    </div>
  );
};

export default StockPoolWidget;