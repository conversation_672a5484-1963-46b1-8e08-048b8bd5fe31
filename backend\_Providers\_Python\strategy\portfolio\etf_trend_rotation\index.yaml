# backend/_Providers/_Python/strategy/portfolio/etf_trend_rotation/index.yaml
# Translated from: 全球大类资产趋势轮动

strategy_id: "etf_trend_rotation"
strategy_name: "全球大类资产趋势轮动" # 策略显示名称

trading_type: "etf" # Assuming based on symbols，TODO: 考虑弃用，因为可能多品种组合

# Translated universe with WT format
universe:
  - SZSE.ETF.159509 # 纳指科技ETF
  - SSE.ETF.518880  # 黄金ETF
  - SSE.ETF.512480  # 半导体ETF
  - SZSE.ETF.159531 # 中证2000ETF
  - SSE.ETF.513100  # 纳指ETF
  - SSE.ETF.513520  # 日经ETF
  - SZSE.ETF.159857 # 光伏ETF
  - SSE.ETF.512100  # 中证1000ETF
  - SSE.ETF.513500  # 标普500ETF
  - SSE.ETF.588000  # 科创50ETF
  - SSE.ETF.513180  # 恒生科技指数ETF
  - SSE.ETF.513330  # 恒生互联网ETF
  - SZSE.ETF.162719 # 石油LOF

# Translated benchmark
benchmark: SSE.ETF.510300

is_for_stk: true # Assuming ETF 按股票交易单位处理

# --- 排序规则 ---
order_by:
  # Translated complex formula
  formula: "trend_score(close,25)*0.4+(roc(close,5)+roc(close,10))*0.2+ma(volume,5)/ma(volume,20) "
  sort_direction: descending # is_desc = true

# --- 调仓与执行规则 ---
rebalance_interval: daily # algo = "RunDaily"

top_n: 1 # topK = 1

weighting_scheme: equal # algo = "WeighEqually"

# --- 卖出规则 ---
sell_rules:
  # Translated sell rule (requires roc(close, 18) factor)
  formulas: 
    - "roc(close, 21) > 0.17"
  # Note: sell_at_least_count has no direct equivalent in this framework
  at_least_count: 1

# --- 买入规则 ---


# --- 数据参数 ---
data_freq: day   # Implied by algo = "RunDaily"
# Adjust bar_count based on the maximum period needed by any factor in formulas
# max(26, 5, 10, 5, 20, 18) = 26. Add buffer.
bar_count: 35    # Needs at least 26+1=27 for trend_score(26)

# --- 数据存储配置 (Kept from original, adjust if needed) ---
data:
  path: ../../storage/ # 相对于策略目录的路径
  mode: csv

# --- 回测参数 ---
backtest:
  stime: 202309190930 # Translated start_date with added time
  etime:  # end_date was empty, so keep etime empty (uses current time)
  initial_capital: 500000 # Kept from original, adjust if needed
  commission_rate: 0.0002 # Kept from original, adjust if needed
  slippage_rate: 0.0001   # Kept from original, adjust if needed