// frontend/src/_Pages/main/components/DrawingTools/index.tsx
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Button } from 'antd';
import { IChartApi, Time, ISeriesApi, LineStyle, LineWidth, CandlestickData, SeriesMarker, BusinessDay } from 'lightweight-charts';
import { EventBus } from '@/events/eventBus';
import { ChartEvents } from '@/events/events';
import { useAtom } from 'jotai';
import { currentSymbolAtom, currentPeriodAtom } from '@/store/state';
import { DrawingLine, Point, LinePointType } from '@/shared_types/chart';
import { Symbol, KLineInterval } from '@/shared_types/market';
import { useTheme } from '@/models/useTheme';
import { ToolbarButtonType } from '@/shared_types/ui';

interface DrawingToolsProps {
  chartInstance: IChartApi | null;
  candleSeries: ISeriesApi<'Candlestick'> | null;
  activeToolbarButton: ToolbarButtonType;
  onToolbarButtonChange: (buttonType: ToolbarButtonType) => void;
}

interface DrawPoint {
  time: Time;
  price: number;
}

interface PreviewLineData {
  startPoint: Point | null;
  currentPoint: Point | null;
  secondPoint: Point | null;
  visible: boolean;
  toolType: DrawingToolType;
  stage: 'first' | 'second' | 'third' | null;
}

type DrawingToolType = 'segment' | 'ray' | 'horizontal' | 'vertical' | 'caisenmeasurement' | 'line' | null;

// 比较两个周期的大小
const comparePeriods = (period1: KLineInterval, period2: KLineInterval): number => {
  const periodValues: { [key in KLineInterval]: number } = {
    [KLineInterval.MIN1]: 1,
    [KLineInterval.MIN5]: 5,
    [KLineInterval.MIN15]: 15,
    [KLineInterval.MIN30]: 30,
    [KLineInterval.HOUR1]: 60,
    [KLineInterval.HOUR4]: 240,
    [KLineInterval.DAY1]: 1440,
    [KLineInterval.WEEK1]: 10080
  };

  return periodValues[period1] - periodValues[period2];
};

const DrawingTools: React.FC<DrawingToolsProps> = ({ 
  chartInstance, 
  candleSeries, 
  activeToolbarButton, 
  onToolbarButtonChange 
}) => {
  const [toolbarVisible, setToolbarVisible] = useState(false);
  const [activeTool, setActiveTool] = useState<DrawingToolType>(null);
  const [currentSymbol] = useAtom(currentSymbolAtom);
  const [currentPeriod] = useAtom(currentPeriodAtom);
  const drawingLinesMap = useRef<Map<number, ISeriesApi<'Line'>>>(new Map());
  const setPoint = useRef(0);
  const [hoveredLine, setHoveredLine] = useState<DrawingLine | null>(null);
  const [hoverPosition, setHoverPosition] = useState<{ x: number; y: number } | null>(null);
  const drawingLineCache = useRef<Map<number, DrawingLine>>(new Map());
  const { theme, isDarkMode } = useTheme();
  const previewLine = useRef<ISeriesApi<'Line'> | null>(null);
  const secondPreviewLine = useRef<ISeriesApi<'Line'> | null>(null);
  // 添加一个新的 ref 变量来记录当前鼠标悬停的线条
  const hoveredLineRef = useRef<DrawingLine | null>(null);
  // 将选中的线条从ref变量改为状态变量
  const [selectedDrawingLine, setSelectedDrawingLine] = useState<DrawingLine | null>(null);

  // Convert state variables to refs
  const startPointRef = useRef<DrawPoint | null>(null);
  const isDrawingRef = useRef<boolean>(false);
  const isDraggingRef = useRef<boolean>(false);
  const dragPointRef = useRef<'start' | 'end' | 'second' | null>(null);
  const selectedLineRef = useRef<DrawingLine | null>(null);
  const drawingStageRef = useRef<'first' | 'second' | 'third' | null>(null);
  const secondPointRef = useRef<Point | null>(null);
  // 添加上一次点击时间的ref
  const lastClickTimeRef = useRef<number>(0);

  // 添加预览线状态
  const [previewLineData, setPreviewLineData] = useState<PreviewLineData>({
    startPoint: null,
    currentPoint: null,
    secondPoint: null,
    visible: false,
    toolType: null,
    stage: null
  });

  // 根据主题设置画线颜色
  const defaultColor = isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)';

  // 判断画线工具是否处于激活状态
  const isDrawingActive = activeToolbarButton === ToolbarButtonType.DRAWING;

  // 修改按钮点击处理函数
  const handleDrawingButtonClick = useCallback(() => {
    // 触发互斥按钮状态变更
    onToolbarButtonChange(ToolbarButtonType.DRAWING);
    
    // 如果画线工具已激活，则隐藏工具栏
    if (isDrawingActive) {
      setToolbarVisible(false);
    } else {
      // 否则显示工具栏
      setToolbarVisible(true);
    }
  }, [isDrawingActive, onToolbarButtonChange]);

  // 处理激活状态变化
  useEffect(() => {
    // 如果画线工具不再激活，则关闭工具栏
    if (!isDrawingActive && toolbarVisible) {
      setToolbarVisible(false);
      // 清除当前激活的工具
      setActiveTool(null);
      isDrawingRef.current = false;
      startPointRef.current = null;
      setPoint.current = 0;
      setPreviewLineData({
        startPoint: null,
        currentPoint: null,
        secondPoint: null,
        visible: false,
        toolType: null,
        stage: null
      });
    }
  }, [isDrawingActive]);

  // 清除所有画线
  const clearDrawingLines = useCallback(() => {
    console.log('[Drawing] 开始清除所有画线，当前画线数量:', drawingLinesMap.current.size);
  
    // 1. 清除所有图表线段
    drawingLinesMap.current.forEach((series, id) => {
      if (chartInstance) {
        console.log('[Drawing] 清除画线 ID:', id);
        chartInstance.removeSeries(series);
      }
    });
    
    // 2. 重置所有映射表
    drawingLinesMap.current.clear();
    derivedLinesMap.current.clear();   // 新增：清理衍生线关系
    horizontalLinesMap.current.clear();// 新增：清理横线关系
    drawingLineCache.current.clear();  // 清理缓存
    hoveredLineRef.current = null;     // 清除悬停的线条引用
  
    // 3. 清除预览线
    if (previewLineData.visible && chartInstance) {
      console.log('[Drawing] 清除预览线');
      const series = previewLineData.toolType === 'segment' 
        ? drawingLinesMap.current.get(0) 
        : drawingLinesMap.current.get(1);
      if (series) {
        chartInstance.removeSeries(series);
      }
      setPreviewLineData({
        startPoint: null,
        currentPoint: null,
        secondPoint: null,
        visible: false,
        toolType: null,
        stage: null
      });
    }
  }, [chartInstance]);

  /**
   * 在图表上创建画线
   * @param drawingLine 画线数据
   * @param chartInstance 图表实例
   * @param candleSeries 蜡烛图系列
   * @returns 创建的线段系列或null（如果创建失败）
   */
  const createChartLine = (
    drawingLine: DrawingLine,
    chartInstance: IChartApi,
    candleSeries: ISeriesApi<'Candlestick'>
  ) => {
    try {
      const lineSeries = chartInstance.addLineSeries({
        color: drawingLine.color,
        lineWidth: drawingLine.lineWidth as LineWidth,
        lineStyle: drawingLine.type === DrawingToolType.PRICE_CHANNEL ? 0 : 1,
        crosshairMarkerVisible: false,
        lastPriceAnimation: 0,
        priceLineVisible: false,
        lastValueVisible: false,
      });

      let lineData: { time: Time; value: number }[] = [];

      switch (drawingLine.points.pointsType) {
        case LinePointType.SinglePoint: {
          // 水平线只需要一个点
          const point = drawingLine.points.point;
          lineData = [
            { time: chartInstance.timeScale().getVisibleRange()?.from || point.time, value: point.price },
            { time: chartInstance.timeScale().getVisibleRange()?.to || point.time, value: point.price }
          ];
          break;
        }
        case LinePointType.TwoPoints: {
          // 两点线段
          const { startPoint, endPoint } = drawingLine.points;
          lineData = [
            { time: startPoint.time as Time, value: startPoint.price },
            { time: endPoint.time as Time, value: endPoint.price }
          ];
          break;
        }
        case LinePointType.ThreePoints: {
          // 三点线段
          const { startPoint, endPoint, posPoint } = drawingLine.points;
          // 计算三点的位置关系
          const startTime = startPoint.time as number;
          const endTime = endPoint.time as number;
          const posTime = posPoint.time as number;

          // 确保时间顺序：startTime < endTime < posTime
          if (startTime < endTime && endTime < posTime) {
            lineData = [
              { time: startPoint.time, value: startPoint.price },
              { time: endPoint.time, value: endPoint.price },
              { time: posPoint.time, value: posPoint.price }
            ];
          } else {
            console.error('Invalid time order in three-point line');
            return null;
          }
          break;
        }
      }

      lineSeries.setData(lineData);

      // 添加端点标记
      //addEndpointMarkers(drawingLine, lineSeries);

      // 同时更新两个缓存
      drawingLinesMap.current.set(drawingLine.id, lineSeries);
      drawingLineCache.current.set(drawingLine.id, drawingLine);

      console.log(`[Drawing] 成功创建画线 ID:${drawingLine.id}, 类型:${drawingLine.lineType}`);
      return lineSeries;
    } catch (error) {
      console.error('[Drawing] 创建画线失败:', error);
      return null;
    }
  };

  // 处理画线数据就绪
  // 增强的 handleDrawingLinesReady
  const handleDrawingLinesReady = useCallback((payload: ChartEvents.DrawingLinesReadyPayload) => {
    console.log('[Drawing] 收到画线数据，数量:', payload.drawingLines.length);

    if (!chartInstance || !candleSeries) {
      console.log('[Drawing] 图表实例或蜡烛图系列不存在，无法处理画线数据');
      return;
    }

    clearDrawingLines();

    const filteredLines = payload.drawingLines.filter(line => {
      if (comparePeriods(line.interval, currentPeriod) < 0) {
        console.log(`[Drawing] 跳过画线 ID:${line.id}，周期 ${line.interval} 小于当前周期 ${currentPeriod}`);
        return false;
      }
      return true;
    });

    console.log('[Drawing] 过滤后的画线数量:', filteredLines.length);

    let successCount = 0;
    let failureCount = 0;

    filteredLines.forEach(line => {
      console.log('[Drawing] 处理画线数据:', line.id, line.lineType);

      // 原始创建逻辑
      const lineSeries = createChartLine(line, chartInstance, candleSeries);

      if (lineSeries) {
        successCount++;

        // 新增：处理衍生线段（仅限caisenmeasurement类型）
        if (line.lineType === 'caisenmeasurement' && line.points.pointsType === LinePointType.ThreePoints) {
          const [p1, p2, p3] = [
            line.points.startPoint,
            line.points.endPoint,
            line.points.posPoint
          ];
          const priceDiff = p2.price - p1.price;

          // 生成两条衍生线段
          const derivedIds = [1, 2].map(step => {
            const derivedLine = createDerivedLine(p3, step, priceDiff/*, line.id*/);
            const series = createChartLine(derivedLine, chartInstance!, candleSeries!);
            if (series) {
              drawingLinesMap.current.set(derivedLine.id, series);
              drawingLineCache.current.set(derivedLine.id, derivedLine);
              return derivedLine.id;
            }
            return -1;
          }).filter(id => id !== -1);

          derivedLinesMap.current.set(line.id, derivedIds);
        }
      } else {
        failureCount++;
        console.error(`[Drawing] 画线创建失败 ID:${line.id}`);
      }
    });

    console.log(`[Drawing] 画线处理完成 - 成功:${successCount}, 失败:${failureCount}`);

    if (failureCount > 0) {
      console.log('[Drawing] 画线加载失败', failureCount);
    }
  }, [chartInstance, candleSeries, clearDrawingLines, currentPeriod]);

  // 清除预览线
  const clearPreviewLine = useCallback(() => {
    if (previewLineData.visible && chartInstance) {
      const series = previewLineData.toolType === 'segment' ? drawingLinesMap.current.get(0) : drawingLinesMap.current.get(1);
      if (series) {
        chartInstance.removeSeries(series);
      }
      setPreviewLineData({
        startPoint: null,
        currentPoint: null,
        secondPoint: null,
        visible: false,
        toolType: null,
        stage: null
      });
    }
  }, [chartInstance]);

  // 更新预览线
  const updatePreviewLine = useCallback((currentPoint: DrawPoint) => {
    if (!chartInstance || !startPointRef.current || !candleSeries) return;

    // 如果预览线不存在，创建它
    if (!previewLineData.visible) {
      setPreviewLineData({
        startPoint: startPointRef.current,
        currentPoint: currentPoint,
        secondPoint: null,
        visible: true,
        toolType: activeTool,
        stage: null
      });
    }

    // 根据工具类型设置预览线数据点
    let dataPoints: { time: Time; value: number }[] = [];

    // 获取当前可见的时间范围
    const timeRange = chartInstance.timeScale().getVisibleRange();
    if (!timeRange) return;

    switch (activeTool) {
      case 'segment':
        // 线段
        dataPoints = [
          { time: startPointRef.current.time as Time, value: startPointRef.current.price },
          { time: currentPoint.time as Time, value: currentPoint.price }
        ];
        break;
      case 'ray':
        // 射线
        const slope = (currentPoint.price - startPointRef.current.price) /
          ((currentPoint.time as number) - (startPointRef.current.time as number));
        const endPrice = startPointRef.current.price + slope * (timeRange.to as number - (startPointRef.current.time as number));
        dataPoints = [
          { time: startPointRef.current.time as Time, value: startPointRef.current.price },
          { time: timeRange.to as Time, value: endPrice }
        ];
        break;
      case 'horizontal':
        // 水平线：不需要绘制预览线，直接调用 addPriceLine
        candleSeries.createPriceLine({
          price: startPointRef.current.price,
          color: '#FF0000',
          lineWidth: 1,
          lineStyle: LineStyle.Dashed,
          axisLabelVisible: false,
          title: '',
        });
        return; // 直接返回，不需要设置数据点
    }

    if (dataPoints.length === 2) {
      try {
        // 确保时间点是升序的
        if (dataPoints[0].time > dataPoints[1].time) {
          [dataPoints[0], dataPoints[1]] = [dataPoints[1], dataPoints[0]];
        }
        setPreviewLineData({
          startPoint: startPointRef.current,
          currentPoint: currentPoint,
          secondPoint: null,
          visible: true,
          toolType: activeTool,
          stage: null
        });
      } catch (error) {
        console.error('Failed to set preview line data:', error);
        clearPreviewLine();
      }
    }
  }, [chartInstance, startPointRef, activeTool, candleSeries, clearPreviewLine]);

  // 清除图表上的所有画线
  const handleClearDrawingLines = useCallback(() => {
    clearDrawingLines();
  }, [clearDrawingLines]);

  useEffect(() => {
    const subscription = EventBus.on(
      ChartEvents.Types.CLEAR_CHART_DRAWING_LINES,
      handleClearDrawingLines
    );

    return () => {
      console.log('[Drawing] 卸载清除图表画线事件订阅');
      subscription.unsubscribe();
    };
  }, [handleClearDrawingLines]);

  // 预览线渲染的 useEffect
  useEffect(() => {
    if (!chartInstance || !candleSeries || !previewLineData.visible) {
      return;
    }

    // 清除现有的预览线
    if (previewLine.current) {
      chartInstance.removeSeries(previewLine.current);
      previewLine.current = null;
    }
    if (secondPreviewLine.current) {
      chartInstance.removeSeries(secondPreviewLine.current);
      secondPreviewLine.current = null;
    }

    // 根据阶段创建预览线
    switch (previewLineData.stage) {
      case 'first': {
        // 第一阶段：只有当有起点和当前点时才显示预览线
        if (previewLineData.startPoint && previewLineData.currentPoint) {
          previewLine.current = chartInstance.addLineSeries({
            color: defaultColor,
            lineWidth: 1,
            lineStyle: 1, // 虚线
            lastPriceAnimation: 0,
            priceLineVisible: false,
          });
          previewLine.current.setData([
            { time: previewLineData.startPoint.time, value: previewLineData.startPoint.price },
            { time: previewLineData.currentPoint.time, value: previewLineData.currentPoint.price }
          ]);
        }
        break;
      }
      case 'second': {
        // 第二阶段：显示已确认的第一条线，只有当有第三个点时才显示第二条预览线
        if (previewLineData.startPoint && previewLineData.secondPoint) {
          // 第一条确认的线
          previewLine.current = chartInstance.addLineSeries({
            color: defaultColor,
            lineWidth: 1,
            lineStyle: 0, // 实线
            lastPriceAnimation: 0,
            priceLineVisible: false,
          });
          previewLine.current.setData([
            { time: previewLineData.startPoint.time, value: previewLineData.startPoint.price },
            { time: previewLineData.secondPoint.time, value: previewLineData.secondPoint.price }
          ]);

          // 只有当有第三个点时才显示第二条预览线
          if (previewLineData.currentPoint) {
            secondPreviewLine.current = chartInstance.addLineSeries({
              color: defaultColor,
              lineWidth: 1,
              lineStyle: 1, // 虚线
              lastPriceAnimation: 0,
              priceLineVisible: false,
            });
            secondPreviewLine.current.setData([
              { time: previewLineData.secondPoint.time, value: previewLineData.secondPoint.price },
              { time: previewLineData.currentPoint.time, value: previewLineData.currentPoint.price }
            ]);
          }
        }
        break;
      }
      case 'third': {
        // 第三阶段：显示完整的三点线
        if (previewLineData.startPoint && previewLineData.secondPoint && previewLineData.currentPoint) {
          previewLine.current = chartInstance.addLineSeries({
            color: defaultColor,
            lineWidth: 1,
            lineStyle: 0, // 实线
            lastPriceAnimation: 0,
            priceLineVisible: false,
          });
          previewLine.current.setData([
            { time: previewLineData.startPoint.time, value: previewLineData.startPoint.price },
            { time: previewLineData.secondPoint.time, value: previewLineData.secondPoint.price },
            { time: previewLineData.currentPoint.time, value: previewLineData.currentPoint.price }
          ]);
        }
        break;
      }
    }

    return () => {
      if (previewLine.current) {
        chartInstance.removeSeries(previewLine.current);
        previewLine.current = null;
      }
      if (secondPreviewLine.current) {
        chartInstance.removeSeries(secondPreviewLine.current);
        secondPreviewLine.current = null;
      }
    };
  }, [chartInstance, candleSeries, previewLineData]);

  // 订阅画线数据就绪事件
  useEffect(() => {
    const subscription = EventBus.on(
      ChartEvents.Types.APPLY_DRAWING_LINES,
      handleDrawingLinesReady
    );

    return () => {
      console.log('[Drawing] 卸载画线数据就绪事件订阅');
      subscription.unsubscribe();
    };
  }, [handleDrawingLinesReady, clearDrawingLines]);

  // 检查点是否在线段附近
  const isNearLine = (point: DrawPoint, line: DrawingLine): boolean => {
    if (!chartInstance || !candleSeries) return false;

    if (line.lineType === 'horizontal') {
      // 对于水平线，只需要检查价格是否在附近
      const points = line.points as { pointsType: LinePointType.SinglePoint; point: Point };
      const priceDiff = Math.abs(point.price - points.point.price);
      return priceDiff <= 3 * candleSeries.priceToCoordinate(1)!; // 10像素的检测范围
    }

    const points = line.points as { pointsType: LinePointType.TwoPoints; startPoint: Point; endPoint: Point };
    const timeScale = chartInstance.timeScale();
    const startXCoord = timeScale.timeToCoordinate(points.startPoint.time as Time);
    const startYCoord = candleSeries.priceToCoordinate(points.startPoint.price);
    const endXCoord = timeScale.timeToCoordinate(points.endPoint.time as Time);
    const endYCoord = candleSeries.priceToCoordinate(points.endPoint.price);
    const pointXCoord = timeScale.timeToCoordinate(point.time as Time);
    const pointYCoord = candleSeries.priceToCoordinate(point.price);

    if (startXCoord === null || startYCoord === null ||
      endXCoord === null || endYCoord === null ||
      pointXCoord === null || pointYCoord === null) return false;

    // 计算点到线段的距离
    const lineLength = Math.sqrt(
      Math.pow(endXCoord - startXCoord, 2) +
      Math.pow(endYCoord - startYCoord, 2)
    );

    if (lineLength === 0) return false;

    const distance = Math.abs(
      (endYCoord - startYCoord) * pointXCoord -
      (endXCoord - startXCoord) * pointYCoord +
      endXCoord * startYCoord -
      endYCoord * startXCoord
    ) / lineLength;

    return distance <= 3; // 5像素的检测范围
  };

  // 检查点是否在端点附近
  const isNearEndpoint = (point: DrawPoint, endpoint: DrawPoint): boolean => {
    if (!chartInstance || !candleSeries) return false;

    const timeScale = chartInstance.timeScale();
    const xCoord = timeScale.timeToCoordinate(endpoint.time as Time);
    const yCoord = candleSeries.priceToCoordinate(endpoint.price);

    if (xCoord === null || yCoord === null) return false;

    const pointXCoord = timeScale.timeToCoordinate(point.time as Time);
    const pointYCoord = candleSeries.priceToCoordinate(point.price);

    if (pointXCoord === null || pointYCoord === null) return false;

    const distance = Math.sqrt(
      Math.pow(xCoord - pointXCoord, 2) +
      Math.pow(yCoord - pointYCoord, 2)
    );

    return distance <= 10
  };

  // 修改setChartInteraction函数实现
  const setChartInteraction = useCallback((enabled: boolean) => {
    if (chartInstance) {
      console.log('[Drawing] ' + (enabled ? '启用' : '禁用') + '图表交互');
      // 注意：这里参数取反，当enabled=false时实际设置handleScroll: false
      chartInstance.applyOptions({
        handleScroll: enabled,
        handleScale: enabled, // 需要同时控制缩放
        kineticScroll: {
          touch: enabled,
          mouse: enabled
        }
      });
    }
  }, [chartInstance]);

  // 检查时间点顺序
  const isValidTimeOrder = useCallback((newPoint: Point, stage: 'second' | 'third'): boolean => {
    if (stage === 'second' && startPointRef.current) {
      return (newPoint.time as number) > (startPointRef.current.time as number);
    } else if (stage === 'third' && secondPointRef.current) {
      return (newPoint.time as number) > (secondPointRef.current.time as number);
    }
    return false;
  }, []);

  // 清除画线状态的辅助函数
  const clearDrawingState = useCallback(() => {
    startPointRef.current = null;
    secondPointRef.current = null;
    isDrawingRef.current = false;
    drawingStageRef.current = null;
    setActiveTool(null);
    setPoint.current = 0;
    // 清除悬停的线条引用
    hoveredLineRef.current = null;
    // 不清除选中的线条引用，因为这个函数主要用于清除画线过程中的状态
    setPreviewLineData({
      startPoint: null,
      currentPoint: null,
      secondPoint: null,
      visible: false,
      toolType: null,
      stage: null
    });
  }, []);

  // 增强parseTime函数（处理所有时间类型）
  const parseTime = (time: Time): number => {
    if (typeof time === 'number') return time;
    if (typeof time === 'string') return Date.parse(time);
    if ('year' in time) {
      const businessDay = time as BusinessDay;
      // 4. 确保数值类型运算
      return Date.UTC(
        Number(businessDay.year),
        Number(businessDay.month) - 1,
        Number(businessDay.day)
      );
    }
    return Date.now();
  };

  // 处理鼠标事件
  const handleMouseEvent = useCallback((event: ChartEvents.ChartMouseEvent) => {
    if (!chartInstance || !candleSeries || !currentSymbol || !currentPeriod) return;

    const coordinate = chartInstance.timeScale().coordinateToTime(event.x);
    const price = candleSeries.coordinateToPrice(event.y);

    if (!coordinate || price === null) return;

    const point = { time: coordinate, price };

    switch (event.type) {

      case 'mousedown':
        // 检查点击时间间隔
        const currentTime = Date.now();
        if (currentTime - lastClickTimeRef.current < 300) {
          console.log('[Drawing] 忽略快速重复点击');
          return;
        }
        lastClickTimeRef.current = currentTime;

        // 如果不是在画线状态，检查是否有悬停的线条，有则设置为选中线条，没有则清空选中线条
        if (!isDrawingRef.current && activeTool === null) {
          if (hoveredLineRef.current) {
            console.log('[Drawing] 选择线条:', hoveredLineRef.current.id);
            setSelectedDrawingLine(hoveredLineRef.current);
          } else {
            console.log('[Drawing] 清除选择的线条');
            setSelectedDrawingLine(null);
          }
        }

        // 画线的第二、第三次点击检查，优先级高于端点点击

        if (drawingStageRef.current === 'first') {
          // 检查第二个点的时间是否晚于第一个点
          if (!isValidTimeOrder(point, 'second')) {
            console.log('[Drawing] 第二个点时间早于第一个点，取消画线');
            clearDrawingState();
            return;
          }

          console.log('[Drawing] 设置第二个点:', point);
          secondPointRef.current = point;

          // 如果 caisenmeasurement 则继续第三个点，否则结束
          if (activeTool === 'caisenmeasurement') {

            drawingStageRef.current = 'second'

            setPreviewLineData(prev => ({
              ...prev,
              secondPoint: point,
              currentPoint: null,  // 重置第三个点为空
              stage: 'second'
            }));

          } else {
            // 划线结束，退出画线，保存画线
            const drawingLine: DrawingLine = {
              id: 0,
              symbol: currentSymbol,
              interval: currentPeriod,
              points: {
                pointsType: LinePointType.TwoPoints,
                startPoint: startPointRef.current!,
                endPoint: secondPointRef.current!
              },
              lineType: activeTool,
              lineWidth: 1,
              lineStyle: 'solid',
              color: defaultColor,
              textInfo: '',
              textPosition: 'middle'
            };

            console.log('[Drawing] 发送添加画线事件');
            EventBus.emit(ChartEvents.Types.ADD_DRAWING_LINE, { drawingLine });

            createChartLine(drawingLine, chartInstance, candleSeries);
            clearDrawingState();

            drawingStageRef.current = null;
          }


        } else if (drawingStageRef.current === 'second') {
          // 检查第三个点的时间是否晚于第二个点
          if (!isValidTimeOrder(point, 'third')) {
            console.log('[Drawing] 第三个点时间早于第二个点，取消画线');
            clearDrawingState();
            return;
          }

          // 完成三点画线
          console.log('[Drawing] 完成画线，第三个点:', point);
          const drawingLine: DrawingLine = {
            id: 0,
            symbol: currentSymbol,
            interval: currentPeriod,
            points: {
              pointsType: LinePointType.ThreePoints,
              startPoint: startPointRef.current!,
              endPoint: secondPointRef.current!,
              posPoint: point
            },
            lineType: activeTool,
            lineWidth: 1,
            lineStyle: 'solid',
            color: defaultColor,
            textInfo: '',
            textPosition: 'middle'
          };

          console.log('[Drawing] 发送添加画线事件');
          EventBus.emit(ChartEvents.Types.ADD_DRAWING_LINE, { drawingLine });

          createChartLine(drawingLine, chartInstance, candleSeries);
          clearDrawingState();

          return;
        }

        // 首先检查是否点击了端点，无论是否有激活的工具
        console.log('[Drawing] 检查是否点击了端点');
        let endpointClicked = false;
        drawingLinesMap.current.forEach((lineSeries, id) => {
          const line = drawingLineCache.current.get(id);
          if (line && isNearLine(point, line) && line.points.pointsType === LinePointType.TwoPoints) {
            // 如果鼠标在线上，检查是否在端点附近
            // 在mousedown事件处理中添加严格模式检查
            if (isNearEndpoint(point, line.points.startPoint)) {
              console.log('[Drawing] 点击到起始端点，开始拖拽');
              isDraggingRef.current = true;
              dragPointRef.current = 'start';
              selectedLineRef.current = line;
              endpointClicked = true;

              // 添加双重禁用保证
              setChartInteraction(false);
              chartInstance?.applyOptions({
                handleScroll: false,
                handleScale: false
              });
              return;
            }

            if (isNearEndpoint(point, line.points.endPoint)) {
              console.log('[Drawing] 点击到结束端点，开始拖拽');
              isDraggingRef.current = true;
              dragPointRef.current = 'end';
              selectedLineRef.current = line;
              endpointClicked = true;
              setChartInteraction(false);
              return;
            }
          }
        });

        // --------- 如果点击了端点，不继续处理其他画线操作 ---------

        if (!endpointClicked) {
          // 如果没有点击端点且有激活的工具，继续处理画线操作
          if (activeTool) {
            if (activeTool === 'horizontal') {
              console.log('[Drawing] 开始绘制水平线，价格:', point.price);

              const drawingLine: DrawingLine = {
                id: 0,
                symbol: currentSymbol,
                interval: currentPeriod,
                points: {
                  pointsType: LinePointType.SinglePoint,
                  point: point
                },
                lineType: activeTool,
                lineWidth: 1,
                lineStyle: 'solid',
                color: defaultColor,
                textInfo: '',
                textPosition: 'middle'
              };

              console.log('[Drawing] 发送添加水平线事件');
              EventBus.emit(ChartEvents.Types.ADD_DRAWING_LINE, {
                drawingLine
              });

              setActiveTool(null);
              isDrawingRef.current = false;
              setPoint.current = 0;
              setPreviewLineData({
                startPoint: null,
                currentPoint: null,
                secondPoint: null,
                visible: false,
                toolType: null,
                stage: null
              });

              createChartLine(drawingLine, chartInstance, candleSeries);

            } else if (!isDrawingRef.current) {
              // 开始画线，设置第一个点
              console.log('[Drawing] 开始画线，第一个点:', point);
              startPointRef.current = point;
              isDrawingRef.current = true;
              drawingStageRef.current = 'first';
              setPreviewLineData({
                startPoint: point,
                currentPoint: null,  // 初始不设置第二个点
                secondPoint: null,
                visible: true,
                toolType: activeTool,
                stage: 'first'
              });
            } 

            // 第二次、第三次点击放到最前方，因为此时不允许拖动端点
            
          }
        }
        break;

      // 在mousemove事件处理中修复拖拽更新逻辑
      case 'mousemove':
        // 修改后的拖拽处理逻辑
        if (isDraggingRef.current && selectedLineRef.current && dragPointRef.current) {
          const updatedLine = { ...selectedLineRef.current };
          const lineSeries = drawingLinesMap.current.get(updatedLine.id);

          if (lineSeries && updatedLine.points.pointsType === LinePointType.TwoPoints) {
            // 获取当前坐标对应的时间（保持原始Time类型）
            const currentTime = chartInstance.timeScale().coordinateToTime(event.x);
            const currentPrice = candleSeries.coordinateToPrice(event.y);

            if (!currentTime || currentPrice === null) return;

            // 转换对比时间为时间戳（仅用于校验）
            const newTime = parseTime(currentTime);
            const otherTime = dragPointRef.current === 'start'
              ? parseTime(updatedLine.points.endPoint.time)
              : parseTime(updatedLine.points.startPoint.time);

            // 在更新前进行校验
            const isValid = dragPointRef.current === 'start'
              ? newTime < otherTime
              : newTime > otherTime;

            if (isValid) {
              // 使用原始Time类型更新点（不要转换类型）
              if (dragPointRef.current === 'start') {
                updatedLine.points.startPoint = {
                  time: currentTime,  // 保持原始Time类型
                  price: currentPrice
                };
              } else {
                updatedLine.points.endPoint = {
                  time: currentTime,
                  price: currentPrice
                };
              }

              // 排序时保持Time类型（不要转换时间戳）
              const sortedData = [
                {
                  time: updatedLine.points.startPoint.time,
                  value: updatedLine.points.startPoint.price
                },
                {
                  time: updatedLine.points.endPoint.time,
                  value: updatedLine.points.endPoint.price
                }
              ].sort((a, b) => parseTime(a.time) - parseTime(b.time));

              // 强制更新线段
              lineSeries.setData(sortedData);
              addEndpointMarkers(updatedLine, lineSeries);

              // 更新引用
              selectedLineRef.current = updatedLine;
              drawingLineCache.current.set(updatedLine.id, updatedLine);
            }
          }
        }

        else if (isDrawingRef.current) {
          if (drawingStageRef.current === 'first') {
            // 第一阶段：只有当当前点晚于起始点时才更新预览线
            if ((point.time as number) > (startPointRef.current!.time as number)) {
              setPreviewLineData(prev => ({
                ...prev,
                currentPoint: point
              }));
            }
          } else if (drawingStageRef.current === 'second') {
            // 第二阶段：只有当当前点晚于第二个点时才更新预览线
            if ((point.time as number) > (secondPointRef.current!.time as number)) {
              setPreviewLineData(prev => ({
                ...prev,
                currentPoint: point
              }));
            }
          }
        } else {
          // 先清除所有线段的标记
          drawingLinesMap.current.forEach((lineSeries) => {
            lineSeries.setMarkers([]);
          });

          let foundLine = false;

          drawingLinesMap.current.forEach((lineSeries, id) => {
            const line = drawingLineCache.current.get(id);

            // 检查是否为延伸线或横线
            const isDerivedLine = Array.from(derivedLinesMap.current.values()).some(
              derivedIds => derivedIds.includes(id)
            );
            const isHorizontalLine = Array.from(horizontalLinesMap.current.values()).some(
              horizontalIds => horizontalIds.includes(id)
            );

            // 如果是当前选中的线条，显示端点标记（但不包括延伸线和横线）
            if (line && selectedDrawingLine && line.id === selectedDrawingLine.id && !isDerivedLine && !isHorizontalLine) {
              addEndpointMarkers(line, lineSeries);
            }

            // 选择画线的时候禁止更新悬浮状态
            // 只对非延伸线和非横线进行鼠标交互
            if (line && isNearLine(point, line) && activeTool === null && !isDerivedLine && !isHorizontalLine) {
              // 仅当悬停时添加标记
              addEndpointMarkers(line, lineSeries);
              foundLine = true;

              // 更新悬浮状态
              if (!hoveredLine || hoveredLine.id !== line.id) {
                setHoveredLine(line);
                setHoverPosition({ x: event.x, y: event.y });
              }
              
              // 更新当前悬停的线条引用
              hoveredLineRef.current = line;
            }
          });

          // 更新或清除悬浮状态
          if (!foundLine) {
            setHoveredLine(null);
            setHoverPosition(null);
            // 清除当前悬停的线条引用
            hoveredLineRef.current = null;
          }
        }
        break;

      case 'mouseup':
        if (isDraggingRef.current && selectedLineRef.current) {
          // 先保存当前选中的线引用
          const currentLine = selectedLineRef.current;

          console.log('[Drawing] 拖拽结束，保存画线更新');
          // 发送更新事件时使用保存的引用
          EventBus.emit(ChartEvents.Types.UPDATE_DRAWING_LINE, {
            drawingLine: currentLine
          });

          console.log('[Drawing] 重置拖拽状态');
          isDraggingRef.current = false;
          dragPointRef.current = null;
          selectedLineRef.current = null; // 此时再置空

          // 恢复图表交互
          setChartInteraction(true);

          // 获取当前鼠标位置下的线段
          const lineSeries = drawingLinesMap.current.get(currentLine.id);
          if (lineSeries && isNearLine(point, currentLine)) {
            addEndpointMarkers(currentLine, lineSeries);
          } else if (lineSeries) {
            lineSeries.setMarkers([]);
          }
        }
        break;
    }
  }, [chartInstance, candleSeries, currentSymbol, currentPeriod, activeTool, hoveredLine, setChartInteraction, isValidTimeOrder, clearDrawingState, selectedDrawingLine]);

  // 订阅图表鼠标事件
  useEffect(() => {
    const subscription = EventBus.on(
      ChartEvents.Types.CHART_MOUSE_EVENT,
      handleMouseEvent
    );
    return () => {
      subscription.unsubscribe();
      clearPreviewLine();
    };
  }, [handleMouseEvent, clearPreviewLine]);

  // 处理工具按钮点击
  const handleToolClick = (toolType: DrawingToolType) => {
    if (toolType === activeTool) {
      setActiveTool(null);
      isDrawingRef.current = false;
      startPointRef.current = null;
      setPoint.current = 0;
      setPreviewLineData({
        startPoint: null,
        currentPoint: null,
        secondPoint: null,
        visible: false,
        toolType: null,
        stage: null
      });
    } else {
      setActiveTool(toolType);
      isDrawingRef.current = false;
      startPointRef.current = null;
      setPoint.current = 0;
      setPreviewLineData({
        startPoint: null,
        currentPoint: null,
        secondPoint: null,
        visible: false,
        toolType: toolType,
        stage: null
      });
    }
  };

  // 新增状态管理衍生线段
  const derivedLinesMap = useRef<Map<number, number[]>>(new Map()); // 记录主线段ID => 衍生线段ID列表

  // 修改后的 caisenmeasurement 处理逻辑
  const handleCaisenMeasurement = (mainLine: DrawingLine) => {
    if (!chartInstance || !candleSeries) return;
    if (mainLine.points.pointsType !== LinePointType.ThreePoints) return;

    const [p1, p2, p3] = [
      mainLine.points.startPoint,
      mainLine.points.endPoint,
      mainLine.points.posPoint
    ];

    const priceDiff = p2.price - p1.price;

    // 生成并过滤有效衍生线段
    const derivedIds = [1, 2]
      .map(step => {
        const derivedLine = createDerivedLine(p3, step, priceDiff);
        const series = createChartLine(derivedLine, chartInstance, candleSeries);
        if (series) {
          drawingLinesMap.current.set(derivedLine.id, series);
          drawingLineCache.current.set(derivedLine.id, derivedLine);
          return derivedLine.id;
        }
        return null;
      })
      .filter((id): id is number => id !== null);

    // 记录关联关系
    derivedLinesMap.current.set(mainLine.id, derivedIds);
  };

  // 新增横线管理映射表
  const horizontalLinesMap = useRef<Map<number, number[]>>(new Map()); // 衍生线段ID => 横线ID数组

  // 周期转换工具函数
  const getPeriodMilliseconds = (interval: KLineInterval): number => {
    const map: Record<KLineInterval, number> = {
      [KLineInterval.MIN1]: 60 * 1000,
      [KLineInterval.MIN5]: 5 * 60 * 1000,
      [KLineInterval.MIN15]: 15 * 60 * 1000,
      [KLineInterval.MIN30]: 30 * 60 * 1000,
      [KLineInterval.HOUR1]: 60 * 60 * 1000,
      [KLineInterval.HOUR4]: 4 * 60 * 60 * 1000,
      [KLineInterval.DAY1]: 24 * 60 * 60 * 1000,
      [KLineInterval.WEEK1]: 7 * 24 * 60 * 60 * 1000
    };
    return map[interval] || 0;
  };

  // 新增横线创建函数
  const createHorizontalLines = (endPoint: Point, interval: KLineInterval): number[] => {
    if (!chartInstance || !candleSeries) return [];

    // 计算5个周期的时间跨度（毫秒）
    const periodMs = getPeriodMilliseconds(interval) / 1000;
    const timeSpan = 5 * periodMs;

    // 计算时间范围
    const startTime = parseTime(endPoint.time) - timeSpan;
    const endTime = parseTime(endPoint.time) + timeSpan;

    // 创建水平线段
    const horizontalLine: DrawingLine = {
      id: Date.now() + Math.random(),
      symbol: currentSymbol,
      interval: currentPeriod,
      points: {
        pointsType: LinePointType.TwoPoints,
        startPoint: {
          time: startTime as Time,
          price: endPoint.price
        },
        endPoint: {
          time: endTime as Time,
          price: endPoint.price
        }
      },
      lineType: 'segment',
      textInfo: '',
      textPosition: 'middle',
      lineWidth: 1,
      lineStyle: 'dotted',
      color: '#666'
    };

    // 创建图表线段
    const series = createChartLine(horizontalLine, chartInstance, candleSeries);
    if (series) {
      drawingLinesMap.current.set(horizontalLine.id, series);
      return [horizontalLine.id];
    }
    return [];
  };

  // 独立创建衍生线段（不影响原始数据）
  // 修改后的 createDerivedLine
  // 增强的 createDerivedLine
  const createDerivedLine = (
    basePoint: Point,
    step: number,
    priceDiff: number,
  ): DrawingLine => {
    // 处理时间类型
    const baseTime = basePoint.time as number;

    // 计算终点时间和价格
    const endTime = baseTime + step * 1;  //lightweight step 是 秒
    const endPrice = Number(
      (basePoint.price + (priceDiff * step)).toFixed(4)
    );

    const derivedLine: DrawingLine = {
      id: Date.now() + step + Math.random(), // 唯一ID
      symbol: currentSymbol,
      interval: currentPeriod,
      points: {
        pointsType: LinePointType.TwoPoints,
        startPoint: {
          time: basePoint.time, // 保持原始时间类型
          price: basePoint.price
        },
        endPoint: {
          time: endTime as Time, // 使用计算后的时间戳
          price: endPrice
        }
      },
      lineType: 'segment',
      textInfo: '',
      textPosition: 'middle',
      lineWidth: 1,
      lineStyle: 'dashed',
      color: '#888'
    };

    // 创建横线（返回横线ID）

    if (derivedLine.points.pointsType === LinePointType.TwoPoints) {
      const horizontalLineIds = createHorizontalLines(derivedLine.points.endPoint, currentPeriod);
      horizontalLinesMap.current.set(derivedLine.id, horizontalLineIds);
    }

    return derivedLine;
  };

  // 处理删除画线
  // 修改后的 handleDeleteLine
  const handleDeleteLine = useCallback((line: DrawingLine) => {
    console.log('[Drawing] 删除画线:', line.id);

    // 如果删除的是当前悬停的线条，清除引用
    if (hoveredLineRef.current && hoveredLineRef.current.id === line.id) {
      hoveredLineRef.current = null;
    }
    
    // 如果删除的是当前选中的线条，清除引用
    if (selectedDrawingLine && selectedDrawingLine.id === line.id) {
      setSelectedDrawingLine(null);
    }

    // 1. 删除关联的横线（通过衍生线）
    if (derivedLinesMap.current.has(line.id)) {
      const derivedIds = derivedLinesMap.current.get(line.id)!;
      derivedIds.forEach(derivedId => {
        // 通过衍生线ID获取横线ID
        if (horizontalLinesMap.current.has(derivedId)) {
          const horizontalIds = horizontalLinesMap.current.get(derivedId)!;
          horizontalIds.forEach(id => {
            const series = drawingLinesMap.current.get(id);
            if (series && chartInstance) {
              chartInstance.removeSeries(series);
              drawingLinesMap.current.delete(id);
              drawingLineCache.current.delete(id);
            }
          });
          horizontalLinesMap.current.delete(derivedId);
        }
      });
    }

    // 删除衍生线段（新增逻辑）
    if (derivedLinesMap.current.has(line.id)) {
      const derivedIds = derivedLinesMap.current.get(line.id)!;
      derivedIds.forEach(id => {
        const series = drawingLinesMap.current.get(id);
        if (series && chartInstance) {
          chartInstance.removeSeries(series);
          drawingLinesMap.current.delete(id);
          drawingLineCache.current.delete(id);
        }
      });
      derivedLinesMap.current.delete(line.id);
    }

    // 原始删除逻辑保持不变
    const lineSeries = drawingLinesMap.current.get(line.id);
    if (lineSeries && chartInstance) {
      chartInstance.removeSeries(lineSeries);
      drawingLinesMap.current.delete(line.id);
      drawingLineCache.current.delete(line.id);
    }

    EventBus.emit(ChartEvents.Types.DELETE_DRAWING_LINE, { id: line.id });
    setHoveredLine(null);
    setHoverPosition(null);
  }, [chartInstance]);


  // 组件卸载时清理
  useEffect(() => {
    return () => {
      console.log('[Drawing] 卸载组件，调用清除画线');
      clearDrawingLines();
      clearPreviewLine();
    };
  }, []);

  // 修改addEndpointMarkers中的标记尺寸（约3像素）
  const addEndpointMarkers = (drawingLine: DrawingLine, lineSeries: ISeriesApi<'Line'>) => {
    if (!['segment', 'ray', 'caisenmeasurement'].includes(drawingLine.lineType!)) return;

    const markers: SeriesMarker<Time>[] = [];
    const markerSize = 1; // 调整为正常尺寸

    switch (drawingLine.points.pointsType) {
      case LinePointType.TwoPoints:
        markers.push(
          {
            time: drawingLine.points.startPoint.time as Time,
            position: 'inBar',
            color: drawingLine.color,
            shape: 'circle',
            size: markerSize,
          },
          {
            time: drawingLine.points.endPoint.time as Time,
            position: 'inBar',
            color: drawingLine.color,
            shape: 'circle',
            size: markerSize,
          }
        );
        break;
      case LinePointType.ThreePoints:
        markers.push(
          {
            time: drawingLine.points.startPoint.time as Time,
            position: 'inBar',
            color: drawingLine.color,
            shape: 'circle',
            size: markerSize,
          },
          {
            time: drawingLine.points.endPoint.time as Time,
            position: 'inBar',
            color: drawingLine.color,
            shape: 'circle',
            size: markerSize,
          },
          {
            time: drawingLine.points.posPoint.time as Time,
            position: 'inBar',
            color: drawingLine.color,
            shape: 'circle',
            size: markerSize,
          }
        );
        break;
    }

    markers.sort((a, b) => (a.time as number) - (b.time as number));
    lineSeries.setMarkers(markers);
  };

  return (
    <>
      {/* 画线工具按钮 */}
      <div
        style={{
          position: 'absolute',
          top: 5,
          right: 130,
          width: 20,
          height: 20,
          borderRadius: '20%',
          backgroundColor: isDrawingActive ? 'rgba(24, 144, 255, 0.3)' : 'rgba(128, 128, 128, 0.3)',
          color: 'var(--ant-color-text)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          zIndex: 100,
        }}
        onClick={handleDrawingButtonClick}
      >
        <span style={{
          fontSize: 14,
          display: 'inline-block',
          marginTop: '-2px'
        }}>✎</span>
      </div>

      {/* 画线工具栏 */}
      {isDrawingActive && toolbarVisible && (
        <div style={{
          position: 'absolute',
          top: 30,
          right: 130,
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          gap: 4,
          background: 'var(--ant-bg-elevated)',
          padding: 4,
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          <Button
            icon={<RulerMeasure2 size={16} />}
            type={activeTool === 'caisenmeasurement' ? 'primary' : 'text'}
            size="small"
            title="测量"
            onClick={() => handleToolClick('caisenmeasurement')}
          />
          <Button
            icon={<ArrowsVertical size={16} />}
            type={activeTool === 'vertical' ? 'primary' : 'text'}
            size="small"
            title="垂直线段"
            onClick={() => handleToolClick('vertical')}
          />
          <Button
            icon={<ArrowsHorizontal size={16} />}
            type={activeTool === 'horizontal' ? 'primary' : 'text'}
            size="small"
            title="水平线段"
            onClick={() => handleToolClick('horizontal')}
          />
          <Button
            icon={<SquareDashed size={16} />}
            type={activeTool === 'segment' ? 'primary' : 'text'}
            size="small"
            title="矩形"
            onClick={() => handleToolClick('segment')}
          />
          <Button
            icon={<Slashes size={16} />}
            type={activeTool === 'channel' ? 'primary' : 'text'}
            size="small"
            title="价格通道"
            onClick={() => handleToolClick('channel')}
          />
          <Button
            icon={<LetterCaseToggle size={16} />}
            type={activeTool === 'text' ? 'primary' : 'text'}
            size="small"
            title="文本注释"
            onClick={() => handleToolClick('text')}
          />
          <Button
            icon={<Trash size={16} />}
            type="text"
            size="small"
            title="删除所有画线"
            onClick={() => {
              // 发送删除所有画线事件
              EventBus.emit(ChartEvents.Types.DELETE_ALL_DRAWING_LINES, {
                symbol: currentSymbol, callback: (success: boolean) => {
                  if (success) {
                    clearDrawingLines();
                    setToolbarVisible(false);
                  }
                }
              });
            }}
            style={{ color: '#ff4d4f' }}
          />
        </div>
      )}

      {/* 悬浮工具栏 */}
      {hoveredLine && hoverPosition && (
        <div style={{
          position: 'absolute',
          top: hoverPosition.y,
          left: hoverPosition.x,
          zIndex: 1000,
          display: 'flex',
          gap: 4,
          background: 'var(--ant-bg-elevated)',
          padding: 4,
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          pointerEvents: 'auto'
        }}>
          <Button
            icon={<DeleteOutlined />}
            type="text"
            size="small"
            title="删除"
            onClick={() => handleDeleteLine(hoveredLine)}
          />
        </div>
      )}

      {/* 选中线条工具栏 */}
      {selectedDrawingLine && (
        <div style={{
          position: 'absolute',
          top: 5,
          right: '10%',
          transform: 'translateX(-50%)',
          zIndex: 1000,
          display: 'flex',
          gap: 4,
          background: 'var(--ant-bg-elevated)',
          padding: 4,
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          pointerEvents: 'auto',
          width: '80px'
        }}>
          <Button
            icon={<DeleteOutlined />}
            type="text"
            size="small"
            title="删除选中线条"
            onClick={() => handleDeleteLine(selectedDrawingLine)}
            style={{ color: '#ff4d4f' }}
          />
        </div>
      )}
    </>
  );
};

export default DrawingTools;