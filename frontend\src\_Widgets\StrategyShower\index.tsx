import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Typo<PERSON>, Button, Spin, Empty, Divider, Modal, Input, Space, Dropdown, Menu, message } from 'antd';
import { EllipsisOutlined, CopyOutlined, RocketOutlined } from '@ant-design/icons';

import { Line as G2PlotLine } from '@antv/g2plot';
import styles from './index.module.less';
import { EventBus } from '../../events/eventBus'; // 引入 EventBus
import { StrategyEvents } from '../../events/events'; // 引入 StrategyEvents
import { getUserRole } from '@/utils/auth'; // 新增：导入 getUserRole

const { Title, Text } = Typography;

// 定义回测结果的结构 (与后端 analyzer 返回的指标对应)
interface BacktestResult {
  // 指标
  total_return_pct?: number;
  max_drawdown_pct?: number;
  annualized_return_pct?: number;
  sharpe_ratio?: number | null;
  total_trades?: number;
  winning_trades?: number;
  losing_trades?: number;
  win_rate_pct?: number;
  profit_factor?: number | string | null; // 可能为 'inf'
  average_profit_per_trade?: number;
  average_profit_winning_trade?: number;
  average_loss_losing_trade?: number;
  backtest_period_start?: string | null;
  backtest_period_end?: string | null;
  initial_capital?: number;
  // 缩略图数据
  equity_curve_thumbnail_data?: Array<{ date: string; equity: number }>;
}

// 定义传递给 StrategyShower 的完整策略数据结构
export interface StrategyData {
  id: string; // 策略ID
  name?: string; // 策略名称 (可能从 YAML 中获取，但也可能在列表项中提供)
  backtest_result: BacktestResult | null; // 最新的回测结果或 null
  yaml?: string; // 新增：策略内容的 YAML 字符串
}

// --- 新增：简单的 UUID v4 格式检查函数 ---
const isUuid = (id: string): boolean => {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(id);
};

// 策略展示卡片组件
const StrategyShower: React.FC<{ strategy: StrategyData }> = ({ strategy }) => {

  // --- 内部状态管理 ---
  const [localBacktestResult, setLocalBacktestResult] = useState<BacktestResult | null>(strategy.backtest_result);
  // --- 新增：内部状态管理 YAML ---
  const [localYaml, setLocalYaml] = useState<string>(strategy.yaml || '');
  const [isRunningBacktest, setIsRunningBacktest] = useState<boolean>(false);
  // --- 新增：跟踪自动运行尝试状态 ---
  const [autoRunAttempted, setAutoRunAttempted] = useState<boolean>(false);
  // --- 使用 useRef 跟踪上一个策略名称，用于重置 autoRunAttempted ---
  const prevStrategyNameRef = React.useRef<string>();

  // --- 新增 Ref 用于 G2Plot ---
  const chartContainerRef = useRef<HTMLDivElement>(null); // Ref for the chart container div
  const chartInstanceRef = useRef<G2PlotLine | null>(null); // Ref to store the G2Plot Line instance

  // --- 检查数据是否足够显示图表 ---
  const hasSufficientData = (result: BacktestResult | null): boolean => {
    const thumbnailData = result?.equity_curve_thumbnail_data;
    return result !== null &&
           Array.isArray(thumbnailData) &&
           thumbnailData.length > 1 && // 需要至少两个点才能画线
           result.initial_capital !== undefined; // 确保关键指标存在
  };

  // --- 自动触发回测 或 同步 Props ---
  useEffect(() => {
    // 始终先尝试同步 prop 到 local state
    setLocalBacktestResult(strategy.backtest_result);
    // --- 同步 YAML ---
    setLocalYaml(strategy.yaml || '');
    // 如果 prop 更新带来了结果，确保取消运行状态
    if (strategy.backtest_result) {
        setIsRunningBacktest(false);
    }

    // 检查策略名称是否已更改，如果更改则重置尝试标记
    let needsResetAttemptFlag = false;
    if (prevStrategyNameRef.current !== strategy.id) {
        console.log(`[策略展示 ${strategy.id}] Strategy name changed. Resetting auto-run attempt flag.`);
        needsResetAttemptFlag = true;
        prevStrategyNameRef.current = strategy.id; // 更新 ref
    }

    const needsAutoRun = !hasSufficientData(strategy.backtest_result);
    const currentAttempted = needsResetAttemptFlag ? false : autoRunAttempted; // 如果重置，则视为未尝试

    console.log(`[策略展示 ${strategy.id}] useEffect Check: needsAutoRun=${needsAutoRun}, isRunning=${isRunningBacktest}, currentAttempted=${currentAttempted}`);

    // 已禁用自动触发回测逻辑
    // 保留重置标记的逻辑
    if (!needsAutoRun && currentAttempted) {
        console.log(`[策略展示 ${strategy.id}] 外部传入有效数据，重置尝试标记。`);
        setAutoRunAttempted(false);
    }

    // 如果策略名称改变，最终确保重置标记状态
    if (needsResetAttemptFlag) {
        setAutoRunAttempted(false);
    }

  // 依赖项主要关注 props 的变化
  }, [strategy.id, strategy.backtest_result, strategy.yaml]);

  // --- 新增：监听回测启动事件，以显示加载状态 ---
  useEffect(() => {

    const handleStrategyUpdateEvent = (payload: StrategyEvents.StrategyUpdatedPayload) => {
      // 确保 payload 中包含 yaml 字段
      if (payload.strategyId === strategy.id && payload.yaml !== undefined) {
        console.log(`[策略展示 ${strategy.id}] 接收到策略更新事件，更新 YAML 并触发回测`);
        // 更新本地 YAML 状态
        setLocalYaml(payload.yaml);
        // 触发回测
        handleRunBacktest();
      } else if (payload.strategyId === strategy.id) {
        // 如果 payload 没有 yaml，仅触发回测 (保持原有逻辑)
        console.log(`[策略展示 ${strategy.id}] 接收到策略更新事件 (无YAML)，仅触发回测`);
        handleRunBacktest();
      }
    };

    const subscription2 = EventBus.on(StrategyEvents.Types.STRATEGY_UPDATED, handleStrategyUpdateEvent);

    // 组件卸载时取消订阅
    return () => {
      console.log(`[策略展示 ${strategy.id}] 取消监听回测启动事件`);
      subscription2.unsubscribe();
    };
    // 依赖于 strategy.id，以便在组件显示不同策略时能正确比较ID
  }, [strategy.id]);
  // --- 新增结束 ---

  // --- 使用内部状态判断是否有结果 ---
  const hasResult = hasSufficientData(localBacktestResult); // 使用辅助函数判断
  const result = localBacktestResult;

  // ---- 处理图表数据 (基于内部状态) ----
  const chartData = result?.equity_curve_thumbnail_data?.map(d => ({
    date: d.date,
    value: d.equity,
  })) || [];

  // --- G2Plot 图表配置 (移除 height) ---
  const chartConfig = {
    data: chartData,
    xField: 'date',
    yField: 'value',
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
        rotate: 0,
        interval: 'auto',
        autoEllipsis: true,
        style: {
            fontSize: 10,
            textAlign: 'center' as 'center'
        }
      }
    },
    yAxis: {
      label: {
        formatter: (text: string) => {
          const v = parseFloat(text);
          if (isNaN(v)) {
            return text;
          }
          if (Math.abs(v) >= 1000000) return `${(v / 1000000).toFixed(1)}m`;
          if (Math.abs(v) >= 1000) return `${(v / 1000).toFixed(0)}k`;
          return `${v.toFixed(0)}`;
        },
      },
      min: null,
      max: null,
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: '策略净值',
        value: datum.value?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      }),
    },
    smooth: true,
    appendPadding: [10, 0, 0, 10],
    lineStyle: { lineWidth: 2 }
  };

  // --- 新增：useEffect 用于管理 G2Plot 实例 ---
  useEffect(() => {
    // 确保容器存在且有数据
    if (chartContainerRef.current && chartData.length > 1) {
      // 如果实例不存在，则创建新实例并渲染
      if (!chartInstanceRef.current) {
        console.log(`[策略展示 ${strategy.id}] G2Plot: 创建图表实例`);
        // G2PlotLine 需要传入容器 DOM 元素和配置
        chartInstanceRef.current = new G2PlotLine(chartContainerRef.current, {
            ...chartConfig, // 传入基础配置
            data: chartData, // 确保传入当前数据
            // G2Plot 实例的 autoFit 默认为 true，通常不需要显式设置
        });
        chartInstanceRef.current.render();
      } else {
        // 如果实例已存在，更新数据或配置
        // G2Plot 的 update 方法可以同时更新数据和配置
        console.log(`[策略展示 ${strategy.id}] G2Plot: 更新图表实例`);
        chartInstanceRef.current.update({
            ...chartConfig, // 传入最新的配置
            data: chartData, // 传入最新的数据
        });
      }
    } else if (chartInstanceRef.current) {
        // 如果容器不存在或数据不足，但实例存在，则销毁实例
        console.log(`[策略展示 ${strategy.id}] G2Plot: 销毁图表实例 (无数据或容器)`);
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
    }

    // 组件卸载或依赖变化时清理图表实例
    return () => {
      if (chartInstanceRef.current) {
        console.log(`[策略展示 ${strategy.id}] G2Plot: 清理并销毁图表实例`);
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
      }
    };
    // 依赖项：当图表数据或需要影响图表渲染的 strategy 属性变化时触发
    // 注意：chartConfig 对象每次渲染都会重新创建，直接作为依赖项会导致无限循环
    // 这里我们依赖 chartData 和 strategy.name (如果配置与名称相关)
    // 如果 chartConfig 的其他部分会动态变化，需要更精细地控制依赖项
  }, [chartData, strategy.id]); // 依赖于 chartData 和 strategy.name (用于日志)

  // ---- 处理显示的指标 (基于内部状态) ----
  const displayMetrics = {
    annualizedReturn: result?.annualized_return_pct?.toFixed(1) || '--',
    maxDrawdown: result?.max_drawdown_pct?.toFixed(1) || '--',
    initialCapital: result?.initial_capital?.toLocaleString() || '--',
  };

  // ---- 事件处理函数 ----
  const handleViewDetails = () => {
    console.log(`[策略展示] 点击查看详情: ${strategy.id}`);
    // --- 修改：只发出事件，使用 name 作为 id ---
    EventBus.emit(StrategyEvents.Types.SHOW_STRATEGY_DETAILS, { strategyId: strategy.id });
  };

  // handleRunBacktest 现在只用于运行按钮
  const handleRunBacktest = () => {
    if (isRunningBacktest) return;
    console.log(`[策略展示 ${strategy.id}] 触发运行回测 (可能由自动或手动触发)`);
    setIsRunningBacktest(true);
    EventBus.emit(StrategyEvents.Types.RUN_STRATEGY_BACKTEST, {
      strategyId: strategy.id,
      callback: (success: boolean, resultData?: BacktestResult | null, message?: string) => {
        setIsRunningBacktest(false);
        if (success && resultData) {
          console.log(`[策略展示 ${strategy.id}] 回调：回测成功`, resultData);
          setLocalBacktestResult(resultData);
          // 成功获取数据后，允许下次数据变坏时再次自动尝试
          setAutoRunAttempted(false);
        } else {
          console.error(`[策略展示 ${strategy.id}] 回调：回测失败: ${message}`);
          setLocalBacktestResult(null);
          // 失败了，保持 autoRunAttempted 为 true，不再自动重试
          // 用户需要手动点击按钮来重试
        }
      }
    });
  };

  // --- 新增：处理复制策略 ---
  // 状态管理复制对话框
  const [isCopyModalVisible, setIsCopyModalVisible] = useState(false);
  const [newStrategyName, setNewStrategyName] = useState('');

  // 新增：状态管理YAML查看对话框
  const [isYamlModalVisible, setIsYamlModalVisible] = useState(false);

  // --- 新增：处理编辑策略点击 ---
  const handleEditStrategy = () => {
    console.log(`[策略展示 ${strategy.id}] 点击编辑策略`);
    // 准备发送给编辑器的数据
    // 注意：当前 StrategyData 不包含 symbols。使用空数组占位。
    // 注意：使用 backtest_result 中的日期，如果存在。
    const dataForEdit: StrategyEvents.StrategyDataForEdit = {
      id: strategy.id,
      name: strategy.name || strategy.id, // 如果 name 不存在，使用 id
      // --- 修改：使用 localYaml 状态 ---
      yaml: localYaml, // 使用内部状态的 YAML
      universe: [], // FIXME: 需要获取真实的 symbols 列表
      startDate: localBacktestResult?.backtest_period_start || null,
      endDate: localBacktestResult?.backtest_period_end || null,
    };
    EventBus.emit(StrategyEvents.Types.SHOW_STRATEGY_EDITOR, dataForEdit);
  };
  // --- 新增结束 ---

  const handleCopyStrategy = () => {
    console.log(`[策略展示 ${strategy.id}] 点击复制策略`);
    // 设置默认的新策略名称
    setNewStrategyName(`${strategy.name || strategy.id}复制`);
    // 显示对话框
    setIsCopyModalVisible(true);
  };

  // 新增：处理确认复制
  const handleConfirmCopy = () => {
    const trimmedName = newStrategyName.trim();
    if (!trimmedName) {
      Modal.warning({
        title: '提示',
        content: '策略名称不能为空，请输入有效的策略名称',
      });
      return;
    }
    console.log(`[策略展示 ${strategy.id}] 确认复制策略，新名称: ${trimmedName}`);
    // 发出复制事件，传递策略ID和新名称
    EventBus.emit(StrategyEvents.Types.COPY_STRATEGY, {
      strategyId: strategy.id,
      newName: trimmedName
    });
    // 关闭对话框
    setIsCopyModalVisible(false);
  };

  // 处理取消复制
  const handleCancelCopy = () => {
    setIsCopyModalVisible(false);
  };

  // 处理查看配置
  const handleViewConfig = () => {
    console.log(`[策略展示 ${strategy.id}] 点击查看配置`);
    setIsYamlModalVisible(true);
  };

  // 处理部署到实盘
  const handleDeployToLive = () => {
    console.log(`[策略展示 ${strategy.id}] 点击部署到实盘`);
    // 发出显示实盘配置对话框事件
    EventBus.emit(StrategyEvents.Types.SHOW_LIVE_CONFIG, {
      strategyId: strategy.id,
      strategyName: strategy.name || strategy.id,
      // 新部署的策略没有当前策略类型，将使用默认值
    });
  };

  // 新增：处理复制配置
  const handleCopyConfig = () => {
    if (localYaml) {
      navigator.clipboard.writeText(localYaml)
        .then(() => {
          message.success('配置已复制到剪贴板');
        })
        .catch(() => {
          message.error('复制失败，请手动选择并复制');
        });
    }
  };

  // --- 新增：计算是否显示重新回测按钮 ---
  const currentUserRole = getUserRole(); // 直接调用获取角色
  const isAdmin = currentUserRole === 'admin'; // 假设管理员角色名为 'admin'
  const isCustomStrategy = isUuid(strategy.id); // 判断是否为自定义策略
  const canShowRunButton = isCustomStrategy || isAdmin; // 自定义策略 或 管理员 才能显示

  return (
    <>
      <Card
        hoverable
        className={styles.strategyShowerCard}
        style={{ width: '380px', height: '430px' }}
        bodyStyle={{ padding: '20px', display: 'flex', flexDirection: 'column', height: '100%' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px', flexShrink: 0 }}>
          <Title level={5} style={{ margin: 0, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }} title={strategy.name || strategy.id}>
            {strategy.name} {/* 直接显示 strategy.name */}
          </Title>

          {/* 仅对管理员用户显示三点菜单 */}
          {isAdmin && (
            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item key="viewConfig" onClick={handleViewConfig}>
                    查看配置
                  </Menu.Item>
                </Menu>
              }
              placement="bottomRight"
              trigger={['click']}
            >
              <Button
                type="text"
                icon={<EllipsisOutlined />}
                style={{
                  marginLeft: 8,
                  color: 'var(--ant-color-text-quaternary)', // 使用 Ant Design 的第四级文本颜色变量（更浅）
                  opacity: 0.3 // 进一步降低不透明度
                }}
              />
            </Dropdown>
          )}
        </div>

        {hasResult ? (
          <div style={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
            {/* 图表容器区域 */}
            <div style={{ marginBottom: '15px', flexShrink: 0, height: '180px' /* 固定图表区域高度 */ }}>
              {chartData.length > 1 ? (
                // 使用 div 作为 G2Plot 的容器
                <div ref={chartContainerRef} style={{ width: '100%', height: '100%' }} />
              ) : (
                // 数据不足时显示占位符
                <div style={{ width: '100%', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#ccc' }}>
                  (无足够数据生成图表)
                </div>
              )}
            </div>

            {/* 性能指标 */}
            <Row gutter={16} justify="space-between" style={{ marginBottom: '15px', textAlign: 'center', flexShrink: 0 }}>
              <Col span={8}>
                <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>年化收益</Text>
                <Text strong style={{ fontSize: '18px', color: parseFloat(displayMetrics.annualizedReturn) >= 0 ? '#cf1322' : '#3f8600' }}>
                  {displayMetrics.annualizedReturn}%
                </Text>
              </Col>
              <Col span={8}>
                <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>最大回撤</Text>
                <Text strong style={{ fontSize: '18px', color: '#3f8600' }}> {/* 回撤一般显示为正数 */}
                  {displayMetrics.maxDrawdown}%
                </Text>
              </Col>
              <Col span={8}>
                <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>初始资金</Text>
                <Text strong style={{ fontSize: '16px' }}>
                  {displayMetrics.initialCapital}
                </Text>
              </Col>
            </Row>

            {/* 分隔线和按钮 */}
            <div style={{ marginTop: 'auto' }}> {/* 将按钮推到底部 */}
              <Divider style={{ margin: '12px 0' }} />
              {/* 修改 Row 的 align 属性为 top */}
              <Row justify="space-between" align="top" gutter={8}>
                {/* 编辑按钮（左侧）- 仅自定义策略显示 */}
                <Col>
                  {isCustomStrategy && (
                    <Button
                      onClick={handleEditStrategy} // 添加点击处理
                      size="small" // 尝试小尺寸按钮
                    >
                      编辑
                    </Button>
                  )}
                </Col>

                {/* 其他按钮（右侧）- 使用 Space 组件保持间距 */}
                <Col>
                  <Space size="small" align="start">
                    {/* 复制按钮 */}
                    <Button onClick={handleCopyStrategy} size="small">
                      复制
                    </Button>

                    {/* 重新回测按钮 (Conditional) */}
                    {canShowRunButton && (
                      <Button onClick={handleRunBacktest} loading={isRunningBacktest} size="small">
                        重新回测
                      </Button>
                    )}

                    {/* 查看详情按钮 */}
                    <Button type="primary" onClick={handleViewDetails} size="small">
                      查看
                    </Button>

                    {/* 部署到实盘按钮 */}
                    <Button
                      type="primary"
                      onClick={handleDeployToLive}
                      size="small"
                      icon={<RocketOutlined />}
                      style={{
                        backgroundColor: '#52c41a',
                        borderColor: '#52c41a',
                        marginLeft: 8
                      }}
                    >
                      实盘
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>
          </div>
        ) : (
          <div style={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', textAlign: 'center' }}>
            <Text type="secondary" style={{ marginBottom: '20px', padding: '0 10px' }}>
              {isRunningBacktest ?
                '正在运行回测，请稍候...' :
                (strategy.name || '该策略') + '暂无有效回测数据，您可以运行回测以生成性能报告。'
              }
            </Text>
            {isRunningBacktest ? (
              <Spin size="large" />
            ) : (
              // 使用 Space 组件来布局按钮
              <Space direction="vertical" size="middle" style={{ marginTop: '20px' }}>
                <Button type="primary" onClick={handleRunBacktest}>
                  运行回测
                </Button>
                {/* 添加编辑按钮的条件渲染 */}
                {isCustomStrategy && (
                  <Button onClick={handleEditStrategy} size="small">
                    编辑
                  </Button>
                )}
              </Space>
            )}
          </div>
        )}
      </Card>

      {/* 复制策略的对话框 */}
      <Modal
        title="复制策略"
        open={isCopyModalVisible}
        onOk={handleConfirmCopy}
        onCancel={handleCancelCopy}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>请输入新策略的名称：</div>
        <Input
          value={newStrategyName}
          onChange={(e) => setNewStrategyName(e.target.value)}
          placeholder="请输入新策略名称"
          maxLength={50}
        />
      </Modal>

      {/* 查看YAML配置的对话框 */}
      <Modal
        title={
          <Row align="middle" style={{ width: '100%' }}>
            <Col style={{ width: '120px' }}>
              <span>策略配置</span>
            </Col>
            <Col>
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={handleCopyConfig}
                style={{
                  fontSize: '16px',
                  opacity: 0.3
                }}
                title="复制配置"
              />
            </Col>
          </Row>
        }
        open={isYamlModalVisible}
        onCancel={() => setIsYamlModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsYamlModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
        bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
      >
        <pre style={{
          backgroundColor: '#f5f5f5',
          padding: '16px',
          borderRadius: '4px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all',
          fontSize: '14px',
          fontFamily: 'monospace',
          position: 'relative'
        }}>
          {localYaml || '无配置内容'}
        </pre>
      </Modal>
    </>
  );
};

export default StrategyShower;