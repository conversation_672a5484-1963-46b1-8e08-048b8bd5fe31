const { spawn } = require('child_process');
const path = require('path');
const fsp = require('fs').promises;
const fs = require('fs');
const redis = require('redis');
const config = require('../config.json'); // For Redis config

const getBeijingTimeString = () => {
    const now = new Date();
    // UTC+8
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    return beijingTime.toISOString().replace('T', ' ').replace('Z', '');
};

const logger = {
    info: (message) => console.log(`[LiveProcessManager INFO ${getBeijingTimeString()}] ${message}`),
    warn: (message) => console.warn(`[LiveProcessManager WARN ${getBeijingTimeString()}] ${message}`),
    error: (message) => console.error(`[LiveProcessManager ERROR ${getBeijingTimeString()}] ${message}`),
};

const PYTHON_PATH = 'python'; // Or specific path to python executable
const userProcesses = new Map();

class LiveProcessManager {
    constructor() {
        this.redisClient = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        this.redisClient.on('error', (err) => {
            logger.error('Redis Client Error', err);
            logger.error('Redis Client Error (full):', JSON.stringify(err, Object.getOwnPropertyNames(err)));
            if (err && err.stack) {
                logger.error('Redis Client Error stack:', err.stack);
            }
        });
        this.readyPromises = new Map(); // 用于存储等待ready/pong信号的Promise

        // 用于发布的客户端
        this.redisClient = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        this.redisClient.on('error', (err) => logger.error(`Redis Publisher Error: ${err.message}`));

        this.redisSubscriber = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        this.redisSubscriber.on('error', (err) => logger.error(`Redis Subscriber Error: ${err.message}`));

        (async () => {
            try {
                await this.redisClient.connect();
                logger.info('Redis Publisher connected.');
                await this.redisSubscriber.connect();
                logger.info('Redis Subscriber connected.');
                
                // 订阅所有用户的状态频道
                await this.redisSubscriber.pSubscribe('user_engine_status:*', (message, channel) => {
                    this.#handleStatusSignal(message, channel);
                });
                logger.info('Successfully subscribed to user_engine_status:*');

            } catch (err) {
                logger.error(`Redis connection failed: ${err.message}`);
            }
        })();
    }

    #handleStatusSignal(message, channel) {
        try {
            const userId = channel.split(':')[1];
            const data = JSON.parse(message);

            // 检查是否有等待这个用户信号的Promise
            if (this.readyPromises.has(userId)) {
                const { resolve, timeout, signalType } = this.readyPromises.get(userId);
                
                // 如果是在等待 'pong' 或 'ready' 信号，并且收到了匹配的信号
                if (data.status === signalType) {
                    logger.info(`Received expected signal '${data.status}' from user ${userId} (PID: ${data.pid})`);
                    clearTimeout(timeout);
                    resolve(true); // Promise成功解决
                    this.readyPromises.delete(userId);
                }
            }
        } catch (err) {
            logger.error(`Error handling status signal: ${err.message}`);
        }
    }

    /**
     * 等待特定的信号 ('ready' 或 'pong')
     */
    #waitForSignal(userId, signalType, timeoutMs) {
        return new Promise((resolve, reject) => {
            // 如果之前有同用户的Promise，先清理
            if (this.readyPromises.has(userId)) {
                const { timeout } = this.readyPromises.get(userId);
                clearTimeout(timeout);
            }

            const timeout = setTimeout(() => {
                this.readyPromises.delete(userId);
                reject(new Error(`Timeout: Did not receive '${signalType}' signal from user ${userId} within ${timeoutMs}ms.`));
            }, timeoutMs);

            this.readyPromises.set(String(userId), { resolve, reject, timeout, signalType });
        });
    }

    /**
     * 确保指定用户的引擎进程正在运行并已就绪。
     * @param {number|string} userId - 用户ID
     * @returns {Promise<boolean>}
     */
    async #ensureUserProcessIsRunning(userId) {
        try {
            logger.info(`Pinging engine for user ${userId}...`);
            const controlChannel = `user_engine_control:${userId}`;
            await this.redisClient.publish(controlChannel, JSON.stringify({ action: 'ping' }));
            
            // 等待 pong 回复，超时时间较短
            await this.#waitForSignal(String(userId), 'pong', 2000); 
            logger.info(`Pong received from user ${userId}. Process is running.`);
            return true;

        } catch (error) {
            // 如果ping超时或失败，则认为进程未运行，启动它
            logger.warn(`Ping failed for user ${userId}: ${error.message}. Starting new process...`);
            return this.#startAndAwaitReady(userId);
        }
    }

    /**
     * 启动一个新进程并等待其 'ready' 信号
     */
    async #startAndAwaitReady(userId) {
        // 首先确保没有旧的进程映射
        if (userProcesses.has(userId)) {
             logger.warn(`Found stale process map for user ${userId}, cleaning up before start.`);
             userProcesses.delete(userId);
        }
        
        const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
        const engineSourcePath = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'base', 'live_engine.py');
        const engineDestPath = path.join(userDir, 'live_engine.py');

        await fsp.mkdir(userDir, { recursive: true });
        await fsp.copyFile(engineSourcePath, engineDestPath);

        const logDir = path.join(userDir, 'logs');
        await fsp.mkdir(logDir, { recursive: true });
        const logDate = new Date().toISOString().split('T')[0];
        const logFilePath = path.join(logDir, `${logDate}.log`);
        const logStream = fs.createWriteStream(logFilePath, { flags: 'a' });

        // 增加日志流监控
        logger.info(`创建日志文件流: ${logFilePath}`);
        
        logStream.on('open', () => {
            logger.info(`日志文件流已打开: ${logFilePath}`);
        });
        
        logStream.on('error', (err) => {
            logger.error(`日志文件流错误: ${err.message}`);
        });

        const pyProcess = spawn(PYTHON_PATH, [
            'live_engine.py',
            '--user_id', userId,
            '--redis_host', config.redis.host,
            '--redis_port', config.redis.port,
            '--redis_password', config.redis.password,
            '--redis_db', config.redis.db,
        ], {
            cwd: userDir,
            detached: true,
            stdio: ['ignore', 'pipe', 'pipe']
        });

        userProcesses.set(userId, pyProcess);
        logger.info(`Process for user ${userId} started with PID: ${pyProcess.pid}. Waiting for ready signal...`);

        // 重新添加日志监听和写入逻辑
        pyProcess.stdout.on('data', (data) => {
            const message = data.toString().trim();
            logger.info(`[PID:${pyProcess.pid}] [USER:${userId}] [STDOUT]: ${message}`);
            logger.info(`准备写入日志文件: ${logFilePath}`);
            try {
                logStream.write(`[STDOUT] ${message}\n`);
                logger.info(`成功写入日志文件: ${message.substring(0, 50)}...`);
            } catch (err) {
                logger.error(`写入日志文件失败: ${err.message}`);
            }
        });

        pyProcess.stderr.on('data', (data) => {
            const message = data.toString().trim();
            logger.error(`[PID:${pyProcess.pid}] [USER:${userId}] [STDERR]: ${message}`);
            logger.info(`准备写入错误日志到文件: ${logFilePath}`);
            try {
                logStream.write(`[STDERR] ${message}\n`);
                logger.info(`成功写入错误日志文件: ${message.substring(0, 50)}...`);
            } catch (err) {
                logger.error(`写入错误日志文件失败: ${err.message}`);
            }
        });

        pyProcess.on('exit', (code, signal) => {
            logger.warn(`Process for user ${userId} exited with code ${code} and signal ${signal}`);
            logger.info(`准备关闭日志文件流: ${logFilePath}`);
            userProcesses.delete(userId);
            logStream.end();
            logger.info(`日志文件流已关闭: ${logFilePath}`);
            if (this.readyPromises.has(String(userId))) {
                const { reject, timeout } = this.readyPromises.get(String(userId));
                clearTimeout(timeout);
                reject(new Error(`Process for user ${userId} exited before sending ready signal.`));
                this.readyPromises.delete(String(userId));
            }
        });
        
        pyProcess.on('error', (err) => {
             logger.error(`Failed to start process for user ${userId}: ${err.message}`);
             userProcesses.delete(userId);
             logStream.end();
             if (this.readyPromises.has(String(userId))) {
                const { reject, timeout } = this.readyPromises.get(String(userId));
                clearTimeout(timeout);
                reject(new Error(`Process for user ${userId} failed to start: ${err.message}`));
                this.readyPromises.delete(String(userId));
            }
        });

        // 等待 ready 信号，超时时间较长
        return this.#waitForSignal(String(userId), 'ready', 15000);
    }

    /**
     * 为指定用户添加一个策略到其运行的引擎中
     * @param {number|string} userId - 用户ID
     * @param {string} strategyId - 策略ID
     * @param {string} strategyYaml - 策略的YAML配置内容
     * @param {string} strategyType - 策略类型，默认为 'portfolio'
     */
    async addStrategy(userId, strategyId, strategyYaml, strategyType = 'portfolio') {

        logger.info('LiveProcessManager.addStrategy 被调用');

        await this.#ensureUserProcessIsRunning(userId);

        logger.info(`添加策略被调用，用户ID：${userId}，策略ID：${strategyId}，策略类型：${strategyType}`);

        const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
        const yamlFileName = `${strategyId}.yaml`;
        const yamlPath = path.join(userDir, yamlFileName);
        
        // 将策略的YAML内容写入用户目录下的文件
        await fsp.writeFile(yamlPath, strategyYaml, 'utf8');
        logger.info(`Wrote strategy YAML to ${yamlPath}`);

        // 通过Redis发布"添加策略"的指令
        const controlChannel = `user_engine_control:${userId}`;
        const command = {
            action: 'add',
            // Python进程的工作目录就是userDir，所以我们只需传递文件名
            yaml_path: yamlFileName,
            // 添加策略类型参数
            strategy_type: strategyType
        };
        await this.redisClient.publish(controlChannel, JSON.stringify(command));
        logger.info(`Published 'add' command for strategy ${strategyId} with type ${strategyType} to channel ${controlChannel}`);
    }
    
    /**
     * 从指定用户的引擎中移除一个策略
     * @param {number|string} userId - 用户ID
     * @param {string} strategyId - 策略ID
     */
    async removeStrategy(userId, strategyId) {
        if (!userProcesses.has(userId)) {
            logger.warn(`Cannot remove strategy: Process for user ${userId} is not running.`);
            return;
        }

        // 通过Redis发布"移除策略"的指令
        const controlChannel = `user_engine_control:${userId}`;
        const command = {
            action: 'remove',
            strategy_id: strategyId
        };
        await this.redisClient.publish(controlChannel, JSON.stringify(command));
        logger.info(`Published 'remove' command for strategy ${strategyId} to channel ${controlChannel}`);
        
        // 删除对应的YAML文件
        const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
        const yamlPath = path.join(userDir, `${strategyId}.yaml`);
        try {
            await fsp.unlink(yamlPath);
            logger.info(`Deleted strategy YAML file: ${yamlPath}`);
        } catch(err) {
            logger.warn(`Could not delete YAML file ${yamlPath}: ${err.message}`);
        }
    }

    /**
     * 停止指定用户的所有策略引擎进程
     * @param {number|string} userId - 用户ID
     */
    async stopUserProcess(userId) {
        const process = userProcesses.get(userId);
        if (process) {
            logger.info(`Stopping process for user ${userId} (PID: ${process.pid})`);
            process.kill('SIGTERM'); // 发送终止信号
            userProcesses.delete(userId);
        } else {
            logger.warn(`Cannot stop process: No running process found for user ${userId}.`);
        }
    }
}

// 导出一个单例
module.exports = new LiveProcessManager(); 