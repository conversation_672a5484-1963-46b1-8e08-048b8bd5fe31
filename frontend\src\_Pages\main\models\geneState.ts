import { SignalConfig } from '@/shared_types/market';
import { atom } from 'jotai';
import { MarketEvents } from '@/events/events';

/**
 * 指标类型定义
 */
export interface GeneIndicator {
    id: number;
    name: string;
    type: 'past' | 'future';
    description: string;
}

/**
 * 指标参数定义
 */
export interface GeneParam {
    id: number;
    name: string;
    label: string;
    type: string;
    required: boolean;
    options?: Array<{
        value: string | number;
        label: string;
    }>;
}

/**
 * 指标列表类型
 */
export interface GeneIndicators {
    past: GeneIndicator[];
    future: GeneIndicator[];
}

/**
 * 基因配置类型
 */
export interface GeneConfig {
    indicator: GeneIndicator;
    params: Record<string, any>;
}

/**
 * 选中的指标状态
 */
export const selectedIndicatorAtom = atom<GeneIndicator | null>(null);

/**
 * 基因配置对话框可见性状态
 */
export const geneDialogVisibleAtom = atom<boolean>(false);

/**
 * 指标列表状态
 */
export const geneIndicatorsAtom = atom<GeneIndicators>({
    past: [],
    future: []
});

/**
 * 基因配置状态
 */
export const geneConfigAtom = atom<GeneConfig | null>(null);

export const observingSignalAtom = atom<SignalConfig | null>(null);
export const signalListVisibleAtom = atom<boolean>(false);
export const historicalSignalsAtom = atom<MarketEvents.SignalItem[]>([]); 