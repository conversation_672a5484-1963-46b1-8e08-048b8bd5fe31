// frontend/src/_Dispatchers/StrategyDispatcher.ts
import { EventBus } from '../events/eventBus'; // 导入事件总线实例
import { StrategyEvents } from '../events/events'; // 导入事件定义和类型
import { StrategyListItem, TradeRecord } from '../shared_types/strategy';
import axios from 'axios'; // 导入 axios
import { getToken } from '@/utils/auth'; // 导入 getToken 函数

// --- 导入或定义 BacktestResult 类型 --- 
// 基础回测指标
interface BaseBacktestMetrics {
    total_return_pct?: number;
    max_drawdown_pct?: number;
    annualized_return_pct?: number;
    sharpe_ratio?: number | null;
    total_trades?: number;
    winning_trades?: number;
    losing_trades?: number;
    win_rate_pct?: number;
    profit_factor?: number | string | null;
    average_profit_per_trade?: number;
    average_profit_winning_trade?: number;
    average_loss_losing_trade?: number;
    backtest_period_start?: string | null;
    backtest_period_end?: string | null;
    initial_capital?: number;
}

// 缩略图数据点
interface EquityPoint {
    date: string;
    equity: number;
}

// 策略列表项的回测结果（只包含缩略图）
interface BacktestResultThumbnail extends BaseBacktestMetrics {
    equity_curve_thumbnail_data?: EquityPoint[];
}

// --- 修正：策略详情数据结构 (Flattened) --- 
// 包含基础信息、所有指标、完整权益曲线和交易历史
export interface StrategyDetails extends BaseBacktestMetrics { 
    id: string;                         // 策略 ID (假设从后端获取或使用传入的)
    name: string;                       // 策略名称
    universe?: string[];                // 品种列表 (假设后端返回)
    equity_curve_data?: EquityPoint[];         // 完整权益曲线
    equity_curve_thumbnail_data?: EquityPoint[]; // 缩略图 (通常也包含在详情中)
    trade_history?: TradeRecord[];             // 交易历史
    // 包含所有从 BaseBacktestMetrics 继承的字段
    // 以及 Python 可能返回的任何其他特定于详情的字段
}
// -------------------------------------

// 定义后端 API 的基础路径 (应与 Python Flask服务器路由匹配)
// 代理设置 '/api/strategy' -> http://localhost:3000 (假设 Node.js API 在 3000，策略服务 API 在此之下)
const BACKEND_API_BASE = '/api'; // Node.js 后端API基础路径

// --- 修正：定义获取策略列表 API 的响应类型 ---
interface StrategyListApiResponse {
  success: boolean;
  data?: StrategyListItem[]; // 成功时包含策略数组
  error?: string;           // 失败时包含错误信息
}
// --- 修正：定义运行回测 API 的响应类型，添加 data 字段 ---
interface RunBacktestApiResponse {
  success: boolean;
  message?: string; // 成功时包含消息
  data?: BacktestResultThumbnail | null; // 添加 data 字段
  error?: string;   // 失败时包含错误信息
}

// --- 新增：获取策略详情 API 的响应类型 ---
interface GetStrategyDetailsApiResponse {
    success: boolean;
    data?: StrategyDetails | null; // 成功时返回完整详情
    error?: string;               // 失败时返回错误
}
// ---------------------------------------

// --- 新增：复制策略 API 的响应类型 ---
interface CopyStrategyApiResponse {
    success: boolean;
    message?: string; // 成功或失败时的消息
    error?: string;   // 失败时的详细错误
}
// ---------------------------------------

/**
 * 处理获取策略列表请求
 * @param payload 包含回调函数的 payload，回调期望 (success: boolean, data: StrategyListItem[])
 */
async function handleGetStrategiesList(payload: StrategyEvents.GetStrategiesListPayload) {
  console.log('[策略分发器] 收到获取策略列表事件');
  const { callback } = payload;
  if (!callback || typeof callback !== 'function') {
    console.error('[策略分发器] 获取策略列表事件缺少有效的回调函数');
    return;
  }

  // --- 修改：使用 getToken() --- 
  const token = getToken();
  if (!token) {
    console.error('[策略分发器] 未找到认证 Token，无法获取策略列表');
    callback(false, []); // 直接回调失败，传递空数组
    return; 
  }
  // --------------------------

  try {
    // --- 修改：调用 Node.js 后端的接口 --- 
    const apiUrl = `${BACKEND_API_BASE}/strategy/list`; 
    console.log(`[策略分发器] 调用 API: GET ${apiUrl}`);
    
    // 添加 Authorization Header 
    const response = await axios.get<StrategyListApiResponse>(apiUrl, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`[策略分发器] 响应状态: ${response.status}`);
    
    // 检查 Node.js 后端的响应
    if (response.data && response.data.success && Array.isArray(response.data.data)) {
      console.log('[策略分发器] 成功获取策略列表:', response.data.data);
      callback(true, response.data.data);
    } else {
      console.error('[策略分发器] 获取策略列表失败:', response.status, response.data?.error || '响应格式不符合预期');
      // --- 修正：失败时传递空数组给回调 --- 
      callback(false, []); 
    }
  } catch (error: any) {
    console.error('[策略分发器] 请求策略列表时出错:', error);
    const errorMsg = axios.isAxiosError(error) ? (error.response?.data?.error || error.message) : error.message;
    console.error('[策略分发器] 错误详情:', errorMsg);
    // --- 修正：失败时传递空数组给回调 --- 
    callback(false, []); 
  }
}

/**
 * 处理运行策略回测请求
 * @param payload 包含策略 ID (name) 和回调函数的 payload, 回调期望 (success: boolean, resultData?: BacktestResultThumbnail | null, message?: string)
 */
async function handleRunStrategyBacktest(payload: StrategyEvents.RunStrategyBacktestPayload) {
  console.log('[策略分发器] 收到运行策略回测事件:', payload.strategyId);
  const { strategyId, callback } = payload;

  if (!strategyId) {
      console.error('[策略分发器] 运行回测事件缺少 strategyId');
      // --- 修正：匹配回调签名 --- 
      if (callback) callback(false, null, '缺少策略ID'); 
      return;
  }
  if (!callback || typeof callback !== 'function') {
      console.error('[策略分发器] 运行回测事件缺少有效的回调函数');
      return;
  }

  const token = getToken();
  if (!token) {
    console.error('[策略分发器] 未找到认证 Token，无法运行回测');
    // --- 修正：匹配回调签名 --- 
    callback(false, null, '用户未认证'); 
    return; 
  }

  try {
    const apiUrl = `${BACKEND_API_BASE}/strategy/backtest`; 
    const payloadData = { name: strategyId }; 
    console.log(`[策略分发器] 调用后端接口: POST ${apiUrl} with payload:`, payloadData);
    
    const response = await axios.post<RunBacktestApiResponse>(apiUrl, payloadData, { 
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data && response.data.success) {
      console.log('[策略分发器] 策略回测请求成功:', response.data.message);
      // --- 新增：打印接收到的数据 --- 
      console.log('[策略分发器] 收到的回测数据:', response.data.data);
      // --- 修正：传递 resultData 和 message 给回调 --- 
      callback(true, response.data.data, response.data.message || '回测已成功启动'); 
    } else {
      console.error('[策略分发器] 策略回测请求失败:', response.status, response.data?.error || '响应格式不符合预期');
      // --- 修正：匹配回调签名 --- 
      callback(false, null, response.data?.error || '回测请求失败'); 
    }
  } catch (error: any) {
    console.error(`[策略分发器] 请求策略回测 (${strategyId}) 时出错:`, error);
    const errorMsg = axios.isAxiosError(error) ? (error.response?.data?.error || error.message) : error.message;
    console.error('[策略分发器] 错误详情:', errorMsg);
    // --- 修正：匹配回调签名 --- 
    callback(false, null, errorMsg || '请求出错'); 
  }
}

// --- 修改：处理获取策略详情请求 ---
/**
 * 处理获取策略详情请求
 * @param payload 包含 strategyId 和 callback, 回调期望 (success: boolean, details?: StrategyDetails | null, message?: string)
 */
async function handleGetStrategyDetails(payload: StrategyEvents.GetStrategyDetailsPayload) {
    console.log('[策略分发器] 收到获取策略详情事件:', payload.strategyId);
    const { strategyId, callback } = payload;
  
    if (!strategyId) {
        console.error('[策略分发器] 获取详情事件缺少 strategyId');
        if (callback) callback(false, null, '缺少策略ID'); 
        return;
    }
    if (!callback || typeof callback !== 'function') {
        console.error('[策略分发器] 获取详情事件缺少有效的回调函数');
        return;
    }
  
    const token = getToken();
    if (!token) {
      console.error('[策略分发器] 未找到认证 Token，无法获取策略详情');
      callback(false, null, '用户未认证'); 
      return; 
    }
  
    try {
      // 调用新的后端接口
      const apiUrl = `${BACKEND_API_BASE}/strategy/details/${strategyId}`; 
      console.log(`[策略分发器] 调用 API: GET ${apiUrl}`);
      
      const response = await axios.get<GetStrategyDetailsApiResponse>(apiUrl, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
  
      console.log(`[策略分发器] 获取详情响应状态: ${response.status}`);
      
      if (response.data && response.data.success && response.data.data) {
        console.log('[策略分发器] 成功获取策略详情:', response.data.data);
        
        // --- 修正：直接使用后端返回的数据，并进行类型断言 --- 
        // 假设 Python 后端返回的 data 对象结构与修正后的 StrategyDetails 接口匹配
        const details = response.data.data as StrategyDetails;
        
        // 可选：进行一些基本验证或设置默认值，以防后端数据不完整
        if (!details.id) details.id = strategyId; // 如果后端没返回 id，使用传入的
        if (!details.name) details.name = strategyId; // 如果后端没返回 name，使用传入的
        // ... 其他字段的默认值或验证 ...

        callback(true, details); 
      } else {
        console.error('[策略分发器] 获取策略详情失败:', response.status, response.data?.error || '响应格式不符合预期');
        callback(false, null, response.data?.error || '获取策略详情失败'); 
      }
    } catch (error: any) {
      console.error(`[策略分发器] 请求策略详情 (${strategyId}) 时出错:`, error);
      const errorMsg = axios.isAxiosError(error) ? (error.response?.data?.error || error.message) : error.message;
      console.error('[策略分发器] 错误详情:', errorMsg);
      callback(false, null, errorMsg || '请求出错'); 
    }
  }
// --- 修改结束 ---

// --- 新增：处理复制策略请求 ---
/**
 * 处理复制策略请求
 * @param payload 包含要复制的策略名称 { strategyName: string }
 */
async function handleCopyStrategy(payload: StrategyEvents.CopyStrategyPayload) {
    console.log('[策略分发器] 收到复制策略事件:', payload.strategyId);
    const { newName: newName, strategyId: strategyId } = payload;

    if (!strategyId) {
        console.error('[策略分发器] 复制策略事件缺少 strategyName');
        // 这里不需要回调，所以只记录错误
        return;
    }

    const token = getToken();
    if (!token) {
        console.error('[策略分发器] 未找到认证 Token，无法复制策略');
        return;
    }

    try {
        // 定义后端复制策略的 API 端点
        const apiUrl = `${BACKEND_API_BASE}/strategy/copy`;
        const payloadData = { new_name: newName, id: strategyId }; // 后端需要知道要复制哪个策略
        console.log(`[策略分发器] 调用后端接口: POST ${apiUrl} with payload:`, payloadData);

        const response = await axios.post<CopyStrategyApiResponse>(apiUrl, payloadData, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.data && response.data.success) {
            console.log('[策略分发器] 策略复制请求成功:', response.data.message || '策略已复制');
            // 复制成功后，发出列表更新事件，通知其他组件刷新列表
            EventBus.emit(StrategyEvents.Types.STRATEGY_LIST_UPDATED, undefined);
        } else {
            console.error('[策略分发器] 策略复制请求失败:', response.status, response.data?.error || response.data?.message || '后端未提供明确错误');
            // 可以考虑在这里通知用户复制失败，例如通过 UI 提示
        }
    } catch (error: any) {
        console.error(`[策略分发器] 请求复制策略 (${strategyId}) 时出错:`, error);
        const errorMsg = axios.isAxiosError(error) ? (error.response?.data?.error || error.response?.data?.message || error.message) : error.message;
        console.error('[策略分发器] 错误详情:', errorMsg);
        // 可以考虑在这里通知用户复制失败
    }
}
// --- 新增结束 ---

// --- 新增：处理保存 YAML 事件 --- 
/**
 * 处理保存策略 YAML 请求
 * @param payload 包含策略 ID 和更新后的 YAML 字符串 { id: string; yaml: string; }
 */
async function handleStrategyUpdate(payload: StrategyEvents.StrategyUpdatePayload) {
  
  console.log('[策略分发器] 收到保存策略 YAML 事件, ID:', payload.id);
  const { id, yaml } = payload;

  if (!id || typeof yaml !== 'string') {
      console.error('[策略分发器] 保存 YAML 事件缺少 id 或 yaml');
      // 可以考虑通知用户失败
      return;
  }

  const token = getToken();
  if (!token) {
      console.error('[策略分发器] 未找到认证 Token，无法保存策略');
      // 可以考虑通知用户失败
      return;
  }

  try {
      // 假设后端更新接口是 /api/strategy/update，接受 { id, yaml }
      const apiUrl = `${BACKEND_API_BASE}/strategy/update`;
      // 这里的 payloadData 需要和服务端确认，匹配后端需要的 { strategy_id, strategy_yaml }
      const payloadData = { strategy_id: id, strategy_yaml: yaml }; 
      console.log(`[策略分发器] 调用后端接口: POST ${apiUrl} for ID: ${id}`);

      // 假设后端返回 { success: boolean, message?: string, error?: string }
      const response = await axios.post<{ success: boolean, message?: string, error?: string }>(apiUrl, payloadData, {
          headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json' // 明确指定 Content-Type
          }
      });

      if (response.data && response.data.success) {
          console.log('[策略分发器] 策略 YAML 保存成功:', response.data.message || '');
          // 保存成功后，发出列表更新事件，以便列表刷新
          // EventBus.emit(StrategyEvents.Types.STRATEGY_LIST_UPDATED, undefined);
          // 保存成功后，触发回测事件
          console.log(`[策略分发器] 触发对策略 ${id} 的回测`);

          // 发出已经被更新的事件
          EventBus.emit(StrategyEvents.Types.STRATEGY_UPDATED, {
              strategyId: id,
              yaml: yaml
          });

      } else {
          console.error('[策略分发器] 策略 YAML 保存失败:', response.status, response.data?.error || response.data?.message || '后端未提供明确错误');
          // 可以考虑通知用户失败
      }
  } catch (error: any) {
      console.error(`[策略分发器] 请求保存策略 YAML (${id}) 时出错:`, error);
      const errorMsg = axios.isAxiosError(error) ? (error.response?.data?.error || error.response?.data?.message || error.message) : error.message;
      console.error('[策略分发器] 错误详情:', errorMsg);
      // 可以考虑通知用户失败
  }
}
// --- 新增结束 ---

/**
 * 初始化策略分发器，监听相关事件
 */
export function initializeStrategyDispatcher() {
  console.log('[策略分发器] 初始化，开始监听事件...');
  EventBus.on(StrategyEvents.Types.GET_STRATEGIES_LIST, handleGetStrategiesList);
  EventBus.on(StrategyEvents.Types.RUN_STRATEGY_BACKTEST, handleRunStrategyBacktest);
  EventBus.on(StrategyEvents.Types.GET_STRATEGY_DETAILS, handleGetStrategyDetails);
  EventBus.on(StrategyEvents.Types.COPY_STRATEGY, handleCopyStrategy);
  // --- 新增：监听保存 YAML 事件 --- 
  EventBus.on(StrategyEvents.Types.UPDATE_STRATEGY, handleStrategyUpdate);
  // --- 新增结束 ---

  // 在应用卸载或不再需要时，记得取消监听
  // EventBus.off(StrategyEvents.Types.GET_STRATEGIES_LIST, handleGetStrategiesList);
  // EventBus.off(StrategyEvents.Types.RUN_STRATEGY_BACKTEST, handleRunStrategyBacktest);
  // EventBus.off(StrategyEvents.Types.GET_STRATEGY_DETAILS, handleGetStrategyDetails);
  // EventBus.off(StrategyEvents.Types.COPY_STRATEGY, handleCopyStrategy);
  // EventBus.off(StrategyEvents.Types.SAVE_STRATEGY_YAML, handleSaveStrategyYaml);
}

// 可以在应用入口处或其他适当位置调用 initializeStrategyDispatcher() 来启动监听
// initializeStrategyDispatcher();