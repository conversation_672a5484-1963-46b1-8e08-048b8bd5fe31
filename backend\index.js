require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const chatRouter = require('./routes/chat');
const { userHandler } = require('./_Handlers/userHandler');
const http = require('http');
const WebSocket = require('ws');
const chatHandler = require('./_Handlers/chatHandler');
const sessionDBManager = require('./database/SessionDBManager');
const path = require('path');
const { marketHandler } = require('./_Handlers/marketHandler');
const { strategyHandler } = require('./_Handlers/strategyHandler');
const { chartHandler } = require('./_Handlers/chartHandler');
const { poolHandler } = require('./_Handlers/poolHandler');
const tradeHandler = require('./_Handlers/tradeHandler/index');
const socketHandler = require('./_Handlers/socketHandler');
const liveStrategyHandler = require('./_Handlers/liveStrategyHandler');
const socketio = require('socket.io');
//const userRouter = require('./routes/user');

const app = express();

// Middleware
app.use(bodyParser.json());

// 添加静态文件服务
app.use('/uploads', (req, res, next) => {
  /*console.log('[Static Files] Request:', {
    path: req.path,
    method: req.method,
    url: req.url,
    headers: {
      'user-agent': req.headers['user-agent'],
      'accept': req.headers.accept
    }
  });*/
  next();
}, express.static('uploads'));

// 详细的调试中间件
app.use((req, res, next) => {
  const startTime = Date.now();

  console.log('\n=== Incoming Request ===', new Date().toLocaleString());
  console.log(`[API] ${req.method} ${req.url}`);
  console.log('Headers:', {
    'content-type': req.headers['content-type'],
    'authorization': req.headers.authorization ? 'Bearer ...' : undefined,
    'user-agent': req.headers['user-agent']
  });

  if (Object.keys(req.body).length > 0) {
    console.log('Body:', {
      ...req.body,
      password: req.body.password ? '[HIDDEN]' : undefined
    });
  }

  if (Object.keys(req.query).length > 0) {
    console.log('Query:', req.query);
  }

  // 添加响应完成的监听
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    console.log(`[API] Response ${res.statusCode} sent in ${duration}ms`);
    console.log('=== Request Complete ===\n');
  });

  next();
});

// Routes
app.use('/api/user', userHandler.router);
app.use('/api/chat', chatRouter);
app.use('/api/market', marketHandler.getRouter());
// 异步初始化实盘策略处理器（加载数据库到内存）
liveStrategyHandler.init().then(router => {
  app.use('/api/strategy', router);
  console.log('[LiveStrategy] 实盘策略处理器初始化完成，数据已加载到内存');
}).catch(error => {
  console.error('[LiveStrategy] 实盘策略处理器初始化失败:', error);
  // 降级使用getRouter（不加载内存数据）
  app.use('/api/strategy', liveStrategyHandler.getRouter());
});
app.use('/api/strategy', strategyHandler.getRouter());

app.use('/api/chart', chartHandler.getRouter());
app.use('/api/pool', poolHandler.getRouter());
app.use('/api/trade', tradeHandler.init());
app.use('/api/socket', socketHandler.router);

// 健康检查接口，直接返回success:true
app.get('/api/health', (req, res) => {
  res.json({ success: true });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Internal Server Error' });
});

// 引入配置文件
const config = require('./config.json');

// 创建 HTTP 服务器
const server = http.createServer(app);

// 创建 WebSocket 服务器 (用于聊天)
const wss = new WebSocket.Server({
  server,
  path: '/chat' // 聊天使用的路径
});

// 初始化聊天 WebSocket 处理器
chatHandler.initWebSocket(wss);

// 创建Socket.IO服务器（用于实时数据和未来可能的其他通信）
const io = socketio(server, {
  cors: { // 添加CORS配置，允许前端访问
    origin: "*", // 允许所有来源，生产环境应配置具体来源
    methods: ["GET", "POST"]
  },
  // 可以根据需要调整其他 Socket.IO 服务器选项
});

// --- 新增: 创建交易通道 Socket.IO 命名空间 ---
const tradeNamespace = io.of('/trade');
console.log(`Socket.IO trade namespace initialized at /trade`);

// --- 修改: 初始化交易通道 Socket.IO 处理器 (传递命名空间) ---
tradeHandler.initSocketIO(tradeNamespace); // 假设 tradeHandler 有 initSocketIO 方法
// console.log(`Trade WebSocket server running at ws://localhost:${config.port}/ws/trade`); // 移除旧日志
// --- 结束修改 ---

// 初始化市场数据 Socket.IO 处理器（可以继续使用根命名空间或也用特定命名空间）
marketHandler.initSocketIO(io); // 保持不变，或改为 io.of('/market') ?

// 在应用退出时清理所有数据库连接
process.on('SIGINT', async () => {
  await sessionDBManager.closeAllConnections();
  process.exit(0);
});

// 使用 server 而不是 app 来监听端口
server.listen(config.port, () => {
  console.log(`Server running on port ${config.port}`);
  console.log(`WebSocket server running at ws://localhost:${config.port}/chat`);
  // --- 修改: 添加交易WebSocket日志 ---
  console.log(`Trade WebSocket server running at ws://localhost:${config.port}/ws/trade`);
  // --- 结束修改 ---
  console.log('File uploads available at: /uploads');
});

// 打印所有路由的函数
function printRoutes(layer, prefix = '') {
  if (layer.route) {
    // 如果是路由层
    const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
    const path = prefix + (layer.route.path || '');
    console.log(`${methods} ${path}`);
  } else if (layer.name === 'router' && layer.handle.stack) {
    // 如果是嵌套的路由
    const newPrefix = prefix + cleanPath(layer.regexp);
    layer.handle.stack.forEach((sublayer) => {
      printRoutes(sublayer, newPrefix);
    });
  }
}

// 清理路径中的特殊字符
function cleanPath(regexp) {
  return regexp.source
    .replace('\\/?(?=\\/|$)', '') // 去掉 Express 添加的额外字符
    .replace(/\\\//g, '/') // 将 \/ 替换为 /
    .replace(/^\^/, ''); // 去掉开头的 ^
}

// 监视 /api/chat/session/default 路由的中间件
app.use('/api/chat/session/default', (req, res, next) => {
  console.log(`[Monitor] Request to /api/chat/session/default:`, {
    method: req.method,
    url: req.originalUrl,
    time: new Date().toISOString()
  });
  next();
});

// 打印所有路由
console.log('\nAvailable Routes:');
app._router.stack.forEach((layer) => {
  printRoutes(layer);
});