<!--
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<!DOCTYPE html>
<html lang="en" >
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="keywords" content="kline time-line candlestick stock chart canvas k线 行情 蜡烛图 分时图 技术指标 图表"/>
    <meta name="description" content="shape test"/>
    <script type="text/javascript" src="../../../dist/klinecharts.min.js"></script>
    <script type="text/javascript" src="../../js/dataSource.js"></script>
    <link rel="stylesheet" type="text/css" href="../../css/chart.css"/>
    <title>Overlay -- Use built-in overlays</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="chart"></div>
    <script>
      window.onload = function () {
        var chart = klinecharts.init('chart')
        var kdjPaneId = chart.createIndicator('KDJ')
        var dataList = generated()
        chart.applyNewData(dataList)
        // Create on main
        chart.createOverlay({
          name: 'simpleAnnotation',
          extendData: '1111111111',
          points: [{ timestamp: dataList[dataList.length - 20].timestamp, value: dataList[dataList.length - 20].high }]
        })
         // Create on sub
        chart.createOverlay({
          name: 'simpleAnnotation',
          extendData: '22222222222',
          points: [{ timestamp: dataList[dataList.length - 20].timestamp, value: 40 }]
        }, kdjPaneId)
      }
    </script>
  </body>
</html>
