import { IndicatorType } from '@/shared_types/market';
import { MACD, RSI, MA, EMA, BOLL, VOL, KDJ, SWMA, WMA } from './index_debutIndicatorsd_based';

// 定义指标映射
const indicatorCatalog: Record<string, any> = {
  [IndicatorType.MACD]: MACD,
  [IndicatorType.RSI]: RSI,
  [IndicatorType.MA]: MA,
  [IndicatorType.EMA]: EMA,
  [IndicatorType.BOLL]: BOLL,
  [IndicatorType.VOL]: VOL,
  [IndicatorType.KDJ]: KDJ,
  [IndicatorType.SWMA]: SWMA,
  [IndicatorType.WMA]: WMA,
};

// 获取指标类的函数
export const getIndicatorByType = (type: string) => {
  const indicator = indicatorCatalog[type];
  if (!indicator) {
    throw new Error(`未找到类型为 ${type} 的指标`);
  }
  return indicator;
};
