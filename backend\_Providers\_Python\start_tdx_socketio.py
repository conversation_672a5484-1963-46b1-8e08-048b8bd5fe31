#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TDX Socket.IO 服务器启动脚本
简化版启动脚本，避免复杂的事件循环问题
"""

import os
import sys
import threading
import time
import logging
from tdxserver import *

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='[%(asctime)s] [%(levelname)s] [%(module)s] %(message)s',
                   encoding='utf-8')
logger = logging.getLogger(__name__)

def start_flask_server():
    """启动 Flask HTTP 服务器"""
    try:
        logger.info("[启动器] 正在启动 Flask HTTP 服务器...")
        app.run(host='127.0.0.1', port=5003, debug=False, use_reloader=False)
    except Exception as e:
        logger.error(f"[启动器] Flask 服务器启动失败: {e}")

def start_socketio_server():
    """启动 Socket.IO 服务器"""
    try:
        logger.info("[启动器] 正在启动 Socket.IO 服务器...")
        web.run_app(aio_app, host='127.0.0.1', port=5004, access_log=logger)
    except Exception as e:
        logger.error(f"[启动器] Socket.IO 服务器启动失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("TDX Socket.IO 数据服务器")
    print("=" * 60)
    print("请确保通达信客户端 (tdxw.exe) 正在运行。")
    print()
    
    # 加载配置
    logger.info("[启动器] 正在加载配置...")
    global HQ_TDX_IP, HQ_TDX_PORT, EXHQ_TDX_IP, EXHQ_TDX_PORT
    
    loaded_config = load_server_config(CONFIG_FILE)
    HQ_TDX_IP = loaded_config['hq']['ip']
    HQ_TDX_PORT = loaded_config['hq']['port']
    EXHQ_TDX_IP = loaded_config['exhq']['ip']
    EXHQ_TDX_PORT = loaded_config['exhq']['port']
    
    # 执行动态检测
    logger.info("[启动器] 正在检测通达信连接参数...")
    detected_servers = get_tdx_connection_info()
    config_updated = False

    # 检查 HQ 更新
    if detected_servers['hq']:
        detected_hq_ip = detected_servers['hq']['ip']
        detected_hq_port = detected_servers['hq']['port']
        if detected_hq_ip != HQ_TDX_IP or detected_hq_port != HQ_TDX_PORT:
            HQ_TDX_IP = detected_hq_ip
            HQ_TDX_PORT = detected_hq_port
            config_updated = True

    # 检查 ExHq 更新
    if detected_servers['exhq']:
        detected_exhq_ip = detected_servers['exhq']['ip']
        detected_exhq_port = detected_servers['exhq']['port']
        if detected_exhq_ip != EXHQ_TDX_IP or detected_exhq_port != EXHQ_TDX_PORT:
            EXHQ_TDX_IP = detected_exhq_ip
            EXHQ_TDX_PORT = detected_exhq_port
            config_updated = True

    # 保存更新后的配置
    if config_updated:
        logger.info(f"[启动器] 检测到新的服务器地址，正在更新配置文件...")
        current_config = {
            'hq': {'ip': HQ_TDX_IP, 'port': HQ_TDX_PORT},
            'exhq': {'ip': EXHQ_TDX_IP, 'port': EXHQ_TDX_PORT}
        }
        save_server_config(CONFIG_FILE, current_config)

    # 打印检测结果
    hq_status = f"检测到普通行情接口: {HQ_TDX_IP}:{HQ_TDX_PORT}" if detected_servers['hq'] else "未检测到普通行情接口"
    exhq_status = f"检测到扩展行情接口: {EXHQ_TDX_IP}:{EXHQ_TDX_PORT}" if detected_servers['exhq'] else "未检测到扩展行情接口"
    logger.info(f"[启动器] {hq_status}")
    logger.info(f"[启动器] {exhq_status}")
    
    print()
    print("服务器配置:")
    print(f"  - HTTP API 服务: http://127.0.0.1:5003")
    print(f"  - Socket.IO 服务: http://127.0.0.1:5004")
    print(f"  - Socket.IO 连接路径: /socket.io")
    print()
    
    try:
        # 在单独线程中启动 Flask 服务器
        logger.info("[启动器] 启动 Flask HTTP 服务器线程...")
        flask_thread = threading.Thread(target=start_flask_server, daemon=True)
        flask_thread.start()
        
        # 等待一下确保 Flask 启动
        time.sleep(2)
        
        # 启动 Socket.IO 服务器（主线程）
        logger.info("[启动器] 启动 Socket.IO 服务器...")
        print("服务器启动完成！按 Ctrl+C 停止服务器。")
        print("=" * 60)
        
        start_socketio_server()
        
    except KeyboardInterrupt:
        logger.info("[启动器] 收到停止信号，正在关闭服务器...")
        print("\n正在停止服务器...")
    except Exception as e:
        logger.error(f"[启动器] 服务器运行错误: {e}")
        print(f"\n服务器运行错误: {e}")
    finally:
        # 停止 tick 数据线程
        global tick_data_running
        tick_data_running = False
        logger.info("[启动器] 服务器已停止")
        print("服务器已停止。")

if __name__ == '__main__':
    main()
