# 中心数据引擎 (live_DT)

## 概述

中心数据引擎是基于Wonder Trader数据引擎构建的统一行情数据服务，为实盘交易系统提供实时行情数据支持。

## 目录结构

```
live_DT/
├── runDT.py                    # 数据引擎启动脚本
├── dtcfg.yaml                  # 数据引擎主配置文件
├── contracts.json              # 合约配置文件（动态生成）
├── mdparsers.yaml              # 行情解析器配置
├── logcfgdt.yaml               # 日志配置
├── statemonitor.yaml           # 状态监控配置
├── start_data_engine.bat       # Windows启动脚本
├── DtLogs/                     # 日志目录（自动创建）
└── README.md                   # 说明文档
```

## 主要功能

1. **多数据源支持**
   - 通达信数据源（默认启用）
   - XTP数据源（可配置）
   - 自定义扩展解析器

2. **数据广播**
   - UDP广播：向局域网内其他系统转发行情数据
   - 共享内存：本机内进程间高效数据共享

3. **数据存储**
   - 实时tick数据存储
   - K线数据自动生成（1分钟、5分钟、日线）
   - L2逐笔数据存储（股票）

4. **状态监控**
   - 交易时段自动管理
   - 系统状态实时监控

## 配置说明

### 1. 数据源配置 (mdparsers.yaml)

```yaml
parsers:
-   active: true                 # 是否启用
    id: tdx_parser              # 解析器ID
    module: ParserTDX           # 解析器模块
    host: **************        # 服务器地址
    port: 7709                  # 服务器端口
    code: SSE.000001,SSE.600036 # 订阅代码列表
```

### 2. 存储配置 (dtcfg.yaml)

```yaml
writer:
    path: ../../storage         # 数据存储路径
    async: true                 # 异步存储
    savelog: true               # 保存tick到CSV
    disabletick: false          # 是否禁用tick存储
```

### 3. 日志配置 (logcfgdt.yaml)

```yaml
root:
    level: info                 # 日志级别
    sinks:
    -   type: daily_file_sink   # 按日分割的文件日志
        filename: DtLogs/DataEngine.log
```

## 启动方式

### Windows
```bash
# 方式1：直接运行批处理文件
start_data_engine.bat

# 方式2：命令行启动
cd live_DT
python runDT.py
```

### Linux/Mac
```bash
cd live_DT
python3 runDT.py
```

## 依赖关系

### 配置文件依赖
- `./contracts.json` - 合约信息（动态生成，位于当前目录）
- `../../common/stk_comms.json` - 品种信息
- `../../common/holidays.json` - 节假日信息
- `../../common/stk_sessions.json` - 交易时段信息

### 存储目录
- `../../storage/` - 数据存储目录
- `./DtLogs/` - 日志存储目录

## TDX Socket ExtParser集成

### 概述
数据引擎现已集成自定义ExtParser，可以接收来自tdxserver的Socket.IO实时数据推送，实现与Wonder Trader引擎的无缝对接。

### 数据流向
```
pytdx API → tdxserver → Socket.IO → TdxSocketParser → Wonder Trader引擎
```

### ExtParser功能
1. **自动连接**: 启动时自动连接到tdxserver的Socket.IO服务
2. **品种订阅**: 根据Wonder Trader的订阅请求动态订阅品种
3. **数据转换**: 将Socket数据转换为WTSTickStruct格式
4. **实时推送**: 将tick数据推送给Wonder Trader引擎

### 启动方式
```bash
# 1. 先启动tdxserver
cd backend/_Providers/_Python
python tdxserver.py

# 2. 再启动数据引擎(包含ExtParser)
cd backend/_Providers/_Python/strategy/portfolio/live_DT
python runDT.py
```

### 品种代码格式
- **格式**: 交易所.代码 (如 SZ.000001, SH.000001)
- **支持市场**: SH(上海), SZ(深圳), HK(香港), US(美国), SHFE(上期所)等
- **示例**:
  - SZ.000001 (平安银行)
  - SH.000001 (上证指数)
  - SH.600036 (招商银行)

### 在策略中使用
```python
# 在Wonder Trader策略的on_init方法中订阅
def on_init(self, context):
    # 订阅实时tick数据
    context.stra_sub_ticks('SZ.000001')  # 平安银行
    context.stra_sub_ticks('SH.000001')  # 上证指数

    # 订阅K线数据
    context.stra_prepare_bars('SZ.000001', 'day', 30)
```

### 监控和调试
1. **查看日志**: runDT.py控制台会显示ExtParser的连接和数据处理日志
2. **订阅状态**: 访问 http://127.0.0.1:5003/subscriptions 查看当前订阅状态
3. **测试工具**: 运行 `python test_extparser.py` 进行集成测试

## 使用场景

1. **实盘交易系统**
   - 为多个实盘策略提供统一的行情数据源
   - 支持多用户、多策略并发访问
   - 通过ExtParser接收实时tick数据

2. **数据采集**
   - 实时采集股票、ETF行情数据
   - 自动生成各周期K线数据
   - 支持自定义数据源扩展

3. **系统集成**
   - 通过UDP广播向其他系统转发数据
   - 通过共享内存为本机应用提供数据
   - 通过ExtParser对接第三方数据源

## 监控和维护

### 日志文件
- `DtLogs/DataEngine.log` - 主引擎日志
- `DtLogs/Parser.log` - 解析器日志
- `DtLogs/Storage.log` - 存储模块日志
- `DtLogs/Broadcaster.log` - 广播器日志

### 状态检查
- 检查日志文件是否正常生成
- 监控数据存储目录的文件更新
- 验证UDP广播端口是否正常工作

## 数据引擎管理API

### 订阅合约
```bash
POST /data_engine/subscribe
{
    "project_id": "live_project_123",
    "contract_codes": ["SSE.600036", "SZSE.000001", "SSE.510300"]
}
```

### 取消订阅
```bash
POST /data_engine/unsubscribe
{
    "project_id": "live_project_123"
}
```

### 重启数据引擎
```bash
POST /data_engine/restart
```

### 获取引擎状态
```bash
GET /data_engine/status
```

### 一键更新并重启
```bash
POST /data_engine/update_and_restart
{
    "project_id": "live_project_123",
    "contract_codes": ["SSE.600036", "SZSE.000001"]
}
```

## 集成说明

### 实盘项目启动时
1. 调用 `/data_engine/subscribe` 注册品种订阅
2. 调用 `/data_engine/restart` 重启数据引擎

### 实盘项目停止时
1. 调用 `/data_engine/unsubscribe` 取消订阅
2. 调用 `/data_engine/restart` 重启数据引擎

### 一键操作
使用 `/data_engine/update_and_restart` 可以一次完成订阅注册和引擎重启

## 注意事项

1. **路径配置**
   - 所有相对路径都基于 `live_DT` 目录
   - 确保依赖的配置文件存在于 `../../common/` 目录

2. **端口占用**
   - UDP广播端口：9001
   - UDP查询端口：3997
   - 确保端口未被其他程序占用

3. **权限要求**
   - 需要网络访问权限（访问行情服务器）
   - 需要文件写入权限（日志和数据存储）

4. **资源消耗**
   - 内存使用量取决于订阅的合约数量
   - 磁盘空间需求取决于数据保存策略

5. **进程管理**
   - 数据引擎作为子进程运行
   - 重启时会先停止旧进程再启动新进程
   - 支持优雅关闭和强制终止
