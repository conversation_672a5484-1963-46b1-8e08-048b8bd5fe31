import React, { useEffect, useRef, useState } from 'react';
import {
  create<PERSON>hart,
  IChartApi,
  ColorType,
  LineStyle,
  CrosshairMode,
  Time,
  LineData,
  ISeriesApi,
  LineWidth
} from 'lightweight-charts';
import { useTheme } from '@/models/useTheme';
import { IndicatorWrapper } from '@/_Modules/Indicators/IndicatorWrapper';
import { EventBus } from '@/events/eventBus';
import { ChartEvents, IndicatorEvents } from '@/events/events';
import './index.less';
import { KLineData } from '@/_Services/types';
import { IndicatorLineType } from '@/shared_types/indicator';
import { useAtom } from 'jotai';
import { globalActiveChart } from '@/store/state';
import { isChartSyncPauseAtom } from '@/store/state';

interface IndicatorProps {
  height: number;
  indicator: IndicatorWrapper;
  mainChart: IChartApi;  // 主图实例
  mainSeries: ISeriesApi<'Candlestick'>;  // 主图K线series
  onChartReady?: (chart: IChartApi) => void;  // 添加这个可选属性
  onDestroy?: () => void;                     // 添加这个可选属性
  onRenderComplete?: () => void; // 添加 onRenderComplete 属性
  closeButtonStyle?: React.CSSProperties; // 添加关闭按钮样式属性
}

// 将指标数据转换为图表库需要的格式
const convertToLineData = (values: number[], times: number[]): LineData<Time>[] => {
  return values.map((value, i) => ({
    time: times[i] as Time,
    value: value
  }));
};

/**
 * 指标面板组件
 * 用于显示和管理单个副图指标
 */
const Indicator: React.FC<IndicatorProps> = ({
  height,
  indicator,
  mainChart,
  mainSeries,
  onChartReady,
  onDestroy,
  onRenderComplete,
  closeButtonStyle
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const seriesRef = useRef<any[]>([]);  // 存储所有线条引用
  const { isDarkMode } = useTheme();

  const [isChartSyncPause] = useAtom(isChartSyncPauseAtom);  // 使用全局状态
  const myChartId = useRef(Math.random().toString(36).substr(2, 9)); // 生成唯一ID

  const [myIndicatorId, setMyIndicatorId] = useState('')

  // 添加事件处理函数
  const handleInteraction = () => {

    if (chartRef.current && myChartId.current !== globalActiveChart?.id) {

      globalActiveChart.instance = chartRef.current;
      globalActiveChart.id = myChartId.current;

    }
  };

  // 获取时区偏移量（单位：小时）
  const getTimezoneOffset = (): number => {
    // 获取当前时间的时区偏移量（单位：分钟）
    const offsetInMinutes = new Date().getTimezoneOffset();
    // 转换为小时并返回
    return -offsetInMinutes / 60;

  };

  // 辅助函数：将任何时间值转换为Time类型
  const toTime = (time: number | string | Date | object): Time => {
    if (typeof time === 'number') {
      return time as Time;
    } else if (typeof time === 'string') {
      return time as Time;
    } else if (time instanceof Date) {
      return Math.floor(time.getTime() / 1000) as Time;
    } else {
      // 对于对象类型，假设它是业务日期对象
      return time as Time;
    }
  };

  //-----调试------
  // 在 useEffect 的清理函数上方添加以下代码

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 改为监听 mousedown 和 touchstart
    container.addEventListener('mousedown', handleInteraction, { passive: true });
    container.addEventListener('touchstart', handleInteraction, { passive: true });
    container.addEventListener('mousemove', handleInteraction, { passive: true });

    // 清理函数
    return () => {
      container.removeEventListener('mousedown', handleInteraction);
      container.removeEventListener('mousemove', handleInteraction);
      container.removeEventListener('touchstart', handleInteraction);
    };
  }, []); // 依赖 activeChart

  // 处理图表初始化和事件订阅
  useEffect(() => {
    const chart = createChart(containerRef.current!, {
      height: height,
      layout: {
        background: { color: isDarkMode ? '#050505' : '#ffffff' },
        textColor: isDarkMode ? '#d0d0d0' : '#000000',
        attributionLogo: false,
        
      },
      watermark: {
        visible: true,
        text: myIndicatorId,
        fontSize: 15,
        fontFamily: 'Georgia, serif',
        color: 'black',
        vertAlign: 'top',
        horzAlign: 'center',
        fontStyle: '',
      },
      grid: {
        vertLines: { color: isDarkMode ? '#1f1f1f' : '#f0f0f0' },
        horzLines: { color: isDarkMode ? '#1f1f1f' : '#f0f0f0' },
      },
      rightPriceScale: {
        borderColor: isDarkMode ? '#1f1f1f' : '#f0f0f0',
        minimumWidth: mainChart.priceScale('right').width(),
      },
      timeScale: {
        borderColor: isDarkMode ? '#1f1f1f' : '#f0f0f0',
        visible: false,  // 隐藏时间轴
        timeVisible: false,  // 不显示时间标签
        secondsVisible: false,
      },
      crosshair: {
        mode: CrosshairMode.Normal,
      },
      handleScale: false,
      handleScroll: {
        vertTouchDrag: false  // 禁止纵向触摸拖动
      }
    });

    chartRef.current = chart;

    // 监听主图价格坐标轴宽度变化
    const handleMainPriceScaleWidth = () => {
      // 检查主图和子图是否都已初始化
      if (!mainChart || !chart) {
        console.log('[Indicator] 图表未完全初始化，跳过价格轴宽度同步');
        return;
      }

      try {
        const rightScale = mainChart.priceScale('right');
        if (!rightScale) {
          console.log('[Indicator] 主图右侧价格轴未找到');
          return;
        }

        const mainWidth = rightScale.width();
        const subRightScale = chart.priceScale('right');

        if (!subRightScale) {
          console.log('[Indicator] 子图右侧价格轴未找到');
          return;
        }

        subRightScale.applyOptions({
          minimumWidth: mainWidth,
        });
        console.log('[Indicator] 同步了价格轴宽度：', mainWidth);
      } catch (error) {
        console.log('[Indicator] 同步价格轴宽度时发生错误:', error);
      }
    };

    // 创建 ResizeObserver 监听主图容器尺寸变化
    const resizeObserver = new ResizeObserver(() => {
      handleMainPriceScaleWidth();
    });

    // 获取主图容器并开始监听
    const mainContainer = mainChart.chartElement();
    if (mainContainer) {
      resizeObserver.observe(mainContainer);
    }

    // 添加监听 CHART_RESIZE 事件，用于在品种周期变化或主图大小变化时同步价格轴宽度
    const chartResizeSubscription = EventBus.on(
      ChartEvents.Types.CHART_RESIZE,
      () => {
        console.log('[Indicator] 收到 CHART_RESIZE 事件，同步价格轴宽度');
        handleMainPriceScaleWidth();
      }
    );

    // 获取指标的线型配置
    const lineConfigs = indicator.getLineConfigs();
    const values = indicator.getValues();
    const times = indicator.getTimes();

    // 根据配置创建对应类型的线条
    seriesRef.current = lineConfigs.map(config => {
      let series;

      try {
        if (config.type === IndicatorLineType.Line) {
          series = chart.addLineSeries({
            color: config.color || '#2196F3',
            lineWidth: config.lineWidth ? Number(config.lineWidth) as LineWidth : 1 as LineWidth,
            lastValueVisible: true,
            crosshairMarkerVisible: false,
            lastPriceAnimation: 0,
            priceLineVisible: false,  // 隐藏最后价格线
          });
        } else if (config.type === IndicatorLineType.Histogram) {
          series = chart.addHistogramSeries({
            color: config.color || '#4CAF50',
            priceFormat: {
              type: 'volume',
            },
            priceScaleId: '',
            lastValueVisible: true,
            priceLineVisible: false,  // 隐藏最后价格线
          });
        }

        if (series) {
          // 确保所有系列都隐藏最新值标签
          series.applyOptions({
            lastValueVisible: true,
            priceLineVisible: false,
          });

          // 设置数据
          const data = times.map((time, i) => ({
            time: toTime(time + getTimezoneOffset() * 3600),  // lightweight chart 加上位移
            value: values[config.name]?.[i] ?? null
          })).filter(item => item.value !== null);

          series.setData(data);

        }

        console.log('[Indicator] addSeries 创建新的数据序列：', config);

        EventBus.emit(ChartEvents.Types.SERIES_CREATED, {
          id: indicator.id,
          name: config.name,
          series: series as any
        }); 
        
      } catch (error) {
        console.error(`Failed to create series for ${config.name}:`, error);
      }

      return series;

    }).filter(Boolean);

    // 订阅主图十字光标移动事件
    const mainChartCrosshairMoveHandler = (param: any) => {

      if (param.time && chartRef.current && seriesRef.current.length > 0) {

        const price = param.point?.y;
        if (price !== undefined) {

          // 使用第一个有效的 series  
          const firstValidSeries = seriesRef.current.find(s => s !== null);

          if (firstValidSeries && firstValidSeries.data && firstValidSeries.data().length > 0 && chartRef.current && chartRef.current.timeScale().getVisibleRange()) {
            // 不再设置主图为活动图表

            chartRef.current.setCrosshairPosition(price, param.time, firstValidSeries);
          }
        }
      }
    };

    // 订阅副图十字光标移动事件
    const subChartCrosshairHandler = (param: any) => {
      if (param.time) {
        if (mainSeries && mainSeries.data && mainSeries.data().length > 0 && mainChart.timeScale().getVisibleRange()) {
          // 同步主图十字光标，使用mainSeries作为参数
          mainChart.setCrosshairPosition(param.point?.y, param.time, mainSeries);

          // 不再设置当前副图为活动图表

          // 发出事件通知其他副图同步
          EventBus.emit(ChartEvents.Types.SYNC_CROSSHAIR, {
            time: param.time,
            point: param.point,
            sourceChart: chartRef.current
          });
        }
      }
    };

    // 订阅其他副图的十字光标同步事件
    const syncCrosshairSubscription = EventBus.on(ChartEvents.Types.SYNC_CROSSHAIR, (payload: any) => {

      if (chartRef.current && chartRef.current.timeScale().getVisibleRange() && payload.sourceChart !== chartRef.current && seriesRef.current.length > 0) {
        const firstValidSeries = seriesRef.current.find(s => s !== null);
        if (firstValidSeries && firstValidSeries.data && firstValidSeries.data().length > 0 && chartRef.current && chartRef.current.timeScale().getVisibleRange()) {

          chartRef.current.setCrosshairPosition(
            payload.point?.y,
            payload.time,
            firstValidSeries
          );
        }
      }
    });

    // 订阅事件
    mainChart.subscribeCrosshairMove(mainChartCrosshairMoveHandler);
    chart.subscribeCrosshairMove(subChartCrosshairHandler);

    // 初始同步
    const initialRange = mainChart.timeScale().getVisibleRange();
    if (initialRange) {
      // 确保chart有数据
      if (chart.timeScale().getVisibleRange()) {
        chart.timeScale().setVisibleRange(initialRange);
      }
    }

    // 在组件挂载时调用 onChartReady
    if (onChartReady) {
      onChartReady(chartRef.current);
    }

    // 在组件卸载时清理
    return () => {
      // 清理所有订阅
      mainChart.unsubscribeCrosshairMove(mainChartCrosshairMoveHandler);
      chart.unsubscribeCrosshairMove(subChartCrosshairHandler);
      syncCrosshairSubscription.unsubscribe();
      chartResizeSubscription.unsubscribe(); // 清理 CHART_RESIZE 事件监听
      resizeObserver.disconnect();
      chart.remove();

      // 在组件卸载时调用 onDestroy
      if (onDestroy) {
        onDestroy();
      }
    };
  }, [height, indicator, isDarkMode]); // 只保留必要的依赖项

  useEffect(() => {
    if (isChartSyncPause) {
      // 暂停同步逻辑
      console.log('同步已暂停，isChartSyncPause=', isChartSyncPause);
    } else {
      // 恢复同步逻辑
      console.log('同步已恢复，isChartSyncPause=', isChartSyncPause);
    }
  }, [isChartSyncPause]);

  const [isSyncing, setIsSyncing] = useState(false);

  // 时间轴同步效果
  useEffect(() => {
    if (chartRef.current && mainChart) {
      // 副图时间轴变化时同步主图
      const subChartHandler = (range: any) => {

        //console.log("[Indicator] 副图时间轴变化->主图，range=", range);
        // 只有当前图表是活动图表时才执行同步console.log("trying, 副图(", myChartId.current, ") 时间轴变化时同步主图，当前id=", globalActiveChart?.id, "");

        // 只有当前被选择副图可以向主图发出同步
        if (range && !isChartSyncPause && globalActiveChart?.id === myChartId.current) {
          // 获取主图当前的可见范围
          const currentRange = mainChart.timeScale().getVisibleLogicalRange();

          // 比较当前范围和新范围是否一致
          if (currentRange && (currentRange.from !== range.from || currentRange.to !== range.to)) {

            // 1. 首先设置主图的可见范围
            mainChart.timeScale().setVisibleLogicalRange(range);

            // 2. 获取副图的数据数量
            const subDataCount = seriesRef.current[0]?.data().length || 0; // 使用副图的第一个系列的数据总数

            // 3. 计算目标位置：range.to - subDataCount
            const targetPosition = range.to - subDataCount + 1;

            // 4. 使用 scrollToPosition 方法滚动到目标位置
            mainChart.timeScale().scrollToPosition(targetPosition, false); // false 表示不取消动画

            // 打印新的时间范围和目标位置
            const newRange = mainChart.timeScale().getVisibleLogicalRange();

          }
        }
      };

      // 主图时间轴变化时同步副图
      const mainChartHandler = (range: any) => {

        //console.log("[Indicator] 主图时间轴变化->副图，range=", range, 
        //" isChartSyncPause=", isChartSyncPause, " globalActiveChart.id=", globalActiveChart?.id, " myChartId.current=", myChartId.current);

        // 主图变化时，如果当前副图不是活动图表，则同步
        if (range && !isChartSyncPause && globalActiveChart?.id !== myChartId.current) {
          // 获取主图的数据数量
          const mainDataCount = mainSeries ? mainSeries.data().length : 0;

          // 1. 首先设置副图的可见范围
          chartRef.current?.timeScale().setVisibleLogicalRange(range);

          // 2. 计算目标位置：range.to - dataCount
          const targetPosition = range.to - mainDataCount + 1;

          // 3. 使用 scrollToPosition 方法滚动到目标位置
          chartRef.current?.timeScale().scrollToPosition(targetPosition, false); // false 表示不取消动画

          // 打印新的时间范围和目标位置
          const newRange = chartRef.current?.timeScale().getVisibleLogicalRange();

        }
      };

      // 订阅时间轴变化事件
      console.log("[Indicator] 主图幅图互相订阅时间轴变化事件");
      chartRef.current.timeScale().subscribeVisibleLogicalRangeChange(subChartHandler);
      mainChart.timeScale().subscribeVisibleLogicalRangeChange(mainChartHandler);

      return () => {
        // 清理订阅
        if (chartRef.current) {
          chartRef.current.timeScale().unsubscribeVisibleLogicalRangeChange(subChartHandler);
        }
        mainChart.timeScale().unsubscribeVisibleLogicalRangeChange(mainChartHandler);
      };
    }
  }, [mainChart, isChartSyncPause]); // 依赖 activeChart.id

  // 监控isChartSyncPause变化
  useEffect(() => {
    console.log("[Indicator] isChartSyncPause变化，isChartSyncPause=", isChartSyncPause);
  }, [isChartSyncPause]);

  // 处理删除指标
  const handleRemove = () => {
    console.log("[Indicator] 删除指标", indicator.type, "，id=", indicator.id);
    EventBus.emit(IndicatorEvents.Types.REMOVE_INDICATOR, {
      id: indicator.id
    });
  };

  useEffect(() => {

    console.log('[副图指标面板] 指标 ', indicator.type, ' 数据变化，全量替换');

    if (chartRef.current && indicator) {
      // 获取最新的指标数据
      const values = indicator.getValues();
      const times = indicator.getTimes();
      const lineConfigs = indicator.getLineConfigs();

      // 更新每个系列的数据
      seriesRef.current.forEach((series, index) => {
        if (series && lineConfigs[index]) {
          const data = times.map((time, i) => ({
            time: toTime(time + getTimezoneOffset() * 3600),
            value: values[lineConfigs[index].name]?.[i] ?? null
          })).filter(item => item.value !== null);

          series.setData(data);
        }
      });
    }
  }, [indicator.getValues()]); // 依赖于指标的值和参数


  // 监听指标参数变化
  useEffect(() => {
    console.log('[指标面板] 指标 ', indicator.type, ' 参数变化，暂时无动作');
  }, [indicator.getParams()])

  //尺寸变化
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 添加resize处理
    const resizeObserver = new ResizeObserver(entries => {
      const { width } = entries[0].contentRect;
      if (chartRef.current) {
        chartRef.current.applyOptions({ width });
        // 同步价格坐标轴宽度
        const mainWidth = mainChart.priceScale('right').width();
        chartRef.current.priceScale('right').applyOptions({
          minimumWidth: mainWidth,
        });
      }
    });

    // 监听副图容器
    resizeObserver.observe(container);
    // 监听主图容器
    const mainContainer = mainChart.chartElement();
    if (mainContainer) {
      resizeObserver.observe(mainContainer);
    }

    // 在清理函数中取消监听
    return () => {
      resizeObserver.disconnect();
    };
  }, [mainChart]); // 添加 mainChart 作为依赖

  return (
    <div style={{ position: 'relative', marginTop: 4, width: '100%' }}>
      {/* 图表容器 */}
      <div ref={containerRef} style={{ width: '100%' }} />

      {/* 参数配置按钮 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          right: 105,
          width: 20,
          height: 20,
          borderRadius: '20%',
          backgroundColor: 'rgba(128, 128, 128, 0.3)',
          color: 'var(--ant-color-text)', // 使用主题文本颜色
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          zIndex: 100,
        }}
        onClick={() => {
          EventBus.emit(IndicatorEvents.Types.SHOW_INDICATOR_PARAMS, {
            id: indicator.id,
          });
        }}
      >
        <span style={{ fontSize: 14, fontWeight: 'normal', lineHeight: '1', paddingBottom: '2px' }}>⚙</span>
      </div>

      {/* 关闭按钮 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          right: 80,
          width: 20,
          height: 20,
          borderRadius: '20%',
          backgroundColor: 'rgba(128, 128, 128, 0.3)',
          color: 'var(--ant-color-text)', // 使用主题文本颜色
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          zIndex: 100,
          ...closeButtonStyle // 合并传入的样式
        }}
        onClick={handleRemove}
      >
        {/* 叉号图标 */}
        <span style={{ fontSize: 14, fontWeight: 'normal', lineHeight: '24px' }}>X</span>
      </div>
    </div>
  );
};

// 获取不同指标的颜色
const getLineColor = (key: string, index: number) => {
  const colors = ['#2196F3', '#4CAF50', '#FFC107', '#E91E63'];
  return colors[index % colors.length];
};

export default Indicator;