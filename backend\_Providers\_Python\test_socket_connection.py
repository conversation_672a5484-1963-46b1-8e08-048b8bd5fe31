#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Socket.IO连接稳定性
用于验证tdxserver和runDT之间的连接问题修复
"""

import time
import socketio
import threading
from datetime import datetime

class TestSocketClient:
    """测试Socket.IO客户端"""
    
    def __init__(self, server_url="http://127.0.0.1:5004"):
        self.server_url = server_url
        self.sio = None
        self.connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5.0
        self.heartbeat_thread = None
        self.heartbeat_running = False
        self.test_symbols = ['sse:515790', 'sse:515880', 'sse:512560']  # 测试品种
        
    def connect(self):
        """连接到服务器"""
        print(f"[TestClient] 开始连接到 {self.server_url}")
        
        try:
            # 创建Socket.IO客户端
            self.sio = socketio.Client(
                reconnection=True,
                reconnection_attempts=self.max_reconnect_attempts,
                reconnection_delay=int(self.reconnect_delay),
                reconnection_delay_max=30
            )
            
            # 注册事件处理器
            self.sio.on('connect', self.on_connect)
            self.sio.on('disconnect', self.on_disconnect)
            self.sio.on('connected', self.on_connected)
            self.sio.on('tick_data', self.on_tick_data)
            self.sio.on('subscribed', self.on_subscribed)
            self.sio.on('error', self.on_error)
            self.sio.on('ping', self.on_ping)
            self.sio.on('pong', self.on_pong)
            
            # 连接到服务器
            self.sio.connect(self.server_url, wait_timeout=10)
            
            print(f"[TestClient] Socket连接成功")
            return True
            
        except Exception as e:
            print(f"[TestClient] Socket连接失败: {e}")
            self._schedule_reconnect()
            return False
    
    def disconnect(self):
        """断开连接"""
        print(f"[TestClient] 断开连接")
        self.heartbeat_running = False
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.heartbeat_thread.join(timeout=2)
        if self.sio and self.connected:
            self.sio.disconnect()
        self.connected = False
    
    def subscribe_test_symbols(self):
        """订阅测试品种"""
        for symbol in self.test_symbols:
            market, code = symbol.split(':')
            print(f"[TestClient] 订阅品种: {symbol}")
            self.sio.emit('subscribe_tick', {
                'market': market,
                'symbol': code
            })
    
    def on_connect(self):
        """连接成功事件"""
        print(f"[TestClient] Socket连接成功 - {datetime.now()}")
        self.connected = True
        self.reconnect_attempts = 0
        
        # 启动心跳线程
        self._start_heartbeat()
    
    def on_disconnect(self):
        """断开连接事件"""
        print(f"[TestClient] Socket连接断开 - {datetime.now()}")
        self.connected = False
        self.heartbeat_running = False
        
        # 尝试重连
        self._schedule_reconnect()
    
    def on_connected(self, data):
        """收到服务器连接确认"""
        print(f"[TestClient] 收到服务器连接确认: {data}")
        # 订阅测试品种
        self.subscribe_test_symbols()
    
    def on_tick_data(self, data):
        """收到tick数据"""
        symbol = data.get('symbol', 'Unknown')
        tick_data = data.get('data', {})
        price = tick_data.get('price', 0)
        timestamp = data.get('timestamp', 0)
        print(f"[TestClient] 收到tick数据: {symbol} 价格={price} 时间={timestamp}")
    
    def on_subscribed(self, data):
        """订阅成功确认"""
        print(f"[TestClient] 订阅成功: {data}")
    
    def on_error(self, data):
        """错误事件"""
        print(f"[TestClient] Socket错误: {data}")
    
    def on_ping(self):
        """收到服务器PING"""
        print(f"[TestClient] 收到服务器PING - {datetime.now()}")
    
    def on_pong(self):
        """收到服务器PONG"""
        print(f"[TestClient] 收到服务器PONG - {datetime.now()}")
    
    def _start_heartbeat(self):
        """启动心跳线程"""
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            return
        
        self.heartbeat_running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
        self.heartbeat_thread.start()
        print(f"[TestClient] 心跳线程已启动")
    
    def _heartbeat_worker(self):
        """心跳工作线程"""
        while self.heartbeat_running and self.connected:
            try:
                if self.sio and self.connected:
                    # 发送心跳ping
                    self.sio.emit('ping')
                    print(f"[TestClient] 发送心跳PING - {datetime.now()}")
                time.sleep(25)  # 每25秒发送一次心跳
            except Exception as e:
                print(f"[TestClient] 心跳发送失败: {e}")
                break
        print(f"[TestClient] 心跳线程已停止")
    
    def _schedule_reconnect(self):
        """安排重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            print(f"[TestClient] 已达到最大重连次数 ({self.max_reconnect_attempts})，停止重连")
            return
        
        self.reconnect_attempts += 1
        print(f"[TestClient] 安排重连 (第 {self.reconnect_attempts} 次)，{self.reconnect_delay} 秒后重连")
        
        # 在新线程中执行重连，避免阻塞
        reconnect_thread = threading.Thread(target=self._reconnect_worker, daemon=True)
        reconnect_thread.start()
    
    def _reconnect_worker(self):
        """重连工作线程"""
        time.sleep(self.reconnect_delay)
        
        if not self.connected:
            print(f"[TestClient] 开始第 {self.reconnect_attempts} 次重连...")
            if self.connect():
                print(f"[TestClient] 重连成功")
            else:
                print(f"[TestClient] 重连失败")
                # 递增延迟时间，但不超过30秒
                self.reconnect_delay = min(self.reconnect_delay * 1.5, 30)

def main():
    """主测试函数"""
    print("=" * 60)
    print("Socket.IO连接稳定性测试")
    print("=" * 60)
    
    # 创建测试客户端
    client = TestSocketClient()
    
    # 连接到服务器
    if not client.connect():
        print("初始连接失败，但会自动重连")
    
    try:
        print("\n测试运行中...")
        print("- 监控连接状态")
        print("- 测试心跳机制")
        print("- 测试断线重连")
        print("- 按 Ctrl+C 退出")
        print("=" * 60)
        
        # 保持运行状态
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n正在停止测试...")
        client.disconnect()
        print("测试已停止")

if __name__ == "__main__":
    main()
