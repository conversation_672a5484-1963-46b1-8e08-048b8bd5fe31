import mitt from 'mitt';
import { ChartEvents, ChatEvents, IndicatorEvents, MarketEvents, UserEvents, RealtimeEvents, KeyboardEvents, StrategyEvents } from './events';
import { Symbol } from '../shared_types/market';

type Events = {
  // 市场事件
  [MarketEvents.Types.CALCULATE_SIGNALS]: MarketEvents.CalculateSignals;
  [MarketEvents.Types.SIGNALCALCULATED_RESULT]: MarketEvents.SignalsCalculatedResultPayload;
  [MarketEvents.Types.SIGNALS_ERROR]: MarketEvents.SignalsError;
  [MarketEvents.Types.SEARCH_SYMBOLS]: MarketEvents.SearchSymbols;
  [MarketEvents.Types.SEARCH_RESULT]: MarketEvents.SearchResult;
  [MarketEvents.Types.SEARCH_ERROR]: MarketEvents.SearchError;
  [MarketEvents.Types.GET_KLINES]: MarketEvents.GetKLines;
  [MarketEvents.Types.KLINES_ERROR]: MarketEvents.KLinesError;
  [MarketEvents.Types.REFRESH_SYMBOLS]: MarketEvents.RefreshSymbols;
  [MarketEvents.Types.REFRESH_RESULT]: MarketEvents.RefreshResult;
  [MarketEvents.Types.REFRESH_ERROR]: MarketEvents.RefreshError;
  [MarketEvents.Types.SIGNAL_MARKERS_VISIBLE]: MarketEvents.SignalMarkersVisible;
  [MarketEvents.Types.SIGNAL_REMOVEOPTION]: MarketEvents.SignalRemoveOptionPayload;

  [MarketEvents.Types.KLINES_READY]: MarketEvents.KLinesReady;
  [MarketEvents.Types.KLINES_UPDATED]: MarketEvents.KLinesUpdated;
  [MarketEvents.Types.CALCULATE_BARCOUNT]: MarketEvents.CalculateBarCounts;
  [MarketEvents.Types.BARS_COUNT_RESULT]: MarketEvents.BarCountResult;

  [MarketEvents.Types.SYMBOLLIST_CHANGED]: MarketEvents.SymbolListChanged;
  [MarketEvents.Types.SYMBOL_CHANGED]: MarketEvents.SymbolChangedPayload;
  [MarketEvents.Types.REQUEST_SYMBOLLIST_DATA]: MarketEvents.RequestSymbolListDataPayload;
  [MarketEvents.Types.UPDATE_SYMBOLLIST]: MarketEvents.UpdateSymbolListPayload;
  [MarketEvents.Types.SYMBOLLIST_UPDATED]: MarketEvents.SymbolListUpdatedPayload;
  [MarketEvents.Types.SHOW_STOCK_POOL_MANAGER]: MarketEvents.ShowStockPoolManagerPayload;

  [MarketEvents.Types.SAVE_SHAPECONFIGS]: MarketEvents.SaveShapeConfig;
  [MarketEvents.Types.SAVE_SHAPECONFIGS_RESULT]: MarketEvents.SaveShapeConfigsResult;
  [MarketEvents.Types.GET_SHAPECONFIGDETAILS]: MarketEvents.GetShapeConfigDetailsPayload;
  [MarketEvents.Types.GET_ALLSHAPECONFIGS]: MarketEvents.GetShapeConfigs;
  [MarketEvents.Types.GET_ALLSHAPECONFIGS_RESULT]: MarketEvents.GetShapeConfigsResultPayload;
  [MarketEvents.Types.SIGNAL_OPTIONREMOVED]: MarketEvents.SignalOptionRemovedPayload;

  // 图表事件
  [ChartEvents.Types.SYNC_CROSSHAIR]: ChartEvents.SyncCrosshairPayload;
  [ChartEvents.Types.PAUSE_SYNC]: ChartEvents.PauseSyncPayload;
  [ChartEvents.Types.CHART_RESIZE]: ChartEvents.ChartResizePayload;
  [ChartEvents.Types.SAVE_CHART_CONFIG]: ChartEvents.SaveChartConfigPayload;
  [ChartEvents.Types.RANGE_SELECT_MODE_CHANGED]: ChartEvents.RangeSelectModeChanged;
  [ChartEvents.Types.RANGE_SELECTED]: ChartEvents.RangeSelected;
  [ChartEvents.Types.CHART_MOUSE_EVENT]: ChartEvents.ChartMouseEvent;
  [ChartEvents.Types.RANGE_OPERATION]: ChartEvents.RangeOperation;
  [ChartEvents.Types.RANGE_ANALYZE]: ChartEvents.RangeAnalyzePayload;
  [ChartEvents.Types.CHART_CONFIG_READY]: ChartEvents.ChartConfigReadyPayload;
  [ChartEvents.Types.GET_DRAWING_LINES]: ChartEvents.GetDrawingLinesPayload;
  [ChartEvents.Types.ADD_DRAWING_LINE]: ChartEvents.AddDrawingLinePayload;
  [ChartEvents.Types.DELETE_DRAWING_LINE]: ChartEvents.DeleteDrawingLinePayload;
  [ChartEvents.Types.DELETE_ALL_DRAWING_LINES]: ChartEvents.DeleteAllDrawingLinesPayload;
  //[ChartEvents.Types.DRAWING_LINES_READY]: ChartEvents.DrawingLinesReadyPayload;
  [ChartEvents.Types.APPLY_DRAWING_LINES]: ChartEvents.ApplyDrawingLinesPayload;
  [ChartEvents.Types.UPDATE_DRAWING_LINE]: ChartEvents.UpdateDrawingLinePayload;
  [ChartEvents.Types.CLEAR_CHART_DRAWING_LINES]: undefined;
  [ChartEvents.Types.SERIES_CREATED]: ChartEvents.SeriesCreatedPayload;
  [ChartEvents.Types.TOGGLE_SIGNAL_LIST]: ChartEvents.ToggleSignalListPayload;
  [ChartEvents.Types.TOGGLE_SYMBOLLIST]: ChartEvents.ToggleSymbolListPayload;
  [ChartEvents.Types.CHART_SCROLL_TO_INDEX]: ChartEvents.ChartScrollToIndexPayload;
  [ChartEvents.Types.DRAWING_TOOL_SELECTED]: ChartEvents.DrawingToolSelectedPayload;
  [ChartEvents.Types.DRAWING_COMPLETED]: ChartEvents.DrawingCompletedPayload;
  [ChartEvents.Types.SAVE_DRAWING_LINES]: ChartEvents.SaveDrawingLinesPayload;
  // 指标事件
  [IndicatorEvents.Types.ADD_INDICATOR]: IndicatorEvents.AddIndicatorPayload;
  [IndicatorEvents.Types.REMOVE_INDICATOR]: IndicatorEvents.RemoveIndicatorPayload;
  [IndicatorEvents.Types.UPDATE_INDICATOR]: IndicatorEvents.UpdateIndicatorPayload;
  [IndicatorEvents.Types.INDICATOR_PARAMS_EDITED]: IndicatorEvents.UpdateIndicatorPayload;
  [IndicatorEvents.Types.INDICATOR_CALCULATED]: IndicatorEvents.IndicatorCalculatedPayload;
  [IndicatorEvents.Types.INDICATOR_NEWDATACALCULATED]: IndicatorEvents.IndicatorNewDataCalculatedPayload;

  [IndicatorEvents.Types.SHOW_INDICATOR_PARAMS]: IndicatorEvents.ShowIndicatorParamsPayload;
  [IndicatorEvents.Types.SHOW_PARAMS_MODAL]: IndicatorEvents.ShowParamsModalPayload;

  // 用户事件
  [UserEvents.Types.LOGIN]: UserEvents.Payload;
  [UserEvents.Types.LOGIN_RESULT]: UserEvents.ResultPayload;
  [UserEvents.Types.REGISTER]: UserEvents.Payload;
  [UserEvents.Types.REGISTER_RESULT]: UserEvents.ResultPayload;
  [UserEvents.Types.UPDATE]: { username: string; [key: string]: any };
  [UserEvents.Types.UPDATE_RESULT]: UserEvents.ResultPayload;
  [UserEvents.Types.LOGOUT]: undefined;
  [UserEvents.Types.SEND_SMS_CODE]: UserEvents.SMSCodePayload;
  [UserEvents.Types.SMS_CODE_RESULT]: UserEvents.SMSCodeResultPayload;
  [UserEvents.Types.SEND_EMAIL_CODE]: UserEvents.EmailCodePayload;
  [UserEvents.Types.APPROVE_USER]: UserEvents.ApproveUserPayload;
  [UserEvents.Types.APPROVE_USER_RESULT]: UserEvents.ApproveUserResultPayload;
  [UserEvents.Types.RESET_PASSWORD]: UserEvents.ResetPasswordPayload;
  [UserEvents.Types.RESET_PASSWORD_RESULT]: UserEvents.ResultPayload;

  // 聊天事件
  [ChatEvents.Types.SEND_MESSAGE]: ChatEvents.Message;
  [ChatEvents.Types.MESSAGE_SENT]: ChatEvents.Message;
  [ChatEvents.Types.RECEIVE_MESSAGE]: ChatEvents.Message;
  [ChatEvents.Types.RECALL_MESSAGE]: number;
  [ChatEvents.Types.MESSAGE_RECALLED]: number;
  [ChatEvents.Types.MARK_AS_READ]: null;
  [ChatEvents.Types.SEARCH_MESSAGES]: string;
  [ChatEvents.Types.SEARCH_RESULT]: ChatEvents.Message[];
  [ChatEvents.Types.UPLOAD_FILE]: File;
  [ChatEvents.Types.FILE_UPLOADED]: ChatEvents.FileUploaded;
  [ChatEvents.Types.GET_DEFAULT_SESSION]: void;
  [ChatEvents.Types.SESSION_UPDATED]: number;
  [ChatEvents.Types.UPLOAD_STARTED]: ChatEvents.FileUpload;
  [ChatEvents.Types.UPLOAD_PROGRESS]: ChatEvents.FileProgress;
  [ChatEvents.Types.UPLOAD_COMPLETED]: ChatEvents.FileCompleted;
  [ChatEvents.Types.UPLOAD_FAILED]: ChatEvents.FileUpload;
  [ChatEvents.Types.CHAT_HISTORY_RECEIVED]: ChatEvents.Message[];
  [ChatEvents.Types.TOGGLE_CHAT_WINDOW]: boolean;

  // 键盘事件
  [KeyboardEvents.Types.KEY_PRESSED]: KeyboardEvents.KeyPressed;
  [KeyboardEvents.Types.INPUT_STARTED]: KeyboardEvents.InputStarted;
  [KeyboardEvents.Types.INPUT_CANCELLED]: KeyboardEvents.InputCancelled;
  [KeyboardEvents.Types.INPUT_COMPLETE]: KeyboardEvents.InputComplete;

  // 实时数据事件
  [RealtimeEvents.Types.REALTIME_SUBSCRIBE]: RealtimeEvents.SubscribePayload;
  [RealtimeEvents.Types.REALTIME_UNSUBSCRIBE]: RealtimeEvents.SubscribePayload;
  [RealtimeEvents.Types.REALTIME_DATA_START]: undefined;
  [RealtimeEvents.Types.REALTIME_DATA_END]: undefined;
  [RealtimeEvents.Types.REALTIME_ERROR]: RealtimeEvents.ErrorPayload;
  [RealtimeEvents.Types.REALTIME_RECONNECT]: undefined;

  // 策略事件
  [StrategyEvents.Types.GET_STRATEGIES_LIST]: StrategyEvents.GetStrategiesListPayload;
  [StrategyEvents.Types.STRATEGY_LIST_UPDATED]: undefined;
  [StrategyEvents.Types.DEPLOY_STRATEGY]: StrategyEvents.DeployStrategyPayload;
  [StrategyEvents.Types.STRATEGY_DEPLOYED]: StrategyEvents.StrategyDeployedPayload;
  [StrategyEvents.Types.RUN_BACKTEST]: StrategyEvents.RunBacktestPayload;
  [StrategyEvents.Types.RUN_STRATEGY_BACKTEST]: StrategyEvents.RunStrategyBacktestPayload;
  [StrategyEvents.Types.BACKTEST_RESULT]: StrategyEvents.BacktestResultPayload;
  [StrategyEvents.Types.GET_STRATEGY_DETAILS]: StrategyEvents.GetStrategyDetailsPayload;
  [StrategyEvents.Types.SHOW_STRATEGY_DETAILS]: StrategyEvents.ShowStrategyDetailsPayload;
  [StrategyEvents.Types.COPY_STRATEGY]: StrategyEvents.CopyStrategyPayload;
  [StrategyEvents.Types.STRATEGY_UPDATED]: StrategyEvents.StrategyUpdatedPayload;
  [StrategyEvents.Types.UPDATE_STRATEGY]: StrategyEvents.StrategyUpdatePayload;

  // 实时策略事件
  [StrategyEvents.Types.GET_LIVE_STRATEGIES]: StrategyEvents.GetLiveStrategyListPayload;
  [StrategyEvents.Types.LIVE_STRATEGY_LIST_UPDATED]: StrategyEvents.LiveStrategyListUpdatedPayload;
  [StrategyEvents.Types.START_LIVE_STRATEGY]: StrategyEvents.StartLiveStrategyPayload;
  [StrategyEvents.Types.LIVE_STRATEGY_STARTED]: StrategyEvents.LiveStrategyStartedPayload;
  [StrategyEvents.Types.STOP_LIVE_STRATEGY]: StrategyEvents.StopLiveStrategyPayload;
  [StrategyEvents.Types.LIVE_STRATEGY_STOPPED]: StrategyEvents.LiveStrategyStoppedPayload;
  [StrategyEvents.Types.GET_LIVE_STRATEGY_LOGS]: StrategyEvents.GetLiveStrategyLogsPayload;
  [StrategyEvents.Types.LIVE_STRATEGY_LOGS_UPDATED]: StrategyEvents.LiveStrategyLogsUpdatedPayload;
  [StrategyEvents.Types.DELETE_LIVE_STRATEGY]: StrategyEvents.DeleteLiveStrategyPayload;
  [StrategyEvents.Types.SHOW_LIVE_CONFIG]: StrategyEvents.ShowLiveConfigPayload;
  [StrategyEvents.Types.LIVE_STRATEGY_UPDATED]: undefined;
  [StrategyEvents.Types.DEPLOY_TO_LIVE]: StrategyEvents.DeployToLivePayload;
  [StrategyEvents.Types.UPDATE_LIVE_STRATEGY]: StrategyEvents.UpdateLiveStrategyPayload;
  [StrategyEvents.Types.GET_AVAILABLE_TRADING_CHANNELS]: StrategyEvents.GetAvailableTradingChannelsPayload;
  [StrategyEvents.Types.GET_TRADING_CHANNEL_CONFIG]: StrategyEvents.GetTradingChannelConfigPayload;
  [StrategyEvents.Types.SHOW_TRADING_CHANNEL_CONFIG]: StrategyEvents.ShowTradingChannelConfigPayload;
};

// 创建 mitt 实例
const eventBus = mitt<Events>();

// 定义 EventBus 接口
interface EventBus {
  on: <K extends keyof Events>(type: K, handler: (data: Events[K]) => void) => {
    unsubscribe: () => void
  };
  emit: <K extends keyof Events>(type: K, data: Events[K]) => void;
  off: <K extends keyof Events>(type: K, handler: (data: Events[K]) => void) => void;
}

// 添加一个 Map 来跟踪事件订阅
const subscriptionMap = new Map<string, Set<Function>>();

// 创建 loggedEventBus 单例
export const EventBus: EventBus = {

  on: (type: keyof Events, handler: any) => {
    // 检查重复订阅
    if (!subscriptionMap.has(type)) {
      subscriptionMap.set(type, new Set());
    }
    const handlers = subscriptionMap.get(type)!;

    if (handlers.has(handler)) {
      console.warn('[事件总线] 检测到重复订阅:', type);
      return {
        unsubscribe: () => { }
      };
    }

    console.log('[事件总线] 订阅事件:', type);
    const wrappedHandler = (payload: any) => {
      if (!type.includes('mouse') && !type.includes('chart:')) {
        console.log('[事件总线] 接收到事件:', type, {
          type,
          payload,
          timestamp: new Date()
        });
      }

      handler(payload);
    };

    handlers.add(handler);
    eventBus.on(type, wrappedHandler);

    return {
      unsubscribe: () => {
        eventBus.off(type, wrappedHandler);
        handlers.delete(handler);
      }
    };
  },

  emit: (type: keyof Events, payload?: any) => {
    if (type != ChartEvents.Types.SYNC_CROSSHAIR && !type.includes('mouse') ) {
      console.log('[事件总线] 发布事件:', type, {
        type,
        payload,
        timestamp: new Date()
      });
    }
    eventBus.emit(type, payload);
  },

  off: (type: keyof Events, handler: any) => {
    eventBus.off(type, handler);
    const handlers = subscriptionMap.get(type);
    if (handlers) {
      handlers.delete(handler);
    }
  }
};

// 导出单例
export type { Events }; // 导出 Events 类型