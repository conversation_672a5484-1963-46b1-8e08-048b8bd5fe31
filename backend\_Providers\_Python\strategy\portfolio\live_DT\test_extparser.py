#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试TDX ExtParser功能
验证Socket数据接收和WTSTickStruct转换
"""

import time
import requests
import json

# 测试配置
TDX_SERVER_URL = "http://127.0.0.1:5003"
SOCKET_SERVER_URL = "http://127.0.0.1:5004"

# 测试品种
TEST_SYMBOLS = [
    {"market": "sh", "symbol": "000001", "name": "上证指数", "fullcode": "SH.000001"},
    {"market": "sz", "symbol": "000001", "name": "平安银行", "fullcode": "SZ.000001"},
    {"market": "sz", "symbol": "000002", "name": "万科A", "fullcode": "SZ.000002"},
]

def check_tdx_server():
    """检查TDX服务器状态"""
    print("=" * 60)
    print("检查TDX服务器状态")
    print("=" * 60)
    
    try:
        # 测试基本连接
        response = requests.get(f"{TDX_SERVER_URL}/quote?market=sh&code=000001", timeout=5)
        if response.status_code == 200:
            print("✓ TDX HTTP服务器运行正常")
            
            # 测试tick接口
            response = requests.get(f"{TDX_SERVER_URL}/tick?market=sh&code=000001", timeout=5)
            if response.status_code == 200:
                print("✓ TDX Tick接口运行正常")
                return True
            else:
                print(f"✗ TDX Tick接口异常: {response.status_code}")
                return False
        else:
            print(f"✗ TDX HTTP服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到TDX服务器: {e}")
        print("请确保tdxserver.py正在运行")
        return False

def check_socket_server():
    """检查Socket服务器状态"""
    print("\n检查Socket.IO服务器状态")
    print("-" * 40)
    
    try:
        import socketio
        
        # 创建测试客户端
        sio = socketio.Client()
        connected = False
        
        @sio.event
        def connect():
            nonlocal connected
            connected = True
            print("✓ Socket.IO服务器连接成功")
        
        @sio.event
        def connect_error(data):
            print(f"✗ Socket.IO连接错误: {data}")
        
        # 尝试连接
        sio.connect(SOCKET_SERVER_URL, wait_timeout=5)
        time.sleep(1)
        
        if connected:
            sio.disconnect()
            return True
        else:
            print("✗ Socket.IO服务器连接失败")
            return False
            
    except Exception as e:
        print(f"✗ Socket.IO测试异常: {e}")
        return False

def get_subscription_stats():
    """获取订阅统计信息"""
    try:
        response = requests.get(f"{TDX_SERVER_URL}/subscriptions", timeout=5)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"获取订阅统计失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"获取订阅统计异常: {e}")
        return None

def test_extparser_integration():
    """测试ExtParser集成"""
    print("\n" + "=" * 60)
    print("测试ExtParser集成")
    print("=" * 60)
    
    print("\n1. 检查数据引擎是否运行...")
    print("   请确保runDT.py正在运行")
    print("   数据引擎应该已经连接到TDX Socket服务器")
    
    print("\n2. 检查订阅状态...")
    stats = get_subscription_stats()
    if stats:
        print(f"   当前订阅品种数: {stats.get('total_symbols', 0)}")
        print(f"   当前客户端数: {stats.get('total_clients', 0)}")
        active_symbols = stats.get('active_symbols', {})
        if active_symbols:
            print("   活跃订阅:")
            for symbol, count in active_symbols.items():
                print(f"     {symbol}: {count} 个订阅")
        else:
            print("   无活跃订阅")
    
    print("\n3. 模拟ExtParser订阅...")
    print("   ExtParser应该通过以下方式订阅品种:")
    for symbol_info in TEST_SYMBOLS:
        fullcode = symbol_info["fullcode"]
        name = symbol_info["name"]
        print(f"   - parser.subscribe('{fullcode}')  # {name}")
    
    print("\n4. 验证数据流...")
    print("   数据流向应该是:")
    print("   pytdx → tdxserver → Socket.IO → ExtParser → WonderTrader引擎")
    
    print("\n5. 检查日志输出...")
    print("   在runDT.py的控制台中应该看到:")
    print("   - [TdxSocketParser] Socket连接成功")
    print("   - [TdxSocketParser] 订阅品种: XXX")
    print("   - [TdxSocketParser] 推送tick数据: XXX")

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("ExtParser使用说明")
    print("=" * 60)
    
    print("\n启动顺序:")
    print("1. 启动tdxserver.py")
    print("   cd backend/_Providers/_Python")
    print("   python tdxserver.py")
    
    print("\n2. 启动数据引擎(包含ExtParser)")
    print("   cd backend/_Providers/_Python/strategy/portfolio/live_DT")
    print("   python runDT.py")
    
    print("\n3. 在策略中订阅品种")
    print("   # 在Wonder Trader策略的on_init中:")
    print("   context.stra_sub_ticks('SZ.000001')  # 订阅平安银行")
    print("   context.stra_sub_ticks('SH.000001')  # 订阅上证指数")
    
    print("\n数据格式:")
    print("- 品种代码格式: 交易所.代码 (如 SZ.000001, SH.000001)")
    print("- 支持的交易所: SH(上海), SZ(深圳), HK(香港), US(美国)等")
    print("- 数据字段: 价格、成交量、买卖五档、时间戳等")
    
    print("\n监控方法:")
    print("- 查看runDT.py控制台日志")
    print("- 访问 http://127.0.0.1:5003/subscriptions 查看订阅状态")
    print("- 检查Wonder Trader引擎日志")

def main():
    """主测试函数"""
    print("TDX ExtParser集成测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 检查服务器状态
    tdx_ok = check_tdx_server()
    socket_ok = check_socket_server()
    
    if not tdx_ok:
        print("\n❌ TDX服务器不可用，请先启动tdxserver.py")
        return
    
    if not socket_ok:
        print("\n❌ Socket.IO服务器不可用，请检查tdxserver.py的Socket功能")
        return
    
    print("\n✅ 基础服务器检查通过")
    
    # 测试ExtParser集成
    test_extparser_integration()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
