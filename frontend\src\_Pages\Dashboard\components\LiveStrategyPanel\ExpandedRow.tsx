import React, { useState, useEffect } from 'react';
import { Tabs, Timeline, List, Table, Card, Row, Col, Statistic, Tag, Space, Empty, Spin } from 'antd';
import {
  FileTextOutlined,
  StockOutlined,
  HistoryOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import { LiveStrategyInfo } from '@/shared_types/trade';
import axios from 'axios';
import { getToken } from '@/utils/auth';

interface ExpandedRowProps {
  record: LiveStrategyInfo;
}

// 日志数据类型
interface LogEntry {
  time: string;
  level: 'info' | 'success' | 'warning' | 'error';
  message: string;
}

// 持仓数据类型
interface Position {
  symbol: string;
  name: string;
  quantity: number;
  price: number;
  cost: number;
  profit: number;
  profitPercent: number;
}

// 交易记录类型
interface TradeRecord {
  time: string;
  symbol: string;
  name: string;
  direction: 'buy' | 'sell';
  quantity: number;
  price: number;
  amount: number;
}

// 日志图标映射
const logIconMap = {
  info: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
  success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
  warning: <WarningOutlined style={{ color: '#faad14' }} />,
  error: <CloseCircleOutlined style={{ color: '#f5222d' }} />,
};

const ExpandedRow: React.FC<ExpandedRowProps> = ({ record }) => {
  // 状态管理
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [trades, setTrades] = useState<TradeRecord[]>([]);
  const [loading, setLoading] = useState({
    logs: true,
    positions: true,
    trades: true,
    performance: true
  });
  const [error, setError] = useState({
    logs: null as string | null,
    positions: null as string | null,
    trades: null as string | null,
    performance: null as string | null
  });

  // 获取策略日志
  const fetchStrategyLogs = async () => {
    try {
      setLoading(prev => ({ ...prev, logs: true }));
      const token = getToken();

      if (!token) {
        throw new Error('未找到认证Token');
      }

      const response = await axios.get(`/api/strategy/live/${record.id}/logs`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success) {
        setLogs(response.data.data || []);
        setError(prev => ({ ...prev, logs: null }));
      } else {
        setError(prev => ({ ...prev, logs: response.data?.error || '获取日志失败' }));
      }
    } catch (err) {
      console.error('获取策略日志失败:', err);
      setError(prev => ({ ...prev, logs: '获取日志失败，请检查网络连接' }));
    } finally {
      setLoading(prev => ({ ...prev, logs: false }));
    }
  };

  // 获取持仓数据
  const fetchPositions = async () => {
    try {
      setLoading(prev => ({ ...prev, positions: true }));

      // 如果有真实持仓数据，直接使用
      if (record.performance?.positions && record.performance.positions.length > 0) {
        // 转换数据格式
        const positionData = record.performance.positions.map(pos => ({
          symbol: pos.symbol,
          name: pos.symbol.split('.')[1] || pos.symbol, // 简单提取名称，实际应从股票信息API获取
          quantity: pos.volume,
          price: pos.price,
          cost: pos.price - (pos.profit / pos.volume), // 估算成本价
          profit: pos.profit,
          profitPercent: (pos.profit / (pos.price * pos.volume)) * 100 // 估算盈亏比例
        }));

        setPositions(positionData);
        setError(prev => ({ ...prev, positions: null }));
        setLoading(prev => ({ ...prev, positions: false }));
        return;
      }

      // 如果没有持仓数据，尝试从API获取
      const token = getToken();

      if (!token) {
        throw new Error('未找到认证Token');
      }

      const response = await axios.get(`/api/strategy/live/${record.id}/positions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success) {
        setPositions(response.data.data || []);
        setError(prev => ({ ...prev, positions: null }));
      } else {
        setError(prev => ({ ...prev, positions: response.data?.error || '获取持仓数据失败' }));
      }
    } catch (err) {
      console.error('获取持仓数据失败:', err);
      setError(prev => ({ ...prev, positions: '获取持仓数据失败，请检查网络连接' }));
    } finally {
      setLoading(prev => ({ ...prev, positions: false }));
    }
  };

  // 获取交易记录
  const fetchTrades = async () => {
    try {
      setLoading(prev => ({ ...prev, trades: true }));
      const token = getToken();

      if (!token) {
        throw new Error('未找到认证Token');
      }

      const response = await axios.get(`/api/strategy/live/${record.id}/trades`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success) {
        setTrades(response.data.data || []);
        setError(prev => ({ ...prev, trades: null }));
      } else {
        setError(prev => ({ ...prev, trades: response.data?.error || '获取交易记录失败' }));
      }
    } catch (err) {
      console.error('获取交易记录失败:', err);
      setError(prev => ({ ...prev, trades: '获取交易记录失败，请检查网络连接' }));
    } finally {
      setLoading(prev => ({ ...prev, trades: false }));
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchStrategyLogs();
    fetchPositions();
    fetchTrades();
  }, [record.id]);
  // 持仓表格列定义
  const positionColumns = [
    {
      title: '股票代码',
      dataIndex: 'symbol',
      key: 'symbol',
      width: '15%',
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
      width: '15%',
    },
    {
      title: '持仓数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: '10%',
    },
    {
      title: '当前价格',
      dataIndex: 'price',
      key: 'price',
      width: '12%',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '成本价格',
      dataIndex: 'cost',
      key: 'cost',
      width: '12%',
      render: (cost: number) => `¥${cost.toFixed(2)}`,
    },
    {
      title: '浮动盈亏',
      dataIndex: 'profit',
      key: 'profit',
      width: '15%',
      render: (profit: number) => (
        <span style={{ color: profit >= 0 ? '#52c41a' : '#f5222d' }}>
          {profit >= 0 ? '+' : ''}¥{profit.toFixed(2)}
        </span>
      ),
    },
    {
      title: '盈亏比例',
      dataIndex: 'profitPercent',
      key: 'profitPercent',
      width: '15%',
      render: (percent: number) => (
        <span style={{ color: percent >= 0 ? '#52c41a' : '#f5222d' }}>
          {percent >= 0 ? '+' : ''}{percent.toFixed(2)}%
        </span>
      ),
    },
  ];

  // 交易记录表格列定义
  const tradeColumns = [
    {
      title: '交易时间',
      dataIndex: 'time',
      key: 'time',
      width: '18%',
    },
    {
      title: '股票代码',
      dataIndex: 'symbol',
      key: 'symbol',
      width: '12%',
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
      width: '12%',
    },
    {
      title: '方向',
      dataIndex: 'direction',
      key: 'direction',
      width: '10%',
      render: (direction: string) => (
        <Tag color={direction === 'buy' ? '#108ee9' : '#f50'}>
          {direction === 'buy' ? '买入' : '卖出'}
        </Tag>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: '10%',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: '12%',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: '15%',
      render: (amount: number) => `¥${amount.toFixed(2)}`,
    },
  ];

  return (
    <div className="expanded-row-container">
      <Tabs
        defaultActiveKey="logs"
        size="small"
        tabBarStyle={{ marginBottom: 16 }}
        items={[
          {
            key: 'logs',
            label: (
              <span>
                <FileTextOutlined />
                运行日志
              </span>
            ),
            children: (
              <Card className="expanded-card" size="small" bodyStyle={{ padding: '12px', maxHeight: '300px', overflow: 'auto' }}>
                {loading.logs ? (
                  <div style={{ textAlign: 'center', padding: '30px' }}>
                    <Spin size="small" tip="加载日志..." />
                  </div>
                ) : error.logs ? (
                  <Empty description={error.logs} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                ) : logs.length === 0 ? (
                  <Empty description="暂无日志记录" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                ) : (
                  <Timeline className="strategy-log-timeline">
                    {logs.map((log, index) => (
                      <Timeline.Item
                        key={index}
                        dot={logIconMap[log.level as keyof typeof logIconMap]}
                        color={
                          log.level === 'info'
                            ? 'blue'
                            : log.level === 'success'
                            ? 'green'
                            : log.level === 'warning'
                            ? 'orange'
                            : 'red'
                        }
                      >
                        <div className="log-item">
                          <span className="log-time">{log.time}</span>
                          <span className="log-message">{log.message}</span>
                        </div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                )}
              </Card>
            ),
          },
          {
            key: 'positions',
            label: (
              <span>
                <StockOutlined />
                持仓明细
              </span>
            ),
            children: (
              <Card className="expanded-card" size="small" bodyStyle={{ padding: '12px' }}>
                {loading.positions ? (
                  <div style={{ textAlign: 'center', padding: '30px' }}>
                    <Spin size="small" tip="加载持仓数据..." />
                  </div>
                ) : error.positions ? (
                  <Empty description={error.positions} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                ) : positions.length === 0 ? (
                  <Empty description="暂无持仓" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                ) : (
                  <Table
                    columns={positionColumns}
                    dataSource={positions}
                    pagination={false}
                    size="small"
                    className="detail-table"
                    rowKey="symbol"
                  />
                )}
              </Card>
            ),
          },
          {
            key: 'trades',
            label: (
              <span>
                <HistoryOutlined />
                历史成交
              </span>
            ),
            children: (
              <Card className="expanded-card" size="small" bodyStyle={{ padding: '12px' }}>
                {loading.trades ? (
                  <div style={{ textAlign: 'center', padding: '30px' }}>
                    <Spin size="small" tip="加载交易记录..." />
                  </div>
                ) : error.trades ? (
                  <Empty description={error.trades} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                ) : trades.length === 0 ? (
                  <Empty description="暂无交易记录" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                ) : (
                  <Table
                    columns={tradeColumns}
                    dataSource={trades}
                    pagination={{ pageSize: 5, size: 'small' }}
                    size="small"
                    className="detail-table"
                    rowKey={(record, index) => `${record.time}-${index}`}
                  />
                )}
              </Card>
            ),
          },
          {
            key: 'performance',
            label: (
              <span>
                <LineChartOutlined />
                绩效分析
              </span>
            ),
            children: (
              <Card className="expanded-card" size="small" bodyStyle={{ padding: '12px' }}>
                {loading.performance ? (
                  <div style={{ textAlign: 'center', padding: '30px' }}>
                    <Spin size="small" tip="加载绩效数据..." />
                  </div>
                ) : error.performance ? (
                  <Empty description={error.performance} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                ) : (
                  <>
                    <Row gutter={16}>
                      <Col span={6}>
                        <Card size="small">
                          <Statistic
                            title="累计收益"
                            value={record.performance?.totalReturn || 0}
                            precision={2}
                            valueStyle={{ color: (record.performance?.totalReturn || 0) >= 0 ? '#3f8600' : '#cf1322' }}
                            prefix={(record.performance?.totalReturn || 0) >= 0 ? '+' : ''}
                            suffix="%"
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card size="small">
                          <Statistic
                            title="日收益"
                            value={record.performance?.dailyReturn || 0}
                            precision={2}
                            valueStyle={{ color: (record.performance?.dailyReturn || 0) >= 0 ? '#3f8600' : '#cf1322' }}
                            prefix={(record.performance?.dailyReturn || 0) >= 0 ? '+' : ''}
                            suffix="%"
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card size="small">
                          <Statistic
                            title="逻辑资本"
                            value={record.performance?.logicalCapital || 0}
                            precision={2}
                            valueStyle={{ color: '#1890ff' }}
                            prefix="¥"
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card size="small">
                          <Statistic
                            title="持仓数量"
                            value={record.performance?.positions?.length || 0}
                            valueStyle={{ color: '#1890ff' }}
                          />
                        </Card>
                      </Col>
                    </Row>
                    <div style={{ marginTop: 16, height: 150, display: 'flex', justifyContent: 'center', alignItems: 'center', background: '#f5f5f5' }}>
                      <span style={{ color: '#999' }}>收益曲线图表区域</span>
                    </div>
                  </>
                )}
              </Card>
            ),
          },
        ]}
      />
    </div>
  );
};

export default ExpandedRow;
