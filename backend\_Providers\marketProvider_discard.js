const { MarketType, ExchangeType, KLineInterval } = require('../shared_types/market')
const axios = require('axios');
const { readFileSync } = require('fs');

const config = require('../config.json');

// 读取配置文件

const PYTHON_SERVICE_URL = `http://127.0.0.1:${config.python_service.port}`;

// 新浪期货API基础URL
const SINA_FUTURES_BASE_URL = `${config.sina_futures.url}`;

// 外部行情API基础URL
const EXTERNAL_MARKET_BASE_URL = `${config.external_service.url}`;
const EXTERNAL_MARKET_INSTRUMENTS_URL = `${EXTERNAL_MARKET_BASE_URL}/instruments`;
const EXTERNAL_MARKET_KLINE_URL = `${EXTERNAL_MARKET_BASE_URL}/kline`;

// 加密货币中文名称映射
const CRYPTO_NAME_MAP = {
  'BTC-USDT': '比特币',
  'ETH-USDT': '以太坊',
  'BNB-USDT': '币安币',
  'XRP-USDT': '瑞波币',
  'ADA-USDT': '艾达币',
  'DOGE-USDT': '狗狗币',
  'DOT-USDT': '波卡币',
  'UNI-USDT': '优尼币',
  'LTC-USDT': '莱特币',
  'LINK-USDT': '链链币'
};

class MarketProvider {
  constructor() {
    this.klineSubscriptions = new Map()
    this.klineIntervals = new Map()
  }

  /**
   * 获取期货K线数据的URL
   * @param {string} symbol - 期货代码
   * @param {string} interval - K线周期
   * @returns {string} API URL
   */
  getFuturesKLineUrl(symbol, interval) {
    // 根据不同的时间间隔返回对应的URL
    switch (interval) {
      case KLineInterval.MIN5:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesMiniKLine5m?symbol=${symbol}`;
      case KLineInterval.MIN15:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesMiniKLine15m?symbol=${symbol}`;
      case KLineInterval.MIN30:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesMiniKLine30m?symbol=${symbol}`;
      case KLineInterval.HOUR1:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesMiniKLine60m?symbol=${symbol}`;
      case KLineInterval.DAY1:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesDailyKLine?symbol=${symbol}`;
      default:
        throw new Error(`Unsupported interval for futures: ${interval}`);
    }
  }

  /**
   * 转换新浪期货K线数据格式
   * @param {Array} data - 新浪返回的K线数据
   * @returns {Array} 标准化的K线数据
   */
  transformFuturesKLineData(data) {
    if (!Array.isArray(data)) {
      throw new Error('Invalid futures kline data format');
    }

    return data.map(item => {
      // 新浪期货K线数据格式：
      // [0]日期, [1]开盘价, [2]最高价, [3]最低价, [4]收盘价, [5]成交量
      return {
        time: new Date(item[0]).getTime() / 1000, // 转换为时间戳
        open: parseFloat(item[1]),
        high: parseFloat(item[2]),
        low: parseFloat(item[3]),
        close: parseFloat(item[4]),
        volume: parseFloat(item[5])
      };
    });
  }

  /**
   * 获取交易品种列表
   */
  async getSymbols(market) {
    try {
      switch (market) {
        case MarketType.STOCK:
          // 调用 Python 服务获取股票列表
          const response = await axios.get(`${PYTHON_SERVICE_URL}/stock/list`);
          if (response.data.success) {
            return response.data.data;
          }
          throw new Error(response.data.error || 'Failed to fetch stock list');

        case MarketType.INDEX:
          // 返回指数列表（暂时使用静态数据）
          return [
            { code: '000001.SH', name: '上证指数', market: MarketType.INDEX, exchange: ExchangeType.SSE },
            { code: '399001.SZ', name: '深证成指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
            { code: '399006.SZ', name: '创业板指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
            { code: '000016.SH', name: '上证50', market: MarketType.INDEX, exchange: ExchangeType.SSE },
            { code: '000300.SH', name: '沪深300', market: MarketType.INDEX, exchange: ExchangeType.SSE },
            { code: '000905.SH', name: '中证500', market: MarketType.INDEX, exchange: ExchangeType.SSE },
            { code: '399005.SZ', name: '中小100', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
            { code: '399673.SZ', name: '创业板50', market: MarketType.INDEX, exchange: ExchangeType.SZSE }
          ];

        case MarketType.FUTURE:
          // 返回期货列表（暂时使用静态数据）
          return [
            { code: 'RB0', name: '螺纹钢', market: MarketType.FUTURE, exchange: ExchangeType.SHFE },
            // ... 其他期货品种 ...
          ];

        case MarketType.CRYPTO:
          try {
            // 调用加密货币API获取品种列表
            const response = await axios.get(EXTERNAL_MARKET_INSTRUMENTS_URL);
            if (response.data && Array.isArray(response.data.data)) {
              return response.data.data.map(symbol => ({
                code: symbol,
                name: CRYPTO_NAME_MAP[symbol] || `(${symbol})`, // 有中文名用中文名,没有的用括号包围代码
                market: MarketType.CRYPTO,
                exchange: ExchangeType.CRYPTOCURRENCY
              }));
            }
            throw new Error('Invalid crypto symbols data format');
          } catch (error) {
            console.error('Error fetching crypto symbols:', error);
            return [];
          }

        default:
          return [];
      }
    } catch (error) {
      console.error('Error in getSymbols:', error);
      return [];
    }
  }

  /**
   * 获取K线历史数据
   */
  async getKLineHistory(symbol, market, interval, options = {}) {
    try {
      switch (market) {
        case MarketType.STOCK:
        case MarketType.INDEX:
          // 将K线周期转换为标准格式
          let period;
          switch (interval) {
            case KLineInterval.MIN1:
              period = '1m';
              break;
            case KLineInterval.MIN5:
              period = '5m';
              break;
            case KLineInterval.MIN15:
              period = '15m';
              break;
            case KLineInterval.MIN30:
              period = '30m';
              break;
            case KLineInterval.HOUR1:
              period = '60m';
              break;
            case KLineInterval.HOUR4:
              period = '240m';
              break;
            case KLineInterval.DAY1:
              period = 'day';
              break;
            case KLineInterval.WEEK1:
              period = 'week';
              break;
            case KLineInterval.MONTH1:
              period = 'month';
              break;
            default:
              period = 'day'; // 默认使用日线
          }

          // 调用 Python 服务获取K线数据
          console.log(`Fetching kline data: symbol=${symbol}, period=${period}, market=${market}`);
          const endpoint = market === MarketType.INDEX ? '/index/kline' : '/stock/kline';
          const response = await axios.get(`${PYTHON_SERVICE_URL}${endpoint}`, {
            params: {
              symbol,
              period,
              adjust: market === MarketType.STOCK ? 'qfq' : undefined  // 指数数据不需要复权
            }
          });
          
          if (response.data.success) {
            return response.data.data;
          }
          throw new Error(response.data.error || 'Failed to fetch stock kline data');

        case MarketType.FUTURE:
          try {
            // 获取期货K线数据
            const url = this.getFuturesKLineUrl(symbol, interval);
            console.log(`Fetching futures kline data: ${url}`);
            
            const response = await axios.get(url);
            if (!response.data) {
              throw new Error('Empty response from Sina futures API');
            }

            // 转换数据格式
            return this.transformFuturesKLineData(response.data);
          } catch (error) {
            console.error('Error fetching futures data:', error);
            throw new Error(`Failed to fetch futures data: ${error.message}`);
          }

        case MarketType.CRYPTO:
          try {
            // 构建K线请求参数
            const params = {
              instrument: symbol,
              interval: interval
            };

            // 调用外部行情API获取K线数据
            const response = await axios.get(EXTERNAL_MARKET_KLINE_URL, { params });
            
            if (response.data && Array.isArray(response.data.data)) {
              console.log('[数据提供者] 获取到K线数据：', response.data.data.length);
              return response.data.data.map(item => ({
                time: parseInt(item[0]) / 1000,  // 转换为秒级时间戳
                open: parseFloat(item[1]),
                high: parseFloat(item[2]),
                low: parseFloat(item[3]),
                close: parseFloat(item[4]),
                volume: parseFloat(item[5]),
                volCcy: parseFloat(item[6]),     // 成交额
                volCcyQuote: parseFloat(item[7]) // 报价币种成交额
              }));
            }
            throw new Error('Invalid crypto kline data format');
          } catch (error) {
            console.error('Error fetching crypto kline data:', error);
            throw error;  // 向上传递错误
          }

        default:
          return [];
      }
    } catch (error) {
      console.error('Error in getKLineHistory:', error, {
        symbol,
        market,
        interval,
        options
      });
      throw error;  // 向上传递错误，而不是返回空数组
    }
  }

  /**
   * 订阅K线数据更新
   */
  subscribeKLine(symbol, interval, callback) {
    const key = `${symbol}_${interval}`
    
    // 保存订阅回调
    if (!this.klineSubscriptions.has(key)) {
      this.klineSubscriptions.set(key, new Set())
    }
    this.klineSubscriptions.get(key).add(callback)
    
    // 如果这个周期还没有在轮询，开始轮询
    if (!this.klineIntervals.has(key)) {
      const intervalMs = this.getIntervalMilliseconds(interval)
      const timerId = setInterval(async () => {
        try {
          const kline = await this.getLatestKLine(symbol, interval)
          const callbacks = this.klineSubscriptions.get(key)
          if (callbacks) {
            callbacks.forEach(cb => cb(kline))
          }
        } catch (error) {
          console.error(`Error fetching kline for ${symbol}:`, error)
        }
      }, intervalMs)
      
      this.klineIntervals.set(key, timerId)
    }
    
    // 返回取消订阅的函数
    return () => {
      const callbacks = this.klineSubscriptions.get(key)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          // 如果没有订阅者了，停止轮询
          const timerId = this.klineIntervals.get(key)
          if (timerId) {
            clearInterval(timerId)
            this.klineIntervals.delete(key)
          }
          this.klineSubscriptions.delete(key)
        }
      }
    }
  }

  /**
   * 获取最新K线数据
   */
  async getLatestKLine(symbol, interval) {
    // TODO: 实现实际的数据获取逻辑
    return {
      time: Date.now() / 1000,
      open: 0,
      high: 0,
      low: 0,
      close: 0,
      volume: 0
    }
  }

  /**
   * 将K线周期转换为毫秒数
   */
  getIntervalMilliseconds(interval) {
    const unit = interval.slice(-1)
    const value = parseInt(interval.slice(0, -1))
    
    switch (unit) {
      case 'm':
        return value * 60 * 1000
      case 'h':
        return value * 60 * 60 * 1000
      case 'd':
        return value * 24 * 60 * 60 * 1000
      case 'w':
        return value * 7 * 24 * 60 * 60 * 1000
      case 'M':
        return value * 30 * 24 * 60 * 60 * 1000
      default:
        throw new Error(`Unknown interval unit: ${unit}`)
    }
  }
}

// 创建单例实例
const marketProvider = new MarketProvider()

module.exports = {
  marketProvider
} 