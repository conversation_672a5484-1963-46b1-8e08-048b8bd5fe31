# 行情解析器配置文件
# 支持多种数据源的行情解析器配置

parsers:
# 通达信数据源解析器
-   active: true
    id: tdx_parser
    module: ParserTDX               # 通达信解析器模块
    host: **************            # 通达信服务器地址
    port: 7709                      # 通达信服务器端口
    protocol: 1                     # TCP协议
    buffsize: 128                   # 缓存大小(MB)
    clientid: 1                     # 客户端ID
    hbinterval: 15                  # 心跳间隔(秒)
    local_ip: 0.0.0.0              # 本地IP
    # 订阅的股票代码列表
    code: SSE.000001,SSE.600036,SSE.600519,SSE.600276,SZSE.000001,SZSE.000002,SZSE.300059

# XTP数据源解析器（备用）
-   active: false
    id: xtp_parser
    module: ParserXTP               # XTP解析器模块
    host: ************              # XTP服务器地址
    port: 6002                      # XTP服务器端口
    protocol: 1                     # TCP协议
    buffsize: 128                   # 缓存大小(MB)
    clientid: 2                     # 客户端ID
    hbinterval: 15                  # 心跳间隔(秒)
    local_ip: 0.0.0.0              # 本地IP
    user: ""                        # XTP用户名（需要配置）
    pass: ""                        # XTP密码（需要配置）
    code: SSE.000001,SSE.600009,SSE.600036,SSE.600276,SZSE.000001

# 自定义扩展解析器
-   active: false
    id: ext_parser
    module: ExtParser               # 扩展解析器模块
    host: localhost                 # 本地服务器
    port: 3005                      # Socket.io端口
    protocol: 1                     # TCP协议
    buffsize: 64                    # 缓存大小(MB)
    clientid: 3                     # 客户端ID
    hbinterval: 30                  # 心跳间隔(秒)
    local_ip: 0.0.0.0              # 本地IP
