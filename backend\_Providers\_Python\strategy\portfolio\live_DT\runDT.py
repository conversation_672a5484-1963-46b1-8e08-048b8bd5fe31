#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
中心数据引擎启动脚本
基于Wonder Trader数据引擎，为实盘交易提供统一的行情数据服务
集成自定义ExtParser，接收tdxserver的Socket数据推送
"""

import time
import os
import sys
import math
import json
import threading
import datetime
import socketio
from wtpy import WtDtEngine, BaseExtParser, WTSTickStruct
from ctypes import byref

class TdxSocketParser(BaseExtParser):
    """
    TDX Socket数据解析器
    接收来自tdxserver的Socket.IO推送数据，转换为WTSTickStruct格式
    """

    def __init__(self, id: str, socket_url: str = "http://127.0.0.1:5004"):
        super().__init__(id)
        self.socket_url = socket_url
        self.sio = None
        self.connected = False
        self.subscriptions = set()  # 已订阅的品种
        self.__worker__ = None
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 5.0  # 重连延迟（秒）

        print(f"[TdxSocketParser] 初始化解析器: {id}, Socket地址: {socket_url}")

    def init(self, engine: WtDtEngine):
        """初始化"""
        super().init(engine)
        print(f"[TdxSocketParser] 解析器已绑定到引擎")

    def connect(self):
        """开始连接"""
        print(f"[TdxSocketParser] 开始连接到 {self.socket_url}")

        try:
            # 创建基础客户端
            self.sio = socketio.Client(
                reconnection=True,
                reconnection_attempts=self.max_reconnect_attempts,
                reconnection_delay=int(self.reconnect_delay),
                reconnection_delay_max=30
            )
            
            # 兼容旧版本：通过 engineio 设置心跳
            if hasattr(self.sio, 'eio'):
                self.sio.eio.ping_interval = 25
                self.sio.eio.ping_timeout = 60

            # 注册事件处理器
            self.sio.on('connect', self.on_connect)
            self.sio.on('disconnect', self.on_disconnect)
            self.sio.on('message', self.on_message)
            self.sio.on('tick_data', self.on_tick_data)
            self.sio.on('error', self.on_error)
            self.sio.on('connected', self.on_connected)
            self.sio.on('subscribed', self.on_subscribed)

            # 连接到服务器
            self.sio.connect(self.socket_url, wait_timeout=10)

            print(f"[TdxSocketParser] Socket连接成功")
            return True

        except Exception as e:
            print(f"[TdxSocketParser] Socket连接失败: {e}")
            self._schedule_reconnect()
            return False

    def disconnect(self):
        """断开连接"""
        print(f"[TdxSocketParser] 断开连接")
        if self.sio and self.connected:
            self.sio.disconnect()
        self.connected = False

    def release(self):
        """释放资源"""
        print(f"[TdxSocketParser] 释放资源")
        self.disconnect()

    def subscribe(self, fullCode: str):
        """
        订阅实时行情
        @fullCode   合约代码，格式如CFFEX.IF2106 或 SZ.000001
        """
        if fullCode in self.subscriptions:
            print(f"[TdxSocketParser] 已订阅 {fullCode}")
            return

        try:
            # 解析品种代码
            parts = fullCode.split('.')
            if len(parts) != 2:
                print(f"[TdxSocketParser] 无效的品种代码格式: {fullCode}")
                return

            market = parts[0].lower()  # 交易所代码转小写
            symbol = parts[1]          # 品种代码

            print(f"[TdxSocketParser] 订阅品种: {fullCode} (market={market}, symbol={symbol})")

            if self.connected and self.sio:
                # 发送订阅请求到tdxserver
                self.sio.emit('subscribe_tick', {
                    'market': market,
                    'symbol': symbol
                })

                self.subscriptions.add(fullCode)
                print(f"[TdxSocketParser] 订阅请求已发送: {fullCode}")
            else:
                print(f"[TdxSocketParser] Socket未连接，暂存订阅请求: {fullCode}")
                self.subscriptions.add(fullCode)

        except Exception as e:
            print(f"[TdxSocketParser] 订阅失败 {fullCode}: {e}")

    def unsubscribe(self, fullCode: str):
        """
        退订实时行情
        @fullCode   合约代码，格式如CFFEX.IF2106 或 SZ.000001
        """
        if fullCode not in self.subscriptions:
            print(f"[TdxSocketParser] 未订阅 {fullCode}")
            return

        try:
            # 解析品种代码
            parts = fullCode.split('.')
            if len(parts) != 2:
                print(f"[TdxSocketParser] 无效的品种代码格式: {fullCode}")
                return

            market = parts[0].lower()
            symbol = parts[1]

            print(f"[TdxSocketParser] 退订品种: {fullCode}")

            if self.connected and self.sio:
                # 发送退订请求到tdxserver
                self.sio.emit('unsubscribe_tick', {
                    'market': market,
                    'symbol': symbol
                })

            self.subscriptions.discard(fullCode)
            print(f"[TdxSocketParser] 退订完成: {fullCode}")

        except Exception as e:
            print(f"[TdxSocketParser] 退订失败 {fullCode}: {e}")

    def on_connect(self):
        """Socket连接成功事件"""
        print(f"[TdxSocketParser] Socket连接成功")
        self.connected = True
        self.reconnect_attempts = 0

    def on_disconnect(self):
        """Socket断开连接事件"""
        print(f"[TdxSocketParser] Socket连接断开")
        self.connected = False

    def on_error(self, data):
        """Socket错误事件"""
        print(f"[TdxSocketParser] Socket错误: {data}")

    def on_connected(self, data):
        """收到服务器连接确认"""
        print(f"[TdxSocketParser] 收到服务器连接确认: {data}")

    def on_subscribed(self, data):
        """订阅成功确认"""
        print(f"[TdxSocketParser] 订阅成功: {data}")

    def on_message(self, data):
        """接收Socket.IO消息并调用on_tick_data"""
        self.on_tick_data(data)

    def on_tick_data(self, data):
        """
        接收tick数据并转换为WTSTickStruct
        """
        try:
            # 解析接收到的数据
            symbol = data.get('symbol', '')
            tick_data = data.get('data', {})

            if not symbol or not tick_data:
                print(f"[TdxSocketParser] 无效的tick数据: {data}")
                return

            # 创建WTSTickStruct
            curTick = WTSTickStruct()

            # 设置基本信息
            market = tick_data.get('exchg', 'UNKNOWN').upper()
            code = tick_data.get('code', symbol)

            curTick.exchg = bytes(market, encoding="UTF8")
            curTick.code = bytes(code, encoding="UTF8")

            # 设置价格数据
            curTick.price = float(tick_data.get('price', 0))
            curTick.open = float(tick_data.get('open', 0))
            curTick.high = float(tick_data.get('high', 0))
            curTick.low = float(tick_data.get('low', 0))

            # 设置成交数据
            curTick.volume = float(tick_data.get('volume', 0))

            # 设置买卖盘数据（五档）
            curTick.ask_price_0 = float(tick_data.get('ask_price_0', 0))
            curTick.ask_qty_0 = float(tick_data.get('ask_qty_0', 0))
            curTick.bid_prices_0 = float(tick_data.get('bid_price_0', 0))
            curTick.bid_qty_0 = float(tick_data.get('bid_qty_0', 0))

            # 设置其他档位
            for i in range(1, 5):
                setattr(curTick, f"ask_price_{i}", float(tick_data.get(f'ask_price_{i}', 0)))
                setattr(curTick, f"ask_qty_{i}", float(tick_data.get(f'ask_qty_{i}', 0)))
                setattr(curTick, f"bid_price_{i}", float(tick_data.get(f'bid_price_{i}', 0)))
                setattr(curTick, f"bid_qty_{i}", float(tick_data.get(f'bid_qty_{i}', 0)))

            # 设置时间字段
            timestamp = tick_data.get('timestamp', 0)
            if timestamp > 0:
                # 从毫秒时间戳转换
                tm = datetime.datetime.fromtimestamp(timestamp / 1000)
                curTick.action_date = tm.year * 10000 + tm.month * 100 + tm.day
                curTick.action_time = tm.hour * 10000000 + tm.minute * 100000 + tm.second * 1000 + math.floor(tm.microsecond / 1000)
                curTick.trading_date = tm.year * 10000 + tm.month * 100 + tm.day
            else:
                # 使用当前时间
                tm = datetime.datetime.now()
                curTick.action_date = tm.year * 10000 + tm.month * 100 + tm.day
                curTick.action_time = tm.hour * 10000000 + tm.minute * 100000 + tm.second * 1000 + math.floor(tm.microsecond / 1000)
                curTick.trading_date = tm.year * 10000 + tm.month * 100 + tm.day

            # 设置期货特有字段（如果有）
            curTick.open_interest = float(tick_data.get('open_interest', 0))
            curTick.diff_interest = float(tick_data.get('diff_interest', 0))

            # 推送给Wonder Trader引擎
            self.__engine__.push_quote_from_extended_parser(self.id(), byref(curTick), False)

            print(f"[TdxSocketParser] 推送tick数据: {market}.{code} 价格={curTick.price}")

        except Exception as e:
            print(f"[TdxSocketParser] 处理tick数据错误: {e}")
            import traceback
            traceback.print_exc()

    def _schedule_reconnect(self):
        """安排重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            print(f"[TdxSocketParser] 已达到最大重连次数 ({self.max_reconnect_attempts})，停止重连")
            return

        self.reconnect_attempts += 1
        print(f"[TdxSocketParser] 安排重连 (第 {self.reconnect_attempts} 次)，{self.reconnect_delay} 秒后重连")

        # 在新线程中执行重连，避免阻塞
        reconnect_thread = threading.Thread(target=self._reconnect_worker, daemon=True)
        reconnect_thread.start()

    def _reconnect_worker(self):
        """重连工作线程"""
        import time
        time.sleep(self.reconnect_delay)

        if not self.connected:
            print(f"[TdxSocketParser] 开始第 {self.reconnect_attempts} 次重连...")
            if self.connect():
                print(f"[TdxSocketParser] 重连成功")
            else:
                print(f"[TdxSocketParser] 重连失败")
                # 递增延迟时间，但不超过30秒
                self.reconnect_delay = min(self.reconnect_delay * 1.5, 30)

def main():
    """启动中心数据引擎"""
    print("=" * 60)
    print("正在启动中心数据引擎...")
    print("=" * 60)

    # 创建数据引擎实例
    print("[DataEngine] 创建Wonder Trader数据引擎...")
    engine = WtDtEngine()

    # 初始化引擎
    try:
        print("[DataEngine] 初始化数据引擎...")
        engine.initialize()
        print("[DataEngine] 数据引擎初始化成功")
    except Exception as e:
        print(f"[DataEngine] 数据引擎初始化失败: {e}")
        return False

    # 创建并添加自定义ExtParser
    try:
        print("[ExtParser] 创建TDX Socket解析器...")
        tdx_parser = TdxSocketParser("tdx_socket_parser", "http://127.0.0.1:5004")

        print("[ExtParser] 初始化解析器...")
        tdx_parser.init(engine)

        print("[ExtParser] 添加解析器到引擎...")
        engine.add_exetended_parser(tdx_parser)

        print("[ExtParser] 连接到TDX服务器...")
        if tdx_parser.connect():
            print("[ExtParser] TDX Socket解析器连接成功")
        else:
            print("[ExtParser] TDX Socket解析器连接失败，但引擎将继续运行")

    except Exception as e:
        print(f"[ExtParser] 创建TDX解析器失败: {e}")
        print("[ExtParser] 将继续使用标准解析器运行")
        import traceback
        traceback.print_exc()

    # 启动引擎
    try:
        print("[DataEngine] 启动数据引擎...")
        engine.run(True)
        print("[DataEngine] 数据引擎启动成功")
        print("[DataEngine] 数据引擎正在运行，按 Ctrl+C 退出")
    except Exception as e:
        print(f"[DataEngine] 数据引擎启动失败: {e}")
        return False

    # 保持运行状态
    try:
        print("\n" + "=" * 60)
        print("数据引擎运行中...")
        print("- 标准解析器: 根据mdparsers.yaml配置运行")
        print("- TDX Socket解析器: 接收来自tdxserver的实时数据")
        print("- 按 Ctrl+C 退出")
        print("=" * 60)

        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n[DataEngine] 正在关闭数据引擎...")

        # 清理ExtParser
        try:
            if 'tdx_parser' in locals():
                print("[ExtParser] 释放TDX解析器资源...")
                tdx_parser.release()
        except Exception as e:
            print(f"[ExtParser] 清理解析器时出错: {e}")

        return True

if __name__ == "__main__":
    # 设置工作目录为脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 启动数据引擎
    success = main()
    
    if success:
        print("数据引擎已正常关闭")
        sys.exit(0)
    else:
        print("数据引擎启动失败")
        sys.exit(1)
