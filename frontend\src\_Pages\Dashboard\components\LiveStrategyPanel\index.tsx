import React, { useEffect, useState } from 'react';
import { Card, Table, Button, Tag, Space, Tooltip, Row, Col, Statistic, Empty, Spin, Divider, Badge, Tabs, List, Timeline, Modal, Typography } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  AreaChartOutlined,
  SettingOutlined,
  LineChartOutlined,
  DollarCircleOutlined,
  RocketOutlined,
  FieldTimeOutlined,
  FileTextOutlined,
  StockOutlined,
  HistoryOutlined,
  DownOutlined,
  RightOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  DeleteOutlined,
  BugOutlined
} from '@ant-design/icons';
import { EventBus } from '@/events/eventBus';
import { StrategyEvents } from '@/events/events';
import { LiveStrategyInfo } from '@/shared_types/trade';
import { message as antMessage } from 'antd';
import httpDispatcher from '@/_Dispatchers/HttpDispatcher';
import ExpandedRow from './ExpandedRow';
import './styles.css';
import ReactJson from 'react-json-view';

// 引入antd子组件
const { TabPane } = Tabs;
const { Text } = Typography;

const LiveStrategyPanel: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [strategies, setStrategies] = useState<LiveStrategyInfo[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [debugVisible, setDebugVisible] = useState(false); // 添加调试面板显示状态


  // 调试对话框状态
  const [debugModalVisible, setDebugModalVisible] = useState(false);
  const [debugData, setDebugData] = useState<{
    strategyYaml: string;
    configFiles: { [key: string]: string };
    loading: boolean;
  }>({
    strategyYaml: '',
    configFiles: {},
    loading: false
  });
  
  // 获取实盘策略列表
  const fetchLiveStrategies = () => {
    setLoading(true);
    setError(null);

    EventBus.emit(StrategyEvents.Types.GET_LIVE_STRATEGIES, {
      callback: (strategies: any[]) => {
        setLoading(false);

        if (strategies) {
          console.log('[LiveStrategyPanel] 收到策略数据:', strategies);
          setStrategies(strategies || []);
        } else {
          setError('获取实盘策略列表失败');
        }
      }
    });
  };

  // 组件挂载时获取策略列表
  useEffect(() => {
    fetchLiveStrategies();

    // 监听策略更新事件
    const subscription = EventBus.on(
      StrategyEvents.Types.LIVE_STRATEGY_UPDATED,
      () => {
        fetchLiveStrategies();
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // 处理启动策略
  const handleStartStrategy = (id: string) => {
    EventBus.emit(StrategyEvents.Types.START_LIVE_STRATEGY, {
      liveStrategyId: id,
      callback: (success: boolean, message?: string) => {
        if (success) {
          antMessage.success('策略已启动');
          fetchLiveStrategies(); // 刷新列表
        } else {
          antMessage.error(`启动失败: ${message || '未知错误'}`);
        }
      }
    });
  };

  // 处理停止策略
  const handleStopStrategy = (id: string) => {
    Modal.confirm({
      title: '确认停止策略?',
      icon: <WarningOutlined />,
      content: '停止策略将会清空当前所有持仓。确定要继续吗？',
      okText: '确认停止',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => { // 用户确认后执行
        console.log(`[实盘面板] 用户确认停止策略: ${id}`);
        EventBus.emit(StrategyEvents.Types.STOP_LIVE_STRATEGY, {
          liveStrategyId: id,
          callback: (success: boolean, message?: string) => {
            if (success) {
              antMessage.success('策略已停止');
              fetchLiveStrategies(); // 刷新列表
            } else {
              antMessage.error(`停止失败: ${message || '未知错误'}`);
            }
          }
        });
      },
      onCancel: () => {
        console.log('[实盘面板] 用户取消停止策略');
      },
    });
  };

  // 处理删除策略
  const handleDeleteStrategy = (id: string) => {
    Modal.confirm({
      title: '确认删除策略?',
      icon: <WarningOutlined />,
      content: '删除策略后将无法恢复。确定要继续吗？',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => { // 用户确认后执行
        console.log(`[实盘面板] 用户确认删除策略: ${id}`);
        EventBus.emit(StrategyEvents.Types.DELETE_LIVE_STRATEGY, {
          liveStrategyId: id,
          callback: (success: boolean, message?: string) => {
            if (success) {
              antMessage.success('策略已删除');
              fetchLiveStrategies(); // 刷新列表
            } else {
              antMessage.error(`删除失败: ${message || '未知错误'}`);
            }
          }
        });
      },
      onCancel: () => {
        console.log('[实盘面板] 用户取消删除策略');
      },
    });
  };

  // 处理查看绩效
  const handleViewPerformance = (id: string) => {
    console.log(`查看绩效: ${id}`);
    // 实现查看绩效的逻辑，例如跳转到绩效分析页面
    antMessage.info('正在开发绩效分析功能...');
  };

  // 处理策略设置 - 发射事件以打开配置对话框，传递当前配置信息
  const handleStrategySettings = (record: LiveStrategyInfo) => {
    console.log(`[实盘面板] 请求配置策略: ${record.id}, 原始策略ID: ${record.strategyId}`);

    // 添加详细日志
    console.log('[实盘面板] 策略详情:', {
      id: record.id,
      strategyId: record.strategyId,
      name: record.name,
      accountId: record.accountId,
      logicalCapital: record.performance?.logicalCapital,
      commissionRate: record.commissionRate,
      riskSettings: record.riskSettings
    });

    // 发射事件，传递策略ID、名称和当前配置信息
    EventBus.emit(StrategyEvents.Types.SHOW_LIVE_CONFIG, {
      strategyId: record.strategyId || record.id, // 优先使用原始策略ID，如果没有则使用实盘策略ID
      strategyName: record.name,
      liveStrategyId: record.id, // 添加实盘策略ID，表示这是编辑模式
      currentStrategyType: record.strategyType,
      currentAccountId: record.accountId,
      currentLogicalCapital: record.performance?.logicalCapital,
      currentCommissionRate: record.commissionRate,
      currentMaxOrderSize: record.riskSettings?.maxOrderSize,
      currentMaxDailyTrades: record.riskSettings?.maxDailyTrades,
      currentStopLossPercent: record.riskSettings?.stopLossPercent,
    });
  };

  // 处理调试功能
  const handleDebugStrategy = async (record: LiveStrategyInfo) => {
    console.log(`[实盘面板] 请求调试策略: ${record.id}`);

    setDebugModalVisible(true);
    setDebugData({
      strategyYaml: '',
      configFiles: {},
      loading: true
    });

    try {
      // 使用 httpDispatcher 获取策略YAML和配置文件（自动处理token）
      const response = await httpDispatcher.get(`/strategy/live/${record.id}/debug`);

      if (response.data && response.data.success) {
        setDebugData({
          strategyYaml: response.data.data.strategyYaml || '# 策略YAML未找到',
          configFiles: response.data.data.configFiles || {},
          loading: false
        });
      } else {
        throw new Error(response.data?.error || '获取调试信息失败');
      }
    } catch (error: any) {
      console.error('[实盘面板] 获取调试信息失败:', error);
      setDebugData({
        strategyYaml: `# 获取策略YAML失败\n# 错误: ${error.message}`,
        configFiles: {
          'error.txt': `获取配置文件失败: ${error.message}`
        },
        loading: false
      });
      antMessage.error(`获取调试信息失败: ${error.message}`);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '策略名称',
      dataIndex: 'name',
      key: 'name',
      width: '18%',
      render: (text: string, record: LiveStrategyInfo) => (
        <Space>
          <RocketOutlined style={{ color: '#1890ff', marginRight: 8 }} />
          <span style={{ fontWeight: 500 }}>{text}</span>
          <Badge
            status={record.status === 'running' ? 'success' : record.status === 'stopped' ? 'default' : 'error'}
            text={
              <Tag
                className={`status-tag-${record.status}`}
              >
                {record.status === 'running' ? '运行中' : record.status === 'stopped' ? '已停止' : '错误'}
              </Tag>
            }
          />
        </Space>
      ),
    },
    {
      title: '账户',
      dataIndex: 'accountId',
      key: 'accountId',
      width: '10%',
      render: (text: string) => (
        <Space>
          <DollarCircleOutlined style={{ color: '#722ed1' }} />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '初始资金',
      dataIndex: 'initialCapital',
      key: 'initialCapital',
      width: '12%',
      render: (value: number) => (
        value !== undefined ? (
          <Space>
            <DollarCircleOutlined style={{ color: '#52c41a' }} />
            <span style={{ fontWeight: 500 }}>
              ¥{value.toLocaleString()}
            </span>
          </Space>
        ) : '-'
      ),
    },
    {
      title: '收益率',
      dataIndex: ['performance', 'totalReturn'],
      key: 'totalReturn',
      width: '10%',
      render: (value: number) => (
        value !== undefined ? (
          <div className={value >= 0 ? 'return-value-positive' : 'return-value-negative'}>
            <LineChartOutlined style={{ marginRight: 8 }} />
            {value >= 0 ? '+' : ''}{value.toFixed(2)}%
          </div>
        ) : '-'
      ),
    },
    {
      title: '今日收益',
      dataIndex: ['performance', 'dailyReturn'],
      key: 'dailyReturn',
      width: '10%',
      render: (value: number) => (
        value !== undefined ? (
          <div className={value >= 0 ? 'return-value-positive' : 'return-value-negative'}>
            {value >= 0 ? '+' : ''}{value.toFixed(2)}%
          </div>
        ) : '-'
      ),
    },
    {
      title: '持仓数量',
      dataIndex: ['performance', 'positions'],
      key: 'positions',
      width: '8%',
      render: (positions: any[]) => (
        <Badge count={positions?.length || 0} showZero style={{ backgroundColor: positions?.length ? '#1890ff' : '#d9d9d9' }} />
      ),
    },
    {
      title: '启动时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: '14%',
      render: (text: string) => (
        <Space>
          <FieldTimeOutlined style={{ color: '#faad14' }} />
          <span>{text ? new Date(text).toLocaleString() : '-'}</span>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '18%',
      render: (_: any, record: LiveStrategyInfo) => (
        <Space size="small">
          {record.status === 'running' ? (
            <Tooltip title="停止策略">
              <Button
                type="primary"
                danger
                icon={<PauseCircleOutlined />}
                size="small"
                className="action-button"
                onClick={() => handleStopStrategy(record.id)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="启动策略">
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                size="small"
                className="action-button"
                onClick={() => handleStartStrategy(record.id)}
              />
            </Tooltip>
          )}

          <Tooltip title="查看绩效">
            <Button
              icon={<AreaChartOutlined />}
              size="small"
              className="action-button"
              onClick={() => handleViewPerformance(record.id)}
            />
          </Tooltip>

          <Tooltip title="策略设置">
            <Button
              icon={<SettingOutlined />}
              size="small"
              className="action-button"
              onClick={() => handleStrategySettings(record)}
            />
          </Tooltip>

          <Tooltip title="查看详情">
            <Button
              icon={<FileTextOutlined />}
              size="small"
              className="action-button"
              onClick={() => {
                // 直接通过状态控制展开/收起
                const isCurrentRowExpanded = expandedRowKeys.includes(record.id);
                if (isCurrentRowExpanded) {
                  // 如果当前行已展开，则收起
                  setExpandedRowKeys([]);
                } else {
                  // 如果当前行未展开，则展开当前行，收起其他行
                  setExpandedRowKeys([record.id]);
                }
              }}
            />
          </Tooltip>

          <Tooltip title="调试信息">
            <Button
              icon={<BugOutlined />}
              size="small"
              className="action-button"
              onClick={() => handleDebugStrategy(record)}
            />
          </Tooltip>

          {/* 只有当策略已停止时才显示删除按钮 */}
          {record.status !== 'running' && (
            <Tooltip title="删除策略">
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                size="small"
                className="action-button"
                onClick={() => handleDeleteStrategy(record.id)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  // 计算统计数据
  const runningCount = strategies.filter(s => s.status === 'running').length;
  const totalReturn = strategies.reduce((sum, s) => sum + (s.performance?.totalReturn || 0), 0) / (strategies.length || 1);
  const dailyReturn = strategies.reduce((sum, s) => sum + (s.performance?.dailyReturn || 0), 0) / (strategies.length || 1);

  // 渲染内容
  return (
    <div style={{ padding: '16px', backgroundColor: '#f5f7fa' }}>
      <Row gutter={12} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Card className="live-strategy-stat-card" size="small" bodyStyle={{ padding: '12px' }}>
            <div className="stat-card-content">
              <div className="stat-card-header">
                <RocketOutlined className="stat-card-icon" style={{ color: '#1890ff' }} />
                <span className="stat-card-title">运行中的策略</span>
              </div>
              <div className="stat-card-value" style={{ color: '#1890ff' }}>
                {runningCount}<span className="stat-card-suffix">/ {strategies.length}</span>
              </div>
              <div className="stat-card-footer">
                {runningCount > 0 ? '策略正常运行中' : '暂无运行中的策略'}
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className="live-strategy-stat-card" size="small" bodyStyle={{ padding: '12px' }}>
            <div className="stat-card-content">
              <div className="stat-card-header">
                <LineChartOutlined className="stat-card-icon" style={{ color: totalReturn >= 0 ? '#3f8600' : '#cf1322' }} />
                <span className="stat-card-title">总收益率</span>
              </div>
              <div className="stat-card-value" style={{ color: totalReturn >= 0 ? '#3f8600' : '#cf1322' }}>
                {totalReturn >= 0 ? '+' : ''}{totalReturn.toFixed(2)}<span className="stat-card-suffix">%</span>
              </div>
              <div className="stat-card-footer">
                {totalReturn >= 0 ? '整体表现良好' : '需要关注策略表现'}
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className="live-strategy-stat-card" size="small" bodyStyle={{ padding: '12px' }}>
            <div className="stat-card-content">
              <div className="stat-card-header">
                <DollarCircleOutlined className="stat-card-icon" style={{ color: dailyReturn >= 0 ? '#3f8600' : '#cf1322' }} />
                <span className="stat-card-title">今日收益</span>
              </div>
              <div className="stat-card-value" style={{ color: dailyReturn >= 0 ? '#3f8600' : '#cf1322' }}>
                {dailyReturn >= 0 ? '+' : ''}{dailyReturn.toFixed(2)}<span className="stat-card-suffix">%</span>
              </div>
              <div className="stat-card-footer">
                {dailyReturn >= 0 ? '今日表现良好' : '今日表现不佳'}
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className="live-strategy-stat-card" size="small" bodyStyle={{ padding: '12px' }}>
            <div className="stat-card-content">
              <div className="stat-card-header">
                <FieldTimeOutlined className="stat-card-icon" style={{ color: '#faad14' }} />
                <span className="stat-card-title">最后更新时间</span>
              </div>
              <div className="stat-card-value time-value">
                {new Date().toLocaleString()}
              </div>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={fetchLiveStrategies}
                size="small"
                className="refresh-button"
              >
                刷新
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <RocketOutlined style={{ marginRight: 6, fontSize: '14px', color: '#1890ff' }} />
            <span style={{ fontSize: '14px', fontWeight: 600 }}>实盘策略管理</span>
          </div>
        }
        extra={
          <Button
            type="primary"
            icon={<RocketOutlined />}
            className="add-strategy-button"
            size="small"
          >
            添加实盘策略
          </Button>
        }
        className="live-strategy-main-card"
        bordered={true}
      >

        {loading ? (
          <div style={{ textAlign: 'center', padding: '30px' }}>
            <Spin size="default" tip="加载策略数据..." />
          </div>
        ) : error ? (
          <Empty description={error} style={{ margin: '20px 0' }} />
        ) : strategies.length === 0 ? (
          <Empty description="暂无实盘策略" style={{ margin: '20px 0' }} />
        ) : (
          <Table
            columns={columns}
            dataSource={strategies}
            rowKey="id"
            pagination={false}
            className="live-strategy-table"
            bordered
            size="middle"
            rowClassName={(record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark'}
            scroll={{ x: 'max-content' }}
            expandable={{
              expandedRowRender: (record) => <ExpandedRow record={record} />,
              expandRowByClick: false,
              expandedRowKeys: expandedRowKeys,
              onExpand: (expanded, record) => {
                if (expanded) {
                  // 只保留当前展开的行，关闭其他行
                  setExpandedRowKeys([record.id]);
                } else {
                  // 关闭当前行
                  setExpandedRowKeys([]);
                }
              },
              expandIcon: ({ expanded, onExpand, record }) => (
                <button
                  onClick={e => {
                    e.stopPropagation();
                    onExpand(record, e);
                  }}
                  className="expand-button"
                >
                  {expanded ? <DownOutlined /> : <RightOutlined />}
                </button>
              ),
            }}
          />
        )}
      </Card>

      {/* 添加调试面板，显示收到的策略数据 */}
      <Button 
        type="dashed" 
        onClick={() => setDebugVisible(!debugVisible)}
        style={{ marginTop: '16px' }}
      >
        {debugVisible ? '隐藏' : '显示'}调试信息
      </Button>
      
      {debugVisible && (
        <Card style={{ marginTop: '16px' }}>
          <Tabs defaultActiveKey="1">
            <TabPane tab="策略数据" key="1">
              <Text>收到的策略数据 ({strategies.length} 条记录):</Text>
              <ReactJson 
                src={strategies} 
                name={null} 
                theme="rjv-default" 
                collapsed={1}
                displayDataTypes={false}
                enableClipboard={false}
              />
            </TabPane>
            <TabPane tab="数据项检查" key="2">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text>initialCapital 字段检查:</Text>
                {strategies.map((strategy, index) => (
                  <Card key={index} size="small" style={{ marginBottom: '8px' }}>
                    <Row>
                      <Col span={8}>策略名称: {strategy.name}</Col>
                      <Col span={8}>ID: {strategy.id}</Col>
                      <Col span={8}>
                        initialCapital: {' '}
                        {strategy.initialCapital !== undefined ? (
                          <Text type="success">已存在 ({strategy.initialCapital})</Text>
                        ) : (
                          <Text type="danger">缺失</Text>
                        )}
                      </Col>
                    </Row>
                  </Card>
                ))}
              </Space>
            </TabPane>
          </Tabs>
        </Card>
      )}

      {/* 调试对话框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <BugOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span>策略调试信息</span>
          </div>
        }
        open={debugModalVisible}
        onCancel={() => setDebugModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDebugModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1000}
        style={{ top: 20 }}
      >
        {debugData.loading ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Spin size="large" tip="正在获取调试信息..." />
          </div>
        ) : (
          <Tabs
            defaultActiveKey="yaml"
            items={[
              {
                key: 'yaml',
                label: (
                  <span>
                    <FileTextOutlined />
                    策略 YAML
                  </span>
                ),
                children: (
                  <div style={{ maxHeight: '500px', overflow: 'auto' }}>
                    <pre style={{
                      backgroundColor: '#f6f8fa',
                      padding: '16px',
                      borderRadius: '6px',
                      fontSize: '12px',
                      lineHeight: '1.5',
                      margin: 0,
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word'
                    }}>
                      {debugData.strategyYaml}
                    </pre>
                  </div>
                )
              },
              {
                key: 'configs',
                label: (
                  <span>
                    <SettingOutlined />
                    配置文件 ({Object.keys(debugData.configFiles).length})
                  </span>
                ),
                children: (
                  <div style={{ maxHeight: '500px', overflow: 'auto' }}>
                    {Object.keys(debugData.configFiles).length === 0 ? (
                      <Empty description="暂无配置文件" />
                    ) : (
                      <Tabs
                        tabPosition="left"
                        size="small"
                        items={Object.entries(debugData.configFiles).map(([filename, content]) => ({
                          key: filename,
                          label: filename,
                          children: (
                            <div>
                              <div style={{
                                marginBottom: '8px',
                                fontSize: '12px',
                                color: '#666',
                                borderBottom: '1px solid #e8e8e8',
                                paddingBottom: '4px'
                              }}>
                                文件: {filename}
                              </div>
                              <pre style={{
                                backgroundColor: '#f6f8fa',
                                padding: '12px',
                                borderRadius: '4px',
                                fontSize: '11px',
                                lineHeight: '1.4',
                                margin: 0,
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-word',
                                maxHeight: '400px',
                                overflow: 'auto'
                              }}>
                                {content}
                              </pre>
                            </div>
                          )
                        }))}
                      />
                    )}
                  </div>
                )
              }
            ]}
          />
        )}
      </Modal>
    </div>
  );
};

export default LiveStrategyPanel;
