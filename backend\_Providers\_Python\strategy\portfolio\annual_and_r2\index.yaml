# Strategy: Trend + ROC + Volume Ratio ETF Rotation
# Description: Selects the top 1 ETF based on a combined score and rebalances daily.

strategy_id: "annual_and_r2" # 策略唯一标识，需与文件名一致，且不能包含特殊字符
strategy_name: "年化收益_R2" # 策略显示名称

trading_type: "etf" # 目前仅支持同一个市场内进行

universe: # 品种池，格式：交易所代码：品种代码
  - SSE.ETF.518880 # 黄金ETF
  - SSE.ETF.510180 # 中证2000ETF
  - SSE.ETF.513100 # 纳指ETF
  - SZSE.ETF.159915 # 上证180ETF
  

# 基准线品种
benchmark: SSE.ETF.510300 # 上证50ETF

is_for_stk: true

rebalance_interval: daily # 调仓周期: 每日

data_freq: day # 因子计算级别格式 1m 5m 15m 30m 1h day week month
# 计算所需K线数，需 >= max(26, 5, 10, 20) + 1 = 27
bar_count: 60    # 当前值 60 > 27，足够

# --- 排序规则 ---
order_by:
  # 复合评分公式: 趋势 * 0.4 + (ROC5+ROC10) * 0.2 + 量比 * 0.4 (隐式)
  # 注意: ma(volume,5)/ma(volume,20) 是量比(Volume Ratio)的一种近似计算
  formula: "trend_score(close, 25)"
  sort_direction: descending # 从高到低排序 (默认)

top_n: 1 # 持仓品种数量: 只选 1 只

weighting_scheme: equal # 权重分配模式 (因为 top_n=1，这个参数影响不大，但需配置)

# 数据存储配置 (请根据你的实际路径修改)
data:
  path: ../../storage/ # <<<<====== 修改为你的数据存储根目录
  mode: csv
# 回测参数
backtest:
  stime: 201611070930 # 回测开始时间 (YYYYMMDDHHMM)
  etime:  # 回测结束时间 (YYYYMMDDHHMM)
  initial_capital: 500000 # 初始资金
  commission_rate: 0.0002 # 手续费率 (双边)
  slippage_rate: 0.0001   # 滑点比率 (双边)