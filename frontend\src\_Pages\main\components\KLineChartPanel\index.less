.scroll-to-latest-btn {
  font-size: 18px; /* 增大图标尺寸，可按需调整 */
  // 添加 flex 布局确保图标在 Button 内完美居中
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.add-indicator-btn {
  // --- Default Appearance ---
  opacity: 0.6; // 默认 60% 不透明度
  // Inherit standard Ant Design button border, background, color

  // --- Icon Sizing & Centering ---
  font-size: 18px; // 图标大小
  display: flex !important; 
  align-items: center !important;
  justify-content: center !important;

  // --- Transitions ---
  transition: opacity 0.3s, background-color 0.3s, border-color 0.3s, color 0.3s;

  // --- Hover State ---
  &:hover {
    opacity: 0.95 !important; // 悬停时 95% 不透明度
    // No need to change color/border/background explicitly for this effect
  }
}

// 指标面板按钮容器
.indicator-pane-buttons {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 2px;
  z-index: 10;
}

// 指标按钮基础样式
.indicator-button {
  width: 16px;
  height: 16px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color, #fff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 2px;
  cursor: pointer;
  opacity: 0.7;
  color: var(--text-color, #000);
  transition: opacity 0.3s, background-color 0.3s;

  &:hover {
    opacity: 1;
    background: var(--hover-color, #f0f0f0);
  }
}

// 主图按钮容器
.main-pane-button {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  z-index: 10;
}
