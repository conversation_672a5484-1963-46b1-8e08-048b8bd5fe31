import os
import sys
import json
import yaml
import pandas as pd
from typing import Union, Dict, Any
import datetime
from datetime import timezone, timedelta

# --- 使用相对导入 ---
# sys.path manipulation might still be needed if run as script,
# but relative imports are preferred for package integrity.
import sys

# 引用上级目录，引用同级目录
script_path = os.path.abspath(__file__)
script_dir = os.path.dirname(script_path)
sys.path.insert(0, script_dir)
parent_dir = os.path.join(script_dir, '../../')
sys.path.insert(0, os.path.abspath(parent_dir))  # 必须转绝对路径

# 注释掉原来的CSV准备函数导入，改用ExtDataLoader
# from prepare_csv import prepare_csv_files # Use relative import (..) to go up one level
# from prepare_csv_cpt import prepare_csv_files_cpt # <<< 新增：导入加密货币处理函数

from wtpy import WtBtEngine,EngineType
from wtpy.apps import WtBtAnalyst
from strategy.portfolio import MultiFactorsCTA # Use relative import (.) for same directory
from portfolio_analyzer import analyze_backtest_results

# 导入统一的ExtDataLoader
import sys
import os
parent_dir = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, os.path.abspath(parent_dir))
from strategy.unified_ext_data_loader import get_unified_ext_data_loader

# 更改工作目录为 python1 所在的目录
os.chdir(script_dir)

show_log = True
# 一个print的替代函数，允许开关显示
def print_log(message):
    if show_log:
        print(message)

# 写一个函数，传入相对路径，使用script_dir作为根目录，返回绝对路径

# 转化为 set_sel_strategy 能够识别的方式 min, d, w, m
# 返回 单位 和 数量
def convert_period1(period:str) -> tuple[str, int]:
    if period == "1m":
        return "min", 1
    elif period == "5m":
        return "min", 5
    elif period == "15m":
        return "min", 15
    elif period == "30m":
        return "min", 30
    elif period == "1h":
        return "min", 60
    elif period == "day":
        return "d", 1
    elif period == "week":
        return "w", 1
    elif period == "month":
        return "m", 1
    else:
        return period, 1

# 根据策略配置文件（对于回测，commodities.json中一定要有回测的品种，contracts.json无所谓）
# a. common基础文件：品种与合约须手动检查，与runBT.py对应；
# 其他的可以用demo的模板，详解见基础文件详解章节
# b. storage数据：存放历史数据
def do_run_backtest(strategy_id, strategy_yaml = ""):
    """
    运行回测引擎
    @param strategy_id: 策略名称，例如trend_roc_vol_etf
    @return: 回测结果
    """
    print_log(f"[策略回测] 开始回测策略: {strategy_id}")

    # 构建并读取策略配置文件
    if strategy_yaml == "":
        config_path = os.path.join(f"./{strategy_id}/index.yaml")
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"[策略回测] 策略配置文件不存在: {config_path}")

        # 获取策略目录的绝对路径，用于正确解析相对路径
        strategy_dir = os.path.abspath(os.path.join(".", strategy_id))

        with open(config_path, 'r', encoding='utf-8') as f:
            strategy_config = yaml.safe_load(f)
    else:
        strategy_config = yaml.safe_load(strategy_yaml)
        strategy_config['strategy_id'] = strategy_id    # 对于定制策略，strategy_name必须是uuid
        strategy_dir = os.getcwd() # Assuming yaml passed as string runs from current dir

    # 警告：强烈推荐，一个python进程初始化一种类型的引擎，例如针对股票，例如针对ETF，例如针对期权，不要混用 !!!!!!之后的初始化引擎不会起效！
    # 创建一个运行环境，并加入策略
    engine = WtBtEngine(EngineType.ET_CTA)
    engine.clear_cache()

    # 从配置中读取交易类型
    trading_type = strategy_config.get('trading_type', 'etf') # 默认为etf

    # 根据交易类型确定文件名
    if "stock" in trading_type.lower(): # 如果包含stock字样，则引用股票
        comm_file = "stk_comms.json"
        contract_file = "stocks.json"
    # TODO: 合约品种具有特殊性，暂时不做特殊处理
    elif trading_type == "option":
        comm_file = "ifopt_comms.json" # 示例，根据实际期权类型调整
        contract_file = "if_options.json" # 示例，根据实际期权类型调整
    elif "etf" in trading_type.lower():
        comm_file = "stk_comms.json"    # etf也是用股票的类别定义，否则针对股票/etf策略进行回测时，会造成成交量倍数等混乱
        contract_file = "etfs.json"
    elif "crypto" in trading_type.lower():
        comm_file = "cypto_comms.json"
        contract_file = "stocks.json"   # 这个应该没有作用，随便写
    else:
        # 如果类型未知，使用默认或抛出错误
        print_log(f"[策略回测] 警告: 未知的 trading_type '{trading_type}', 将使用默认文件")
        comm_file = "stk_comms.json"
        contract_file = "stocks.json"

    print_log(f"[策略回测] 根据交易类型 '{trading_type}' 加载: commfile='{comm_file}', contractfile='{contract_file}'")

    # 使用动态确定的文件名初始化引擎
    engine.init('../common/', "configbt.yaml", commfile=comm_file, contractfile=contract_file)

    # 从index.yaml中获取回测时间范围
    backtest_start = strategy_config['backtest']['stime']
    # etime 允许为空或不存在，表示运行到最新数据
    backtest_end = strategy_config['backtest'].get('etime', None)
    if backtest_end == "": backtest_end = None # Handle empty string case

    # 先配置存储路径，以便检查数据可用范围
    # 正确解析data.path（相对于策略目录）
    data_path = strategy_config['data']['path']
    if not os.path.isabs(data_path):
        # 将相对路径转为绝对路径（基于策略目录）
        storage_base_path = os.path.normpath(os.path.join(strategy_dir, data_path))
    else:
        storage_base_path = data_path

    print_log(f"[策略回测] 数据存储路径: {storage_base_path}")

    # 使用ExtDataLoader替代CSV数据准备
    codes = [code for code in strategy_config['universe']]

    print_log(f"[数据准备] 使用ExtDataLoader加载数据，品种: {codes}")
    print_log(f"[数据准备] 数据周期: {strategy_config['data_freq']}")

    # 注册ExtDataLoader到回测引擎
    ext_loader = get_unified_ext_data_loader()
    engine.set_extended_data_loader(loader=ext_loader, bAutoTrans=False)
    print_log(f"[数据准备] ExtDataLoader已注册到回测引擎")

    # 配置引擎使用正确的存储路径（ExtDataLoader模式下仍需要配置输出路径）
    engine.configBTStorage(mode=strategy_config['data']['mode'], path=storage_base_path)

    # 使用ExtDataLoader时，不需要检查CSV文件，直接使用配置的回测时间
    print_log(f"[数据准备] 使用ExtDataLoader模式，跳过CSV文件检查")
    print_log(f"[数据准备] 将使用配置的回测时间范围")

    # 使用ExtDataLoader时，直接使用配置的回测时间，不需要检查CSV文件
    print_log("\n" + "="*80)
    print_log(f"[策略回测] 使用ExtDataLoader模式，直接使用配置的回测时间")
    print_log("="*80 + "\n")

    # 直接使用配置的回测时间
    actual_start = backtest_start

    # 确定回测结束时间
    actual_end = 0
    config_end = strategy_config['backtest'].get('etime', None)

    if config_end and str(config_end).strip() != "":
        try:
            actual_end = int(config_end)
            print_log(f"[策略回测] 使用配置文件中的结束时间: {actual_end}")
        except ValueError:
            print_log(f"[策略回测] 警告: 配置文件中的 etime '{config_end}' 格式无效")
            config_end = None

    if actual_end == 0:
        # 使用当前时间作为结束时间
        try:
            now = datetime.datetime.now()
            actual_end = int(now.strftime("%Y%m%d%H%M"))
            print_log(f"[策略回测] 未配置结束时间，使用当前系统时间作为结束时间: {actual_end}")
        except Exception as e:
            print_log(f"[策略回测] 错误: 获取当前系统时间失败: {e}")
            raise ValueError("无法设置回测结束时间") from e

    print_log(f"[策略回测] 最终回测时间范围: {actual_start} - {actual_end}")

    # 配置回测时间
    engine.configBacktest(actual_start, actual_end)
    engine.commitBTConfig()

    # 创建策略实例
    straInfo = MultiFactorsCTA(
        name=strategy_id,
        codes=codes,
        barCnt=strategy_config['bar_count'],
        period=strategy_config['data_freq'],
        # 新增参数传递
        order_by_config=strategy_config.get('order_by', {}),
        buy_rules_config=strategy_config.get('buy_rules', {}),
        sell_rules_config=strategy_config.get('sell_rules', {}),
        top_n=strategy_config.get('top_n', 1),
        weighting_scheme=strategy_config.get('weighting_scheme', 'equal'),
        rebalance_interval=strategy_config.get('rebalance_interval', 'daily')
    )

    # 设置策略
    engine.set_cta_strategy(straInfo)

    print_log(f"[策略回测 Caller] 开始回测 ...........")
    engine.run_backtest()

    # 释放回测资源
    engine.release_backtest()
    print_log(f"[策略回测] 回测资源已释放: {strategy_id}")

    # 尝试显式删除对象并触发垃圾回收
    try:
        del engine
        import gc
        gc.collect()
        print_log(f"[策略回测] 引擎对象已尝试删除并执行垃圾回收: {strategy_id}")
    except NameError:
        print_log(f"[策略回测] 引擎对象 'engine' 可能已不存在，无需删除。")

    # 返回策略品种品类和是否成功
    result = {
        "trading_type": trading_type,
        "success": True
    }

    return result



def run_backtest(strategy_id, strategy_yaml = "", output_dir="./outputs_bt") -> dict:

    # portfolio_center.py 中添加

    print(f"[数据加载] 当前工作目录: {os.getcwd()}")


    try:
        # 执行回测
        # 不再接收 initial_capital
        result = do_run_backtest(strategy_id, strategy_yaml)

        print(f"[回测结果] 回测返回的结果: {result}")

        # 检查回测结果是否包含必要的信息
        if not isinstance(result, dict) or 'success' not in result or not result.get('success', False):
            error_msg = result.get('error', '未知错误') if isinstance(result, dict) else '回测结果格式不正确'
            print(f"[回测错误] 回测执行失败: {error_msg}")
            return {"success": False, "error": error_msg}

        # 构造分析需要的完整输出目录路径
        strategy_output_dir = os.path.join(output_dir, strategy_id)

        # 检查输出目录是否存在
        if not os.path.exists(strategy_output_dir):
            print(f"[回测警告] 输出目录不存在: {strategy_output_dir}，尝试创建...")
            try:
                os.makedirs(strategy_output_dir, exist_ok=True)
            except Exception as e:
                print(f"[回测错误] 创建输出目录失败: {e}")
                return {"success": False, "error": f"创建输出目录失败: {e}"}

        # 检查是否有回测结果文件
        result_files = [f for f in os.listdir(strategy_output_dir) if f.endswith('.json') or f.endswith('.csv')]
        if not result_files:
            print(f"[回测警告] 输出目录中没有找到回测结果文件: {strategy_output_dir}")
            print(f"[回测警告] 这可能是因为回测提前结束，例如因为数据不足")
            return {
                "success": False,
                "error": "回测未生成结果文件，可能是因为数据不足或其他原因导致回测提前结束",
                "trading_type": result.get('trading_type', 'unknown'),
                "strategy_id": strategy_id,
                "output_directory": strategy_output_dir
            }

        # 调用分析模块获取结果
        print(f"[回测分析] 开始分析回测结果，输出目录: {strategy_output_dir}")
        try:
            analysis_result = analyze_backtest_results(strategy_output_dir, result['trading_type'])

            # 处理分析结果
            if "error" in analysis_result:
                print(f"[回测分析] 分析失败: {analysis_result['error']}")
                return {"success": False, "error": f"回测分析失败: {analysis_result['error']}"}

            # 补充基础信息到返回结果 (移除 initial_capital)
            analysis_result.update({
                "output_directory": strategy_output_dir,
                "strategy_id": strategy_id,
                "success": True
            })

            print(f"[回测完成] 回测和分析成功完成: {strategy_id}")
            return analysis_result

        except Exception as e:
            print(f"[回测分析] 分析过程中发生异常: {e}")
            import traceback
            print(traceback.format_exc())
            return {"success": False, "error": f"回测分析过程中发生异常: {e}"}

    except Exception as e:
        print(f"[回测错误] 回测执行过程中发生异常: {e}")
        import traceback
        print(traceback.format_exc())
        return {"success": False, "error": f"回测执行过程中发生异常: {e}"}


if __name__ == "__main__":
    # 测试运行，使用命令行参数指定策略名称
    strategy_id = sys.argv[1] if len(sys.argv) > 1 else "trend_roc_vol_etf"

    # 执行回测 (不再接收返回的 capital)
    do_run_backtest(strategy_id)
    print_log(f"[策略回测] 回测完成: {strategy_id}")

    # 将回测的输出数据目录传递给绩效分析模块
    analyst = WtBtAnalyst()

    # 为 Analyst 读取初始资金 (从配置文件)
    config_path = os.path.join(f"./{strategy_id}/index.yaml")
    the_init_capital = 1000000 # Default if config fails
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                strategy_config = yaml.safe_load(f)
                the_init_capital = strategy_config['backtest']['initial_capital']
        except Exception as e:
            print_log(f"[策略回测] 无法从配置 {config_path} 读取初始资金用于分析: {e}")
    else:
        print_log(f"[策略回测] 配置文件 {config_path} 不存在，为分析器使用默认初始资金 {the_init_capital}")

    analyst.add_strategy(strategy_id, folder="./outputs_bt/",
                        init_capital=the_init_capital,
                        rf=0.02, annual_trading_days=240)
    # 运行绩效模块
    analyst.run_new()