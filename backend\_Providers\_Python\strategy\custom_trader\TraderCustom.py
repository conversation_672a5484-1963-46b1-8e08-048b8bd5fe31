#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Wonder Trader自定义交易模块

该模块实现了Wonder Trader所需的交易接口，
将Wonder Trader的交易请求转发到我们的后端tradeHandler模块。

这是一个真正的Wonder Trader交易模块，可以在tdtraders.yaml中直接使用：

traders:
-   active: true
    client: 1
    host: 127.0.0.1        # 后端服务器地址
    id: custom_trader      # 交易通道标识符
    module: TraderCustom   # 使用自定义交易模块
    user: username         # 用户名
    pass: dummy_password   # 占位符密码
    acckey: user1_easytrader_ths  # clientKey，用于标识具体的交易客户端
    port: 3000             # 后端服务器端口
    quick: true
"""

import logging
import requests
import json
import time
from typing import Dict, Any, Optional

logger = logging.getLogger("TraderCustom")

class TraderCustom:
    """
    Wonder Trader自定义交易模块
    实现Wonder Trader所需的交易接口，将交易请求转发到后端tradeHandler
    """
    
    def __init__(self, **kwargs):
        """
        初始化自定义交易模块
        
        Args:
            kwargs: 配置参数，来自tdtraders.yaml，包含：
                - user: 用户名
                - acckey: 客户端标识（clientKey）
                - host: 后端服务器地址
                - port: 后端服务器端口
                - id: 交易通道ID
        """
        self.trader_id = kwargs.get('id', 'custom_trader')
        self.username = kwargs.get('user', '')
        self.acckey = kwargs.get('acckey', '')  # 这就是clientKey
        self.host = kwargs.get('host', '127.0.0.1')
        self.port = kwargs.get('port', 3000)
        self.timeout = 10
        
        # 构建后端API基础URL
        self.backend_url = f"http://{self.host}:{self.port}/api/trade"
        
        # 检查参数
        if not self.username:
            raise ValueError("未指定用户名 (user)")
        
        if not self.acckey:
            raise ValueError("未指定客户端标识 (acckey)")
        
        logger.info(f"初始化Wonder Trader自定义交易模块:")
        logger.info(f"  交易通道ID: {self.trader_id}")
        logger.info(f"  用户名: {self.username}")
        logger.info(f"  客户端标识: {self.acckey}")
        logger.info(f"  后端地址: {self.backend_url}")
        
        # 连接状态
        self.connected = False
        
        # 检查后端连接
        self._check_backend_connection()
    
    def _check_backend_connection(self) -> bool:
        """检查与后端tradeHandler的连接"""
        try:
            # 测试后端连接
            test_url = f"{self.backend_url}/test_connection"
            response = requests.get(test_url, timeout=self.timeout)
            if response.status_code == 200:
                logger.info(f"成功连接到后端tradeHandler: {self.backend_url}")
                self.connected = True
                return True
            else:
                logger.error(f"连接后端失败，状态码: {response.status_code}")
                self.connected = False
                return False
        except Exception as e:
            logger.error(f"检查后端连接异常: {e}")
            self.connected = False
            return False
    
    def _request_backend(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送请求到后端tradeHandler"""
        url = f"{self.backend_url}{endpoint}"
        try:
            # 添加认证信息
            request_data = {
                'acckey': self.acckey,  # 使用acckey标识客户端
                'username': self.username,
                **(data or {})
            }
            
            response = requests.post(url, json=request_data, timeout=self.timeout)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return result.get('data', {})
                else:
                    error_msg = result.get('message', '未知错误')
                    logger.error(f"后端请求失败: {error_msg}")
                    raise Exception(error_msg)
            else:
                logger.error(f"后端请求失败，状态码: {response.status_code}")
                raise Exception(f"后端请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"请求后端时发生错误: {str(e)}")
            raise
    
    # ===== Wonder Trader交易接口实现 =====
    
    def connect(self) -> bool:
        """连接到交易服务器"""
        logger.info(f"[{self.trader_id}] 连接到交易服务器")
        return self._check_backend_connection()
    
    def disconnect(self):
        """断开连接"""
        logger.info(f"[{self.trader_id}] 断开连接")
        self.connected = False
    
    def isConnected(self) -> bool:
        """检查连接状态"""
        return self.connected
    
    def buy(self, code: str, price: float, qty: int, **kwargs) -> str:
        """买入操作"""
        logger.info(f"[{self.trader_id}] 买入请求: code={code}, price={price}, qty={qty}")
        
        data = {
            'action': 'buy',
            'params': {
                'code': code,
                'price': float(price),
                'amount': int(qty)
            }
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            logger.info(f"[{self.trader_id}] 买入成功: {result}")
            # 返回订单ID（如果有的话）
            return result.get('order_id', f"buy_{int(time.time())}")
        except Exception as e:
            logger.error(f"[{self.trader_id}] 买入失败: {e}")
            raise
    
    def sell(self, code: str, price: float, qty: int, **kwargs) -> str:
        """卖出操作"""
        logger.info(f"[{self.trader_id}] 卖出请求: code={code}, price={price}, qty={qty}")
        
        data = {
            'action': 'sell',
            'params': {
                'code': code,
                'price': float(price),
                'amount': int(qty)
            }
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            logger.info(f"[{self.trader_id}] 卖出成功: {result}")
            # 返回订单ID（如果有的话）
            return result.get('order_id', f"sell_{int(time.time())}")
        except Exception as e:
            logger.error(f"[{self.trader_id}] 卖出失败: {e}")
            raise
    
    def cancel(self, order_id: str) -> bool:
        """撤单操作"""
        logger.info(f"[{self.trader_id}] 撤单请求: order_id={order_id}")
        
        data = {
            'action': 'cancel',
            'params': {
                'order_id': order_id
            }
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            logger.info(f"[{self.trader_id}] 撤单成功: {result}")
            return True
        except Exception as e:
            logger.error(f"[{self.trader_id}] 撤单失败: {e}")
            return False
    
    def queryAccount(self) -> Dict[str, Any]:
        """查询账户信息"""
        logger.debug(f"[{self.trader_id}] 查询账户信息")
        
        data = {
            'action': 'query_balance',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[{self.trader_id}] 查询账户失败: {e}")
            return {}
    
    def queryPositions(self) -> Dict[str, Any]:
        """查询持仓"""
        logger.debug(f"[{self.trader_id}] 查询持仓")
        
        data = {
            'action': 'query_position',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[{self.trader_id}] 查询持仓失败: {e}")
            return {}
    
    def queryOrders(self) -> Dict[str, Any]:
        """查询委托"""
        logger.debug(f"[{self.trader_id}] 查询委托")
        
        data = {
            'action': 'query_entrusts',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[{self.trader_id}] 查询委托失败: {e}")
            return {}
    
    def queryTrades(self) -> Dict[str, Any]:
        """查询成交"""
        logger.debug(f"[{self.trader_id}] 查询成交")
        
        data = {
            'action': 'query_trades',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[{self.trader_id}] 查询成交失败: {e}")
            return {}

# Wonder Trader模块入口函数
def create(**kwargs) -> TraderCustom:
    """
    创建自定义交易模块实例
    这是Wonder Trader调用的入口函数
    """
    return TraderCustom(**kwargs)
