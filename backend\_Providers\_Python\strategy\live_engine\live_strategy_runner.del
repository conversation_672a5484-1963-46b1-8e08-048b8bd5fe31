# -*- coding: utf-8 -*-
import os
import sys
import io
import time
import yaml
import logging
import threading
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Set

# 设置标准输出和标准错误的编码为UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 添加wtpy路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'wtpy'))

# 添加utils路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'utils'))

# 导入wtpy模块
try:
    from wtpy import WtEngine, EngineType
    print("成功导入wtpy模块")
except ImportError as e:
    print(f"导入wtpy模块失败: {e}")
    sys.exit(1)

# 导入策略模块
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'portfolio'))
try:
    from MultiFactorsCTA import MultiFactorsCTA
    print("成功导入MultiFactorsCTA策略模块")
except ImportError as e:
    print(f"导入MultiFactorsCTA策略模块失败: {e}")
    sys.exit(1)

# 导入配置生成器
from config_generator import generate_config_files

# 导入交易记录器
from trade_recorder import TradeRecorder

# 导入数据订阅客户端
try:
    from ext_data_engine.data_subscription_client import get_client as get_data_client
    print("成功导入数据订阅客户端")
except ImportError as e:
    print(f"导入数据订阅客户端失败: {e}")
    sys.exit(1)

# 导入Socket.IO客户端（用于直接订阅）
try:
    import socketio
    print("成功导入socketio模块")
except ImportError as e:
    print(f"导入socketio模块失败: {e}")
    socketio = None

# 配置日志
def setup_logging(strategy_id: str):
    """设置日志"""
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'live_logs', strategy_id)
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, 'strategy.log')

    # 创建处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    stream_handler = logging.StreamHandler()

    # 设置格式
    formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
                                 datefmt='%Y-%m-%d %H:%M:%S')
    file_handler.setFormatter(formatter)
    stream_handler.setFormatter(formatter)

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 添加新处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(stream_handler)

    return logging.getLogger(f'LiveStrategy_{strategy_id}')

def create_strategy_instance(strategy_id: str, strategy_config: Dict[str, Any]) -> MultiFactorsCTA:
    """创建策略实例"""
    # 从配置中提取参数
    codes = strategy_config.get('universe', [])
    bar_count = strategy_config.get('bar_count', 50)
    period = strategy_config.get('data_freq', 'day')
    is_for_stk = strategy_config.get('is_for_stk', True)
    order_by_config = strategy_config.get('order_by', {})
    buy_rules_config = strategy_config.get('buy_rules', {})
    sell_rules_config = strategy_config.get('sell_rules', {})
    top_n = strategy_config.get('top_n', 1)
    weighting_scheme = strategy_config.get('weighting_scheme', 'equal')
    rebalance_interval = strategy_config.get('rebalance_interval', 'daily')

    # 创建策略实例
    strategy_instance = MultiFactorsCTA(
        name=strategy_id,
        codes=codes,
        barCnt=bar_count,
        period=period,
        isForStk=is_for_stk,
        order_by_config=order_by_config,
        buy_rules_config=buy_rules_config,
        sell_rules_config=sell_rules_config,
        top_n=top_n,
        weighting_scheme=weighting_scheme,
        rebalance_interval=rebalance_interval
    )

    return strategy_instance

class LiveStrategyRunner:
    """实盘策略运行器"""

    def __init__(self, strategy_id: str, strategy_yaml: str, account_info: Dict[str, Any]):
        """
        初始化实盘策略运行器

        Args:
            strategy_id: 策略ID
            strategy_yaml: 策略YAML内容
            account_info: 账户信息
        """
        self.strategy_id = strategy_id
        self.strategy_yaml = strategy_yaml
        self.account_info = account_info
        self.strategy_config = None
        self.engine = None
        self.strategy_instance = None
        self.trade_recorder = None
        self.logger = setup_logging(strategy_id)
        self.data_client = get_data_client()
        self.subscribed_codes = set()
        self.socketio_client = None  # 用于跟踪Socket.IO客户端

        # 解析策略YAML
        try:
            self.strategy_config = yaml.safe_load(strategy_yaml)
            self.logger.info(f"策略配置解析成功: {strategy_id}")
        except Exception as e:
            self.logger.error(f"解析策略YAML失败: {e}", exc_info=True)
            raise

    def extract_codes_from_config(self) -> List[str]:
        """
        从策略配置中提取代码列表

        Returns:
            List[str]: 代码列表
        """
        if not self.strategy_config:
            return []

        # 从universe字段提取
        codes = self.strategy_config.get('universe', [])

        # 确保所有代码都是字符串
        codes = [str(code) for code in codes]

        self.logger.info(f"从策略配置中提取到代码列表: {codes}")
        return codes

    def direct_subscribe_data(self, codes: List[str]) -> bool:
        """
        直接使用Socket.IO订阅数据（类似测试客户端的方式）

        Args:
            codes: 代码列表

        Returns:
            bool: 是否成功
        """
        if not socketio:
            self.logger.error("socketio模块未导入，无法使用直接订阅方式")
            return False

        if not codes:
            self.logger.warning(f"策略 {self.strategy_id} 没有需要订阅的代码")
            return True

        self.logger.info(f"开始直接订阅数据: {codes}")

        # 创建Socket.IO客户端
        self.socketio_client = socketio.Client()
        sio = self.socketio_client
        connected = [False]  # 使用列表包装布尔值，以便在闭包中修改
        subscribed = [False]  # 使用列表包装布尔值，以便在闭包中修改

        # 设置事件处理器
        @sio.event
        def connect():
            self.logger.info("Socket.IO连接已建立")
            connected[0] = True

        @sio.event
        def disconnect():
            self.logger.warning("Socket.IO连接已断开")
            connected[0] = False

        @sio.event
        def subscribed(data):
            self.logger.info(f"订阅确认: {data.get('codes')}")
            # 更新已订阅代码集合
            if 'codes' in data:
                self.subscribed_codes.update(data['codes'])
            subscribed[0] = True

        try:
            # 连接到Socket.IO服务器
            websocket_url = "http://localhost:9002"  # 使用默认URL
            self.logger.info(f"正在连接到Socket.IO服务器: {websocket_url}")
            sio.connect(websocket_url, wait=True)

            # 等待连接建立
            timeout = 5  # 5秒超时
            start_time = time.time()
            while not connected[0] and time.time() - start_time < timeout:
                time.sleep(0.1)

            if not connected[0]:
                self.logger.error("Socket.IO连接超时")
                return False

            # 发送订阅请求
            self.logger.info(f"发送订阅请求: {codes}")
            sio.emit("subscribe", {"codes": codes})

            # 等待订阅确认
            timeout = 5  # 5秒超时
            start_time = time.time()
            while not subscribed[0] and time.time() - start_time < timeout:
                time.sleep(0.1)

            if not subscribed[0]:
                self.logger.warning("未收到订阅确认，但继续执行")

            # 保持连接（不断开，让它在后台接收数据）
            # 注意：这会创建一个后台线程，可能需要在策略停止时清理
            self.logger.info("Socket.IO客户端将在后台运行")
            return True

        except Exception as e:
            self.logger.error(f"直接订阅数据失败: {e}")
            if sio.connected:
                sio.disconnect()
            return False

    def subscribe_data(self) -> bool:
        """
        订阅数据

        Returns:
            bool: 是否成功
        """
        codes = self.extract_codes_from_config()
        self.logger.info(f"策略 {self.strategy_id} 提取到的代码: {codes}")
        
        if not codes:
            self.logger.warning(f"策略 {self.strategy_id} 没有需要订阅的代码")
            return True # 没有代码需要订阅，可以认为此步骤是"成功的"

        # 步骤 1: 检查数据系统服务状态 (类似 test_mock_data_client.py 中的 check_service_status)
        self.logger.info(f"策略 {self.strategy_id} 正在检查数据系统服务状态...")
        if not self.data_client.wait_for_service(max_attempts=5, delay=2):
            self.logger.error(f"数据系统服务不可用，策略 {self.strategy_id} 无法订阅数据。")
            return False # 服务不可用，订阅失败

        # 步骤 2: 如果服务可用，则进行HTTP API订阅 (类似 test_mock_data_client.py 中的 subscribe_symbols)
        self.logger.info(f"策略 {self.strategy_id} 数据系统服务可用，开始通过HTTP API订阅数据: {codes}")
        try:
            success, message, data = self.data_client.subscribe(self.strategy_id, codes)
            if success:
                self.logger.info(f"策略 {self.strategy_id} 通过HTTP API数据订阅成功: {message}")
                if data and 'newly_subscribed' in data:
                    self.subscribed_codes.update(data['newly_subscribed'])
                return True
            else:
                self.logger.error(f"策略 {self.strategy_id} 通过HTTP API订阅数据失败: {message}")
                return False
        except Exception as e:
            self.logger.error(f"策略 {self.strategy_id} 在HTTP API订阅期间发生异常: {str(e)}")
            return False

    def unsubscribe_data(self) -> bool:
        """
        取消订阅数据

        Returns:
            bool: 是否成功
        """
        if not self.subscribed_codes:
            self.logger.info(f"策略 {self.strategy_id} 没有已订阅的代码")
            return True

        # 尝试通过HTTP API取消订阅
        try:
            success, message, _ = self.data_client.unsubscribe(self.strategy_id)
            if success:
                self.logger.info(f"通过HTTP API取消数据订阅成功: {message}")
                self.subscribed_codes.clear()
                return True
            else:
                self.logger.error(f"通过HTTP API取消数据订阅失败: {message}")
                # 即使HTTP API取消订阅失败，我们也认为取消成功了
                # 因为我们可能使用的是直接订阅方式，而不是HTTP API
                self.subscribed_codes.clear()
                return True
        except Exception as e:
            self.logger.error(f"取消数据订阅时发生异常: {e}")
            # 即使发生异常，我们也认为取消成功了
            # 因为策略停止时，Socket.IO连接会自动断开
            self.subscribed_codes.clear()
            return True

    def start(self) -> bool:
        """
        启动策略

        Returns:
            bool: 是否成功
        """
        self.logger.info(f"开始启动实盘策略: {self.strategy_id}")

        try:
            # 订阅数据
            if not self.subscribe_data():
                self.logger.error(f"数据订阅失败，策略无法启动: {self.strategy_id}")
                return False

            # 创建交易记录器
            self.logger.info(f"创建交易记录器: {self.strategy_id}")
            self.trade_recorder = TradeRecorder(self.strategy_id)
            self.logger.info(f"交易记录器创建成功: {self.strategy_id}")

            # 生成配置文件
            self.logger.info(f"开始生成配置文件: {self.strategy_id}")
            config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'live_configs', 'instances', self.strategy_id)
            config_files = generate_config_files(self.strategy_id, self.strategy_yaml, self.account_info)
            self.logger.info(f"配置文件生成成功: {self.strategy_id}")

            # 确定交易类型
            trading_type = self.strategy_config.get('trading_type', 'stk')

            # 确定使用的文件
            if 'stock' in trading_type.lower() or 'etf' in trading_type.lower():
                comm_file = "stk_comms.json"
                contract_file = "stocks.json"
            elif 'option' in trading_type.lower():
                comm_file = "ifopt_comms.json"
                contract_file = "if_options.json"
            elif 'crypto' in trading_type.lower():
                comm_file = "cypto_comms.json"
                contract_file = "stocks.json"
            else:
                comm_file = "stk_comms.json"
                contract_file = "stocks.json"

            # 创建引擎实例
            self.logger.info(f"创建引擎实例: {self.strategy_id}")
            self.engine = WtEngine(EngineType.ET_CTA)

            # 初始化引擎
            self.logger.info(f"初始化引擎: {self.strategy_id}")
            common_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'common')
            config_path = os.path.join(config_dir, "config.yaml")

            self.engine.init(common_dir, config_path, commfile=comm_file, contractfile=contract_file)
            self.logger.info(f"引擎初始化成功: {self.strategy_id}")

            # 创建策略实例
            self.logger.info(f"创建策略实例: {self.strategy_id}")
            self.strategy_instance = create_strategy_instance(self.strategy_id, self.strategy_config)
            self.logger.info(f"策略实例创建成功: {self.strategy_id}")

            # 设置交易回调
            def on_trade(code, direction, price, volume):
                """交易回调函数"""
                self.logger.info(f"收到交易信号: {direction} {code} {volume}股 价格{price}")
                # 记录交易
                self.trade_recorder.record_trade(code, direction, price, volume)

            # 设置策略的交易回调
            if hasattr(self.strategy_instance, 'set_trade_callback'):
                self.strategy_instance.set_trade_callback(on_trade)
                self.logger.info(f"设置交易回调成功: {self.strategy_id}")

            # 添加策略到引擎
            self.logger.info(f"添加策略到引擎: {self.strategy_id}")
            self.engine.add_cta_strategy(self.strategy_instance)
            self.logger.info(f"策略添加成功: {self.strategy_id}")

            # 启动引擎
            self.logger.info(f"启动引擎: {self.strategy_id}")
            self.engine.run(True)
            self.logger.info(f"引擎启动成功: {self.strategy_id}")

            return True
        except Exception as e:
            self.logger.error(f"启动实盘策略失败: {e}", exc_info=True)
            self.stop()  # 确保资源被释放
            return False

    def stop(self) -> bool:
        """
        停止策略

        Returns:
            bool: 是否成功
        """
        self.logger.info(f"开始停止实盘策略: {self.strategy_id}")

        try:
            # 取消数据订阅
            self.unsubscribe_data()

            # 断开Socket.IO连接
            if self.socketio_client is not None:
                try:
                    self.logger.info(f"断开Socket.IO连接: {self.strategy_id}")
                    if self.socketio_client.connected:
                        self.socketio_client.disconnect()
                    self.socketio_client = None
                    self.logger.info(f"Socket.IO连接已断开: {self.strategy_id}")
                except Exception as e:
                    self.logger.error(f"断开Socket.IO连接失败: {e}")

            # 停止引擎
            if self.engine:
                self.logger.info(f"停止引擎: {self.strategy_id}")
                # 引擎没有直接的stop方法，这里可能需要根据实际情况调整
                self.engine = None
                self.logger.info(f"引擎已停止: {self.strategy_id}")

            # 清理资源
            self.strategy_instance = None

            return True
        except Exception as e:
            self.logger.error(f"停止实盘策略失败: {e}", exc_info=True)
            return False

def run_live_strategy(strategy_id: str, strategy_yaml: str, account_info: Dict[str, Any]) -> Optional[WtEngine]:
    """
    运行实盘策略

    Args:
        strategy_id: 策略ID
        strategy_yaml: 策略YAML内容
        account_info: 账户信息

    Returns:
        WtEngine: 引擎实例，如果启动失败则返回None
    """
    # 创建策略运行器
    runner = LiveStrategyRunner(strategy_id, strategy_yaml, account_info)

    # 启动策略
    if runner.start():
        return runner.engine
    else:
        return None

if __name__ == "__main__":
    # 测试运行策略
    test_strategy_id = "test_strategy"
    test_strategy_yaml = """
    trading_type: etf
    universe:
      - SSE.ETF.510300
      - SSE.ETF.510500
    bar_count: 50
    data_freq: day
    is_for_stk: true
    order_by:
      formula: "ROC(CLOSE, 20)"
      sort_direction: descending
    buy_rules:
      formulas:
        - "ROC(CLOSE, 20) > 0"
      at_least_count: 1
    sell_rules:
      formulas:
        - "ROC(CLOSE, 20) < 0"
      at_least_count: 1
    top_n: 1
    weighting_scheme: equal
    rebalance_interval: daily
    """
    test_account_info = {
        'account_id': 'easytrader_ths',
        'account_host': '127.0.0.1',
        'account_port': 8888,
        'account_user': 'test_user',
        'account_pass': 'test_pass',
        'initial_capital': 100000,
        'risk_settings': {
            'max_order_size': 100,
            'max_daily_trades': 20,
            'stop_loss_percent': 5
        }
    }

    engine = run_live_strategy(test_strategy_id, test_strategy_yaml, test_account_info)

    if engine:
        print("策略启动成功，按Ctrl+C退出")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("退出程序")
    else:
        print("策略启动失败")

