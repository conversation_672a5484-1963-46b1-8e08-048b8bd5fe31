import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import { ProLayout } from '@ant-design/pro-components';
import { Avatar, Dropdown, Space, message, Modal, Drawer } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  SkinOutlined,
  DashboardOutlined,
  FundOutlined,
  SettingOutlined,
  RobotOutlined,
  SlidersOutlined,
  RocketOutlined,
} from '@ant-design/icons';
// import ChatWindow from './Chat/ChatWindow';  // 注释掉聊天窗口导入
import DashboardContent from './components/DashboardContent';
import StrategyPanel from './components/StrategyPanel';
import ChatPanel from './components/ChatPanel';
import SettingsPanel from './components/SettingsPanel';
import LiveStrategyPanel from './components/LiveStrategyPanel';
import { EventBus } from '@/events/eventBus';
import { chatVisibleAtom, isLoading<PERSON><PERSON>, kline<PERSON><PERSON> } from '@/store/state';
import { useAtom } from 'jotai';
import { ChartEvents, ChatEvents, MarketEvents, UserEvents } from '@/events/events';
import UserInfoModal from './components/UserInfoModal';
import { removeToken } from '@/utils/auth';
import { useUser } from '@/models/useUser';
import { useTheme, ThemeType } from '@/models/useTheme';
import { currentSymbolAtom, currentPeriodAtom } from '@/store/state';
import { KLineInterval, Symbol } from '@/shared_types/market';
import { FrontendConfig } from '@/shared_types/enviorment';
import { DrawingLine } from '@/shared_types/chart';
import KLineChartPanel from '../main/components/KLineChartPanel';
import StockPoolWidget from '@/_Widgets/StockPoolWidget';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  // const [sessionId, setSessionId] = useState<number | null>(null);  // 注释掉聊天相关状态
  const [isUserInfoModalVisible, setIsUserInfoModalVisible] = useState(false);
  const [isThemeModalVisible, setIsThemeModalVisible] = useState(false);
  const [selectedKey, setSelectedKey] = useState('dashboard');
  const { userInfo } = useUser();
  const { theme: selectedTheme, changeTheme } = useTheme();

  // const [chatVisible, setChatVisible] = useAtom(chatVisibleAtom);  // 注释掉聊天状态
  const [, setKline] = useAtom(klineAtom);
  const [, setIsLoading] = useAtom(isLoadingAtom);

  const [currentSymbol, setCurrentSymbol] = useAtom(currentSymbolAtom);
  const [currentPeriod, setCurrentPeriod] = useAtom(currentPeriodAtom);

  const [config, setConfig] = useState<FrontendConfig | null>(null);

  // 股票池管理面板状态
  const [stockPoolVisible, setStockPoolVisible] = useState(false);
  const [stockPoolCurrentList, setStockPoolCurrentList] = useState<string>(''); // 当前列表名称
  const [stockPoolTag, setStockPoolTag] = useState<number>(0); // 当前列表tag

  useEffect(() => {

    /*const subscription = EventBus.on(
      MarketEvents.Types.REALTIMEDATA_VALIDATION_FAILED,
      (payload) => {
        const errorMessage = `实时数据验证失败：${JSON.stringify(payload)}`;
        message.error(errorMessage);
      }
    );*/

    // 从public目录获取配置文件
    fetch('/config.json')
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        setConfig(data); // 设置配置数据
      })
      .catch(error => {
        console.error('Error fetching config:', error);
      });

    /*return () => {
      subscription.unsubscribe();
    };*/
  }, []);

  useEffect(() => {
    console.log('Dashboard装载完成');

    /*const handleSessionUpdate = (sessionId: number) => {
      console.log('[Dashboard] Session updated:', sessionId);
      setSessionId(sessionId);
      setChatVisible(true); // 当获取到 sessionId 时自动显示聊天窗口
    };

    const handleToggleChat = (visible: boolean) => {
      console.log('[Dashboard] Toggle chat window:', visible);
      setChatVisible(visible);
      // 如果要显示聊天窗口，但还没有 sessionId，就先获取默认会话
      if (visible && !sessionId) {
        EventBus.emit(ChatEvents.Types.GET_DEFAULT_SESSION, undefined);
      }
    };

    // 订阅事件
    const sessionSubscription = EventBus.on(ChatEvents.Types.SESSION_UPDATED, handleSessionUpdate);
    const toggleSubscription = EventBus.on(ChatEvents.Types.TOGGLE_CHAT_WINDOW, handleToggleChat);

    // 组件挂载时主动获取会话
    EventBus.emit(ChatEvents.Types.GET_DEFAULT_SESSION, undefined);

    return () => {
      sessionSubscription.unsubscribe();
      toggleSubscription.unsubscribe();
    };*/
  }, []);

  //比价两个Symbol是否相同
  const isSymbolEqual = (symbol1: Symbol, symbol2: Symbol) => {
    console.log('比较 symbol1=', symbol1, ' symbol2=', symbol2);
    return symbol1.market === symbol2.market && symbol1.exchange === symbol2.exchange && symbol1.code === symbol2.code;
  };

  // 判断画线周期是否适合当前周期
  const isLinePeriodAcceptable = (line: DrawingLine, currentPeriod: KLineInterval) => {
    // 按照 KLineInterval 的顺序比较
    const order = [KLineInterval.MIN1, KLineInterval.MIN5, KLineInterval.MIN15, KLineInterval.MIN30, KLineInterval.HOUR1, KLineInterval.HOUR4, KLineInterval.DAY1, KLineInterval.WEEK1];
    const lineIndex = order.indexOf(line.interval);
    const currentIndex = order.indexOf(currentPeriod);
    return lineIndex >= currentIndex;
  };

  // 监听K线数据准备就绪事件
  useEffect(() => {
    const subscription = EventBus.on(
      MarketEvents.Types.KLINES_READY,
      (payload: MarketEvents.KLinesReady) => {
        console.log('[Dashboard] 收到K线数据就绪事件，数据长度:', payload.kline.data.length, '，设置新的kline ID');
        setIsLoading(false);

        //新数据接收，重置ID
        setKline(payload.kline);

        // 发布事件调取该品种的所有画线
        //EventBus.emit(ChartEvents.Types.GET_DRAWING_LINES, { symbol: payload.kline.symbol });
        // 等到当前周期等数据准备好，才调用

        if (!isSymbolEqual(currentSymbol, payload.kline.symbol)) {
          console.log("[Dashboard] 检测到品种变化，设置新的currentSymbol:", payload.kline.symbol);
          setCurrentSymbol(payload.kline.symbol);
        }
        if (currentPeriod !== payload.kline.period) {
          console.log("[Dashboard] 检测到周期变化，设置新的currentPeriod:", payload.kline.period);
          setCurrentPeriod(payload.kline.period);
        }

        // 广播事件，新品种切换已确认
        EventBus.emit(MarketEvents.Types.SYMBOL_CHANGED, { symbol: payload.kline.symbol });

      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [currentSymbol, currentPeriod]);

  // 监听显示股票池管理面板事件
  useEffect(() => {
    const subscription = EventBus.on(
      MarketEvents.Types.SHOW_STOCK_POOL_MANAGER,
      (payload: MarketEvents.ShowStockPoolManagerPayload) => {
        console.log('[Dashboard] 收到显示股票池管理面板事件:', payload);
        setStockPoolVisible(payload.visible);
        if (payload.currentListName) {
          setStockPoolCurrentList(payload.currentListName);
        }
        setStockPoolTag(payload.tag);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  useEffect(() => {
    console.log('>>>>> CURRENT PERIOD 改变：', currentPeriod);
  }, [currentPeriod]);


  // 监听绘线数据准备就绪事件
  /*useEffect(() => {
    const subscription = EventBus.on(
      ChartEvents.Types.DRAWING_LINES_READY,
      (payload: ChartEvents.DrawingLinesReadyPayload) => {
        console.log('[Dashboard] 收到绘线数据就绪事件，数据长度:', payload.drawingLines.length);

        // 首先，从画线数据中获取适合于当前周期的所有画线，要求只能获取大于等于当前周期的画线
        const currentDrawingLines = payload.drawingLines.filter(line => isLinePeriodAcceptable(line, currentPeriod));

        // 重新发布画线数据，包含过滤后的画线
        EventBus.emit(ChartEvents.Types.DRAWING_LINES_READY, {
          symbol: payload.symbol,
          drawingLines: currentDrawingLines
        });
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [currentPeriod]);*/

  // 处理用户主动点击登出按钮
  const handleLogout = () => {
    removeToken();
    message.success('已退出登录');
    navigate('/login');
  };

  // 监听登出事件并且完成登出操作
  // 这里处理的是由HttpDispatcher等其他组件触发的登出事件
  useEffect(() => {
    const subscription = EventBus.on(UserEvents.Types.LOGOUT, () => {
      console.log('[Dashboard] 收到登出事件，准备跳转到登录页面');
      // 使用setTimeout确保其他操作有机会完成
      setTimeout(() => {
        navigate('/login');
      }, 300);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [navigate]);

  const handleThemeChange = (themeKey: ThemeType) => {
    changeTheme(themeKey);
    message.success('主题切换成功');
    setIsThemeModalVisible(false);
  };

  const userMenuItems = {
    items: [
      {
        key: 'userInfo',
        icon: <UserOutlined />,
        label: '用户信息',
        onClick: () => setIsUserInfoModalVisible(true)
      },
      {
        key: 'theme',
        icon: <SkinOutlined />,
        label: '主题设置',
        onClick: () => setIsThemeModalVisible(true)
      },
      {
        key: 'divider',
        type: 'divider' as const
      },
      {
        key: 'logout',
        icon: <LogoutOutlined />,
        label: '退出登录',
        onClick: handleLogout
      }
    ]
  };

  // 渲染内容区域
  const renderContent = () => {
    switch (selectedKey) {
      case 'klinechart':
        return <DashboardContent pageType="klinechart" />;
      case 'strategy':
        return <StrategyPanel />;
      case 'chat':
        return <ChatPanel />;
      case 'settings':
        return <SettingsPanel />;
      case 'stockpool':
        return <StockPoolWidget tag={1} />;
      case 'live':
        return <LiveStrategyPanel />;
      default:
        return <DashboardContent pageType="klinechart" />;
    }
  };

  return (
    <>
      <ProLayout
        logo={false}
        title={config?.title || "QuantQuart"}
        token={{
          header: { heightLayoutHeader: 40 }, // 使用 heightLayoutHeader token
        }}
        layout="mix"
        siderWidth={140}
        selectedKeys={[selectedKey]}
        contentStyle={{ padding: 3, margin: 0, minWidth: '100%' }}
        actionsRender={() => [
          <Dropdown menu={userMenuItems} placement="bottomRight">
            <Space>
              <Avatar
                size={24}
                src={userInfo?.avatar ? `/uploads/avatars/${userInfo.avatar}` : undefined}
                icon={!userInfo?.avatar && <UserOutlined />}
              />
              <span style={{ fontSize: '14px' }}>{userInfo?.username || '未知用户'}</span>
            </Space>
          </Dropdown>
        ]}
        menuItemRender={(item, dom) => (
          <div onClick={() => {
            if (item.key) {
              setSelectedKey(item.key);
            }
          }}>
            {dom}
          </div>
        )}
        menuExtraRender={() => (
          <div style={{ padding: '24px 0' }}>
            {/* 注释掉聊天窗口渲染
            {sessionId && config?.enableChat && (
              <div style={{ display: chatVisible ? 'block' : 'none' }}>
                <ChatWindow />
              </div>
            )}
            */}
          </div>
        )}
        route={{
          path: '/',
          routes: [
            {
              path: '/klinechart',
              name: '走势分析',
              icon: <SlidersOutlined />,
              key: 'klinechart',
            },
            {
              path: '/strategy',
              name: '策略池',
              icon: <RobotOutlined />,
              key: 'strategy',
            },
            {
              path: '/chat',
              name: '聊天交流',
              icon: <FundOutlined />,
              key: 'chat',
            },
            {
              path: '/settings',
              name: '系统设置',
              icon: <SettingOutlined />,
              key: 'settings',
            },
            {
              path: '/stockpool',
              name: '股票池',
              icon: <FundOutlined />,
              key: 'stockpool',
            },
            {
              path: '/live',
              name: '实盘管理',
              icon: <RocketOutlined />,
              key: 'live',
            },
          ],
        }}
      >
        {renderContent()}
      </ProLayout>

      <UserInfoModal
        open={isUserInfoModalVisible}
        onCancel={() => setIsUserInfoModalVisible(false)}
        userInfo={userInfo || {}}
      />

      <Modal
        title="主题设置"
        open={isThemeModalVisible}
        onCancel={() => setIsThemeModalVisible(false)}
        footer={null}
      >
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '16px' }}>
          {[
            { key: 'light-default' as ThemeType, name: '默认亮色', color: '#1677ff' },
            { key: 'dark-default' as ThemeType, name: '默认暗色', color: '#141414' },
            { key: 'light-blue' as ThemeType, name: '商务亮色', color: '#0958d9' },
            { key: 'dark-blue' as ThemeType, name: '商务暗色', color: '#0a1426' },
            { key: 'light-tech' as ThemeType, name: '科技亮色', color: '#165dff' },
            { key: 'dark-tech' as ThemeType, name: '科技暗色', color: '#17171a' },
          ].map(theme => (
            <div
              key={theme.key}
              onClick={() => handleThemeChange(theme.key)}
              style={{
                padding: '16px',
                border: `2px solid ${selectedTheme === theme.key ? theme.color : '#d9d9d9'}`,
                borderRadius: '8px',
                cursor: 'pointer',
                backgroundColor: theme.color,
                color: '#fff',
                textAlign: 'center',
              }}
            >
              {theme.name}
            </div>
          ))}
        </div>
      </Modal>

      {/* 股票池管理面板抽屉 */}
      <Drawer
        title={`股票池管理 ${stockPoolCurrentList ? `- ${stockPoolCurrentList}` : ''}`}
        placement="right"
        width={800}
        onClose={() => setStockPoolVisible(false)}
        open={stockPoolVisible}
        bodyStyle={{ padding: 0 }}
      >
        <StockPoolWidget tag={stockPoolTag} />
      </Drawer>
    </>
  );
};

export default Dashboard;