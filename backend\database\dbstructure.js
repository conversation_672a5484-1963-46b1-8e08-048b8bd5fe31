const { DataTypes } = require('sequelize');

module.exports = {
  users: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 20],
        isAlphanumeric: true
      }
    },
    password: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [8, 100]
      }
    },
    mobilephone: {
      type: DataTypes.STRING(15),
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended', 'pending'),
      defaultValue: 'pending'
    },
    role: {
      type: DataTypes.ENUM('admin', 'user'),
      defaultValue: 'user'
    },
    createdat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    lastLogin: {
      type: DataTypes.DATE,
      allowNull: true
    },
    avatar: {
      type: DataTypes.STRING,
      defaultValue: 'default-avatar.png'
    }
  },

  strategies: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('morphological', 'indicator', 'fundamental'),
      allowNull: false
    },
    config: {
      type: DataTypes.JSON,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'active'
    },
    createdat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  },

  backtests: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    strategyid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'strategies',
        key: 'id'
      }
    },
    startdate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    enddate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    result: {
      type: DataTypes.JSON,
      allowNull: false
    },
    createdat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  },

  trades: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    strategyid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'strategies',
        key: 'id'
      }
    },
    symbol: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('buy', 'sell'),
      allowNull: false
    },
    quantity: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: false
    },
    price: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: false
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('pending', 'executed', 'canceled'),
      defaultValue: 'pending'
    }
  },

  messages: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    senderid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    receiverid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('text', 'image', 'file'),
      defaultValue: 'text'
    },
    status: {
      type: DataTypes.ENUM('unread', 'read'),
      defaultValue: 'unread'
    },
    createdat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  },

  chatsessions: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    type: {
      type: DataTypes.ENUM('private', 'group', 'default'),
      allowNull: false
    },
    createdat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  },

  chatsessionmembers: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    sessionid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'chatsessions',
        key: 'id'
      }
    },
    userid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    role: {
      type: DataTypes.ENUM('owner', 'admin', 'member'),
      defaultValue: 'member'
    },
    lastreadat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  },

  chatMessages: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    sessionid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'chatSessions',
        key: 'id'
      }
    },
    senderid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    type: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    fileurl: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    filename: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    filesize: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    filetype: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    isdeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    createdat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  },

  chatMessageStatus: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    messageid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'chatMessages',
        key: 'id'
      }
    },
    userid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    isread: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    readat: {
      type: DataTypes.DATE,
      allowNull: true
    }
  },

  shapes: {
    shapeId: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    klineData: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'K线OHLC数据数组，JSON格式：[{time, open, high, low, close}, ...]'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  },

  shape_details: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    shapeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'shapes',
        key: 'shapeId'
      }
    },
    indicatorType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    indicatorParam: {
      type: DataTypes.JSON,
      allowNull: false
    },
    lineName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    values: {
      type: DataTypes.JSON,
      allowNull: false
    },
    weight: {
      type: DataTypes.FLOAT,
      allowNull: false
    }
  },

  // 画线
  drawing_lines: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    symbol: {
      type: DataTypes.STRING(10), // 根据 @shared_types 中的定义
      allowNull: false
    },
    interval: {
      type: DataTypes.STRING, // 根据 @shared_types 中的定义
      allowNull: false
    },
    overlays: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    }
  },

  // 形态K线数据表
  shape_klines: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: 'K线数据ID'
    },
    shapeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'shapes',
        key: 'shapeId'
      },
      comment: '关联的形态ID'
    },
    klineData: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: 'K线OHLC数据数组，JSON格式：[{time, open, high, low, close}, ...]'
    }
  },

  // 指标列表
  indicator_lists: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    indicators: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: {}
    }
  },

  // 新增：策略所有权表
  strategy_ownership: {
    strategy_id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
      comment: '策略的唯一标识符 (UUID), 对应 Python 服务中的 strategy_id'
    },
    owner_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users', // 确保与用户表名一致
        key: 'id'
      },
      comment: '策略拥有者的用户ID'
    },
    shared_with_ids: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '共享给的用户ID列表 (逗号分隔)'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW, // 使用 Sequelize 的 NOW
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false
    }
  },

  // 新增：股票池主表 (存储用户自选池和系统预定义池)
  pools: {
    pool_id: {
      type: DataTypes.BIGINT, // 对应 BIGSERIAL
      primaryKey: true,
      autoIncrement: true,
      comment: '池子唯一ID'
    },
    user_id: {
      type: DataTypes.INTEGER, // 假设 users.id 是 INTEGER
      allowNull: true, // 允许 NULL，表示系统池
      comment: '关联的用户ID，NULL 表示系统池'
      // 实际的外键约束建议通过 Migrations 或直接 SQL 创建
    },
    name: {
      type: DataTypes.STRING(128),
      allowNull: false,
      comment: '池子名称或内部标识符'
      // 唯一性约束需单独创建
    },
    description: {
      type: DataTypes.STRING(128),
      allowNull: true,
      defaultValue: '',
      comment: '池子描述'
    },
    is_public: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否公开 (主要对用户池有效)'
    },
    // 新增: JSON字段存储所有的股票列表
    symbols_json: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '以JSON格式存储的股票列表。格式为数组，例如: [{"symbol": "NASDAQ.STOCK.AAPL"}]'
    }
    // 移除 created_at 和 updated_at 字段
    // 注意：查询用户池列表的优化索引需要通过 migrations 或直接 SQL 设置
  },

  // 新增：系统池元数据表
  system_pool_metadata: {
    pool_id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      allowNull: false,
      references: {
        model: 'pools',
        key: 'pool_id'
      },
      comment: '关联的系统池ID (pools.user_id IS NULL)'
      // 注意：外键 ON DELETE CASCADE 需要通过 migrations 或直接 SQL 设置
      // 应用层需保证引用的 pool_id 对应的 user_id 为 NULL
    },
    market_type: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: '市场类型 (如 STOCK, STOCK_US, CRYPTO, FOREX)'
    },
    region: {
      type: DataTypes.STRING(10),
      allowNull: true,
      comment: '地区代码 (如 CN, US, HK)'
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '板块/指数类别 (如 科技, 金融)'
    },
    display_name: {
      type: DataTypes.STRING(128),
      allowNull: false,
      comment: '前端显示名称'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '描述信息'
    },
    display_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: '显示排序'
    },
    is_featured: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: '是否精选推荐'
    },
    updated_at: {
      type: DataTypes.DATE, // 对应 TIMESTAMPTZ
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '元数据更新时间'
    }
    // 注意：发现索引需要通过 migrations 或直接 SQL 设置
    // CREATE INDEX idx_system_pool_meta_discovery ON system_pool_metadata(market_type, region, category, display_order);
  },

  // 新增：股票池分类表
  pool_categories: {
    category_id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '分类唯一ID'
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '分类名称，如"A股"、"美股"、"量化策略"'
    },
    type: {
      type: DataTypes.ENUM('system', 'user'),
      allowNull: false,
      comment: '分类类型：system(系统) 或 user(用户)'
    },
    user_id: {
      type: DataTypes.INTEGER, // 假设 users.id 是 INTEGER
      allowNull: true, // 允许 NULL，适用于系统分类
      comment: '所属用户ID（当type=user时有值）'
      // 实际的外键约束建议通过 Migrations 或直接 SQL 创建
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '显示顺序'
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '可选描述'
    },
    icon: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '可选图标标识'
    },
    // 新增JSON字段，存储池列表
    pools_json: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '以JSON格式存储的池列表。格式为数组，例如: [{"pool_id": 1, "pool_name": "沪深300"}]'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    }
    // 注意：查询优化索引需要通过 migrations 或直接 SQL 设置
  },

  // 新增：实盘策略表
  live_strategies: {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
      comment: '实盘策略ID (UUID)'
    },
    userid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '用户ID'
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '用户名'
    },
    strategyid: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '原策略ID'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '策略名称'
    },
    status: {
      type: DataTypes.ENUM('running', 'stopped', 'error', 'deploying'),
      allowNull: false,
      defaultValue: 'stopped',
      comment: '策略状态'
    },
    accountid: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '交易账户ID'
    },
    initialcapital: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: '初始资金'
    },
    commissionrate: {
      type: DataTypes.DECIMAL(10, 6),
      allowNull: false,
      defaultValue: 0.0003,
      comment: '手续费率'
    },
    risksettings: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '风险设置 JSON'
    },
    createdat: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    starttime: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '启动时间'
    },
    lastupdatetime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '最后更新时间'
    },
    yaml: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '策略YAML内容'
    },
    strategytype: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: '策略类型'
    },
    // 新增：数据级别（如 day, 1m, 5m 等）
    timeframe: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '数据级别（如 day, 1m, 5m 等）'
    }
  },
  
  // 新增：组合策略关联表
  group_strategies: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '关联ID'
    },
    groupid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'groups',
        key: 'groupid'
      },
      comment: '组合ID'
    },
    strategyid: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'live_strategies',
        key: 'id'
      },
      comment: '策略ID'
    },
    deployedat: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '部署时间'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active',
      comment: '在组合中的状态'
    },
    weight: {
      type: DataTypes.DECIMAL(5, 4),
      allowNull: true,
      defaultValue: 1.0,
      comment: '策略权重'
    }
  },

  // 新增：用户交易通道配置表
  user_trading_configs: {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '配置ID'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '用户ID'
    },
    channelType: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '通道类型: openctp, miniQMT 等'
    },
    configName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '用户自定义配置名称'
    },
    broker: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '经纪商代码，可为空'
    },
    username: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '交易账户'
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '密码（加密存储）'
    },
    appid: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '应用ID，可为空'
    },
    authcode: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '认证码，可为空'
    },
    frontAddress: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '前置地址'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否激活'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  },

  
};
