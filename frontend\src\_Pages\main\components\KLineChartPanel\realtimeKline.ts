import { io, Socket } from 'socket.io-client';
import { EventBus } from '@/events/eventBus';
import { MarketEvents, RealtimeEvents } from '@/events/events';
import { KLineData, Symbol, KLineInterval, KLine } from '@/shared_types/market';
import { Time } from 'lightweight-charts';
import { jotaiStore, klineAtom } from '@/store/state';
import axios from 'axios';

/**
 * 实时K线数据处理类
 * 负责与后端的WebSocket通信、数据接收和验证
 */
class RealtimeKline {
  private socket: Socket | null = null;
  private isConnected: boolean = false;
  private currentSymbol: Symbol | null = null;
  private currentPeriod: KLineInterval | null = null;
  private cachedKline: KLine | null = null;
  private wsEndpoint: string;
  private wsPort: number | null = null;
  private authToken: string | null = null;

  constructor(wsEndpoint: string = '') {
    this.wsEndpoint = wsEndpoint;
    console.log('[RealtimeKline] 初始化实例，WebSocket地址:', wsEndpoint || '当前站点');

    // 获取认证token
    this.authToken = localStorage.getItem('token');
  }

  /**
   * 从端口池获取可用的WebSocket端口
   * @returns {Promise<number|null>} 返回可用的端口号，如果获取失败则返回null
   */
  private async getWebSocketPort(): Promise<number | null> {
    try {
      console.log('[RealtimeKline] 正在从端口池获取WebSocket端口...');

      if (!this.authToken) {
        console.error('[RealtimeKline] 获取WebSocket端口失败: 未找到认证token');
        EventBus.emit(RealtimeEvents.Types.REALTIME_ERROR, {
          message: '获取WebSocket端口失败: 未找到认证token'
        });
        return null;
      }

      const response = await axios.post('/api/socket/get_port', {}, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success && response.data.port) {
        const port = response.data.port;
        console.log(`[RealtimeKline] 成功获取WebSocket端口: ${port}`);
        return port;
      } else {
        console.error('[RealtimeKline] 获取WebSocket端口失败:', response.data.message || '未知错误');
        EventBus.emit(RealtimeEvents.Types.REALTIME_ERROR, {
          message: `获取WebSocket端口失败: ${response.data.message || '未知错误'}`
        });
        return null;
      }
    } catch (error) {
      console.error('[RealtimeKline] 获取WebSocket端口出错:', error);
      EventBus.emit(RealtimeEvents.Types.REALTIME_ERROR, {
        message: `获取WebSocket端口出错: ${error instanceof Error ? error.message : '未知错误'}`
      });
      return null;
    }
  }

  /**
   * 释放WebSocket端口
   * @returns {Promise<boolean>} 返回是否成功释放端口
   */
  private async releaseWebSocketPort(): Promise<boolean> {
    if (!this.wsPort) {
      console.log('[RealtimeKline] 没有WebSocket端口需要释放');
      return true;
    }

    try {
      console.log(`[RealtimeKline] 正在释放WebSocket端口: ${this.wsPort}...`);

      if (!this.authToken) {
        console.error('[RealtimeKline] 释放WebSocket端口失败: 未找到认证token');
        return false;
      }

      const response = await axios.post('/api/socket/release_port',
        { port: this.wsPort },
        {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        console.log(`[RealtimeKline] 成功释放WebSocket端口: ${this.wsPort}`);
        this.wsPort = null;
        return true;
      } else {
        console.error('[RealtimeKline] 释放WebSocket端口失败:', response.data.message || '未知错误');
        return false;
      }
    } catch (error) {
      console.error('[RealtimeKline] 释放WebSocket端口出错:', error);
      return false;
    }
  }

  /**
   * 订阅 klineAtom 的变化
   */
  private subscribeToKlineAtom(): void {
    const unsubscribe = jotaiStore.sub(klineAtom, () => {
      const newKline = jotaiStore.get(klineAtom);
      console.log('[RealtimeKline] 检测到 klineAtom 变化:', newKline);

      // 更新缓存的 K线数据
      this.cachedKline = newKline;
    });

    // 在类销毁时取消订阅（如果需要）
    // this.unsubscribeKlineAtom = unsubscribe;
  }

  /**
   * 初始化WebSocket连接和事件监听
   */
  public async initialize(): Promise<void> {
    console.log('[RealtimeKline] 开始初始化...');

    // 设置事件监听
    this.setupEventListeners();

    // 获取当前的K线数据作为初始缓存
    this.cachedKline = jotaiStore.get(klineAtom);
    if (this.cachedKline) {
      console.log('[RealtimeKline] 初始缓存K线数据:',
        this.cachedKline.symbol.code,
        this.cachedKline.period,
        `(${this.cachedKline.data.length}条数据)`);
    } else {
      console.log('[RealtimeKline] 无初始缓存K线数据');
    }

    // 订阅 klineAtom 的变化
    this.subscribeToKlineAtom();

    // 初始化WebSocket连接（包括获取端口）
    try {
      await this.initSocketConnection();
      console.log('[RealtimeKline] WebSocket连接初始化成功');
    } catch (error) {
      console.error('[RealtimeKline] WebSocket连接初始化失败:', error);
      EventBus.emit(RealtimeEvents.Types.REALTIME_ERROR, {
        message: `WebSocket连接初始化失败: ${error instanceof Error ? error.message : '未知错误'}`
      });
    }

    console.log('[RealtimeKline] 初始化完成');
  }

  /**
   * 初始化Socket.io连接
   */
  private async initSocketConnection(): Promise<void> {
    try {
      // 如果已经有连接，先确保断开
      if (this.socket) {
        console.log('[RealtimeKline] 已存在Socket连接，先断开:', this.socket.id);
        this.socket.disconnect();
        this.socket = null;
      }

      // 释放之前的端口（如果有）
      if (this.wsPort) {
        await this.releaseWebSocketPort();
      }

      // 从端口池获取可用的WebSocket端口
      this.wsPort = await this.getWebSocketPort();

      if (!this.wsPort) {
        console.error('[RealtimeKline] 无法获取WebSocket端口，使用默认连接');
        // 使用默认连接（不指定端口）
        this.socket = io(`${this.wsEndpoint}/realtime`, {
          path: '/socket.io',  // 确保与后端路径一致
          reconnection: true,
          reconnectionDelay: 1000,
          reconnectionAttempts: 5,
          transports: ['polling', 'websocket'], // 明确指定传输方式
          forceNew: true, // 强制创建新连接
          multiplex: false // 禁用多路复用
        });

        console.log('[RealtimeKline] Socket.IO配置(默认):', {
          url: `${this.wsEndpoint}/realtime`,
          path: '/socket.io',
          forceNew: true,
          multiplex: false
        });
      } else {
        // 提取主机名（不包含协议和端口）
        let host = this.wsEndpoint;

        // 移除协议前缀
        if (host.startsWith('http://')) {
          host = host.substring(7);
        } else if (host.startsWith('https://')) {
          host = host.substring(8);
        }

        // 移除端口号和路径
        if (host.includes(':')) {
          host = host.substring(0, host.indexOf(':'));
        }
        if (host.includes('/')) {
          host = host.substring(0, host.indexOf('/'));
        }

        // 如果host为空，使用当前站点的主机名
        if (!host) {
          host = window.location.hostname;
        }

        // 使用获取到的端口创建连接
        const socketUrl = `http://${host}:${this.wsPort}/realtime`;
        console.log('[RealtimeKline] 正在创建新的WebSocket连接:', socketUrl);

        this.socket = io(socketUrl, {
          path: '/socket.io',  // 确保与后端路径一致
          reconnection: true,
          reconnectionDelay: 1000,
          reconnectionAttempts: 5,
          transports: ['polling', 'websocket'], // 明确指定传输方式
          forceNew: true, // 强制创建新连接
          multiplex: false // 禁用多路复用
        });

        // 详细记录连接过程
        console.log('[RealtimeKline] Socket.IO配置(端口池):', {
          url: socketUrl,
          port: this.wsPort,
          path: '/socket.io',
          forceNew: true,
          multiplex: false
        });
      }

      // 连接事件
      this.socket.on('connect', () => {
        console.log('[RealtimeKline] WebSocket连接成功! ID:', this.socket?.id);
        this.isConnected = true;

        // 如果已经有品种和周期信息，立即发送订阅请求
        if (this.currentSymbol && this.currentPeriod) {
          console.log('[RealtimeKline] 连接成功，发送订阅请求:',
            this.currentSymbol.code, this.currentPeriod);

          const subData = {
            symbol: this.currentSymbol,
            period: this.currentPeriod
          };

          console.log('[RealtimeKline] 发送subscribe数据:', JSON.stringify(subData));
          this.socket?.emit('subscribe', subData);
        }
      });

      // 监听连接状态
      this.socket.io.on("packet", (packet) => {
        console.log('[RealtimeKline] Socket.IO数据包:', packet.type, packet.data);
      });

      // 断开连接事件
      this.socket.on('disconnect', (reason) => {
        console.log('[RealtimeKline] WebSocket连接断开，原因:', reason);
        this.isConnected = false;
      });

      // 重连事件
      this.socket.on('reconnect_attempt', (attempt) => {
        console.log(`[RealtimeKline] 尝试重新连接 (${attempt})...`);
      });

      this.socket.on('reconnect', (attempt) => {
        console.log(`[RealtimeKline] 重新连接成功，尝试次数: ${attempt}`);
      });

      // 错误事件
      this.socket.on('error', (error) => {
        console.error('[RealtimeKline] WebSocket错误:', error);
        // 发布错误事件
        EventBus.emit(RealtimeEvents.Types.REALTIME_ERROR, {
          message: 'WebSocket返回错误'
        });
      });

      // 连接错误
      this.socket.on('connect_error', (error) => {
        console.error('[RealtimeKline] WebSocket连接错误:', error);

        // 发送错误事件到事件总线
        EventBus.emit(RealtimeEvents.Types.REALTIME_ERROR, {
          message: `WebSocket连接失败: ${error.message || '未知错误'}`
        });
      });

      // 订阅成功事件
      this.socket.on('subscribed', (data) => {
        console.log('[RealtimeKline] 订阅成功:', data.symbol?.code, data.period);
      });

      // 取消订阅成功事件
      this.socket.on('unsubscribed', () => {
        console.log('[RealtimeKline] 取消订阅成功');
      });

      // 接收实时K线数据
      this.socket.on('realtimeKline', (data) => {
        console.log(`[RealtimeKline] 收到实时数据:`, data);
        this.handleRealtimeData(data);
      });
    } catch (error) {
      console.error('[RealtimeKline] 初始化Socket连接失败:', error);
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    console.log('[RealtimeKline] 设置事件监听...');

    // 监听K线数据准备就绪事件
    EventBus.on(
      MarketEvents.Types.KLINES_READY,
      (payload: MarketEvents.KLinesReady) => {
        console.log('[RealtimeKline] 收到K线数据更新事件:', {
          symbol: payload.kline?.symbol?.code,
          market: payload.kline?.symbol?.market,
          period: payload.kline?.period,
          dataLength: payload.kline?.data?.length || 0,
          firstTime: payload.kline?.data?.length > 0 ? payload.kline.data[0].time : 'none',
          lastTime: payload.kline?.data?.length > 0 ? payload.kline.data[payload.kline.data.length - 1].time : 'none'
        });

        // 更新当前缓存的K线数据
        this.cachedKline = payload.kline;

        // 检查品种或周期是否变化
        const symbolChanged = !this.currentSymbol ||
          this.currentSymbol.code !== payload.kline.symbol.code ||
          this.currentSymbol.market !== payload.kline.symbol.market;

        const periodChanged = !this.currentPeriod ||
          this.currentPeriod !== payload.kline.period;

        console.log('[RealtimeKline] 检查品种和周期变化:', {
          currentSymbol: this.currentSymbol?.code,
          newSymbol: payload.kline.symbol.code,
          symbolChanged,
          currentPeriod: this.currentPeriod,
          newPeriod: payload.kline.period,
          periodChanged
        });

        if (symbolChanged || periodChanged) {
          console.log('[RealtimeKline] 检测到品种或周期变化，更新订阅');
          // 更新订阅信息
          this.subscribeSymbol(payload.kline.symbol, payload.kline.period);
        } else {
          console.log('[RealtimeKline] 品种和周期未变化，保持当前订阅');
        }
      }
    );

    // 监听品种变化事件
    EventBus.on(
      MarketEvents.Types.SYMBOL_CHANGED,
      (payload: MarketEvents.SymbolChangedPayload) => {
        console.log('[RealtimeKline] 收到品种变化事件:', {
          code: payload.symbol?.code,
          market: payload.symbol?.market,
          isConnected: this.isConnected
        });

        const symbolChanged = !this.currentSymbol ||
          this.currentSymbol.code !== payload.symbol.code ||
          this.currentSymbol.market !== payload.symbol.market;

        console.log('[RealtimeKline] 品种变化检查:', {
          currentSymbol: this.currentSymbol?.code,
          newSymbol: payload.symbol.code,
          symbolChanged,
          currentPeriod: this.currentPeriod
        });

        if (this.currentPeriod && symbolChanged) {
          console.log('[RealtimeKline] 品种已变化，更新订阅');
          // 更新订阅品种
          this.subscribeSymbol(payload.symbol, this.currentPeriod);
        }
      }
    );

    // 监听重新连接事件
    EventBus.on(
      RealtimeEvents.Types.REALTIME_RECONNECT,
      () => {
        console.log('[RealtimeKline] 收到重新连接请求');

        // 如果连接已断开，尝试重新连接
        if (!this.isConnected && this.socket) {
          console.log('[RealtimeKline] 尝试重新连接WebSocket...');
          this.socket.connect();
        }
        // 如果已连接但可能存在订阅问题，尝试重新订阅
        else if (this.isConnected && this.currentSymbol && this.currentPeriod) {
          console.log('[RealtimeKline] 已连接，尝试重新订阅:',
            this.currentSymbol.code, this.currentPeriod);
          this.subscribeSymbol(this.currentSymbol, this.currentPeriod);
        }
        // 如果socket为空，重新初始化连接
        else if (!this.socket) {
          console.log('[RealtimeKline] Socket为空，重新初始化连接');
          this.initSocketConnection();

          // 延迟一点时间等待连接建立后再订阅
          if (this.currentSymbol && this.currentPeriod) {
            setTimeout(() => {
              if (this.isConnected) {
                this.subscribeSymbol(this.currentSymbol!, this.currentPeriod!);
              }
            }, 1000);
          }
        }
      }
    );

    console.log('[RealtimeKline] 事件监听设置完成');
  }

  /**
   * 订阅品种
   */
  private async subscribeSymbol(symbol: Symbol, period: KLineInterval): Promise<void> {
    // 保存新的订阅请求参数，用于重连后使用
    const newSymbol = symbol;
    const newPeriod = period;

    // 发送开始调用实时数据事件
    EventBus.emit(RealtimeEvents.Types.REALTIME_DATA_START, undefined);

    // 如果当前有活跃的socket连接
    if (this.socket && this.isConnected) {
      // 如果有旧的订阅，先发送取消订阅请求
      if (this.currentSymbol && this.currentPeriod) {
        console.log('[RealtimeKline] 取消旧订阅:', this.currentSymbol.code, this.currentPeriod);

        const unsubData = {
          symbol: this.currentSymbol,
          period: this.currentPeriod
        };
        console.log('[RealtimeKline] 发送unsubscribe数据:', JSON.stringify(unsubData));

        this.socket.emit('unsubscribe', unsubData);
      }

      // 更新当前品种和周期
      this.currentSymbol = newSymbol;
      this.currentPeriod = newPeriod;

      // 发送新的订阅请求
      console.log('[RealtimeKline] 发送新订阅:', newSymbol.code, newPeriod);

      const subData = {
        symbol: newSymbol,
        period: newPeriod
      };
      console.log('[RealtimeKline] 发送subscribe数据:', JSON.stringify(subData));

      this.socket.emit('subscribe', subData);
    } else {
      // 如果没有活跃的连接，需要初始化连接
      console.log('[RealtimeKline] 没有活跃的连接，初始化新连接');

      // 更新当前品种和周期，以便在连接成功后自动订阅
      this.currentSymbol = newSymbol;
      this.currentPeriod = newPeriod;

      // 初始化连接
      await this.initSocketConnection();

      // 由于initSocketConnection中的connect事件会自动使用currentSymbol和currentPeriod进行订阅
      // 不需要在这里手动订阅
      console.log('[RealtimeKline] 初始化新连接，新订阅将在连接成功后自动发送:', newSymbol.code, newPeriod);
    }
  }

  /**
   * 处理实时数据
   */
  private handleRealtimeData(data: {
    symbol: Symbol;
    interval: KLineInterval;
    klines: KLineData[];
  }): void {
    console.log(`[RealtimeKline] 开始处理实时数据: ${data.symbol.code} ${data.interval}`);

    // 检查是否匹配当前订阅
    if (
      !this.currentSymbol ||
      !this.currentPeriod ||
      data.symbol.code !== this.currentSymbol.code ||
      data.symbol.market !== this.currentSymbol.market ||
      data.interval !== this.currentPeriod
    ) {
      console.log('[RealtimeKline] 收到非当前订阅的数据，忽略');
      EventBus.emit(RealtimeEvents.Types.REALTIME_DATA_END, undefined);
      return;
    }

    // 确保有缓存数据和接收数据
    if (!this.cachedKline || !data.klines || data.klines.length === 0) {
      console.log('[RealtimeKline] 无缓存数据或接收数据为空，忽略');
      EventBus.emit(RealtimeEvents.Types.REALTIME_DATA_END, undefined);
      return;
    }

    // 验证数据并合并
    // 原则：旧数据是不会错的，最多只能更新最后一条
    let processedData: KLineData[] = [];
    let newDataIndex: number = 0;
    [processedData, newDataIndex] = this.validateAndProcessData(data.klines, this.currentPeriod);

    if (newDataIndex === -1) {
      console.log('[RealtimeKline] 验证后无有效数据，忽略');
      EventBus.emit(RealtimeEvents.Types.REALTIME_DATA_END, undefined);
      return;
    }

    console.log(`[RealtimeKline] 验证后有效数据: ${processedData.length}条`);

    // 构建更新事件数据
    const updatePayload: MarketEvents.KLinesUpdated = {
      kline: {
        id: this.cachedKline.id,
        symbol: this.cachedKline.symbol,
        period: this.cachedKline.period,
        data: processedData,
      },
      newDataIndex: newDataIndex
    };

    // 发布更新事件
    console.log('[RealtimeKline] 发布K线更新事件，数据长度:', processedData.length);
    EventBus.emit(MarketEvents.Types.KLINES_UPDATED, updatePayload);
    EventBus.emit(RealtimeEvents.Types.REALTIME_DATA_END, undefined);
  }

  private getCurrentTimezoneOffset(): number {
    // 获取当前时间的时区偏移量（单位：分钟）
    const offsetInMinutes = new Date().getTimezoneOffset();
    // 转换为小时并返回
    return -offsetInMinutes / 60;
  }

  /**
   * 调整实时数据里面的夜盘时间
   */
  private adjustNightTradingTime(klines: KLineData[]): KLineData[] {
    const timezoneOffset = this.getCurrentTimezoneOffset() * 3600 * 1000;

    return klines.map(kline => {
      // 转换为北京时间（把时间戳加上时区偏移再转化为UTC，就等于北京时间）
      const beijingTime = new Date(kline.time + timezoneOffset);

      // 获取北京时间的年、月、日、小时
      const year = beijingTime.getUTCFullYear();
      const month = beijingTime.getUTCMonth();
      const day = beijingTime.getUTCDate();
      const hours = beijingTime.getUTCHours();
      const minutes = beijingTime.getUTCMinutes();
      const seconds = beijingTime.getUTCSeconds();

      // 如果当前时间是17点之前，无需调整
      if (hours < 17) {
        // k线数据无需调整
      } else {
        // 获取前一个交易日
        let recentDaySessionDay = new Date(year, month, day - 1);
        let attempts = 0;

        // 最多尝试5次，找到前一个交易日
        while (attempts < 5) {
          const dayOfWeek = recentDaySessionDay.getUTCDay();
          if (dayOfWeek >= 1 && dayOfWeek <= 5) {
            // 找到交易日
            break;
          }
          // 如果不是交易日，继续往前推一天
          recentDaySessionDay.setDate(recentDaySessionDay.getDate() - 1);
          attempts++;
        }

        // 把kline的时间设置为前一个日盘，
        kline.time = recentDaySessionDay.getTime() + hours * 3600 * 1000 + minutes * 60 * 1000 + seconds * 1000;
      }

      return kline;
    });
  }

  private pricesWithin(price1: number, price2: number): boolean {
    return Math.abs(price1 - price2) / price1 < 0.01;
  }

  /**
   * 检查两个K线的OHLC是否完全相同
   * @param bar1 第一个K线
   * @param bar2 第二个K线
   * @returns 如果OHLC完全相同返回true，否则返回false
   */
  private pricesExactlyEqual(bar1: KLineData, bar2: KLineData): boolean {
    // 检查开盘价、最高价、最低价和收盘价是否完全相同
    const openEqual = bar1.open === bar2.open;
    const highEqual = bar1.high === bar2.high;
    const lowEqual = bar1.low === bar2.low;
    const closeEqual = bar1.close === bar2.close;

    const result = openEqual && highEqual && lowEqual && closeEqual;

    if (result) {
      console.log('[RealtimeKline] 检测到完全相同的K线数据:',
        `O=${bar1.open}, H=${bar1.high}, L=${bar1.low}, C=${bar1.close}`);
    }

    return result;
  }

  private getPeriodLength(period: KLineInterval): number {
    switch (period) {
      case KLineInterval.MIN1:
        return 60 * 1000;
      case KLineInterval.MIN5:
        return 5 * 60 * 1000;
      case KLineInterval.MIN15:
        return 15 * 60 * 1000;
      case KLineInterval.MIN30:
        return 30 * 60 * 1000;
      case KLineInterval.HOUR1:
        return 60 * 60 * 1000;
      case KLineInterval.HOUR4:
        return 4 * 60 * 60 * 1000;
      case KLineInterval.DAY1:
        return 24 * 60 * 60 * 1000;
      case KLineInterval.WEEK1:
        return 7 * 24 * 60 * 60 * 1000;
      default:
        return 0;
    }
  }

  private timeInRange(time: number, lastCachedTime: number, period: KLineInterval): boolean {

    // 如果两个时间不是毫秒计算，就转为毫秒
    if (time < 10000000000) {
      time *= 1000;
    }
    if (lastCachedTime < 10000000000) {
      lastCachedTime *= 1000;
    }

    // === 修改：处理日线及以上周期 (基于上海时区) ===
    if (period === KLineInterval.DAY1 || period === KLineInterval.WEEK1) {
      const SHANGHAI_OFFSET_MS = 8 * 60 * 60 * 1000; // UTC+8

      // 将时间戳调整到上海时区再创建Date对象
      const date1_sh = new Date(time + SHANGHAI_OFFSET_MS);
      const date2_sh = new Date(lastCachedTime + SHANGHAI_OFFSET_MS);

      // 使用 UTC 方法获取调整后日期(相当于上海时区的年月日)
      const isSameShanghaiDay = date1_sh.getUTCFullYear() === date2_sh.getUTCFullYear() &&
                                date1_sh.getUTCMonth() === date2_sh.getUTCMonth() &&
                                date1_sh.getUTCDate() === date2_sh.getUTCDate();

      //console.log(`[RealtimeKline] 日线/周线时间比较 (上海时区): ${new Date(time).toISOString()} vs ${new Date(lastCachedTime).toISOString()} => 上海日期相同: ${isSameShanghaiDay}`);
      return isSameShanghaiDay;
    }
    // === 结束：处理日线及以上周期 ===

    // 两个时间只差小于周期的时间长度就算符合条件
    const timeDiff = time - lastCachedTime;
    const periodLength = this.getPeriodLength(period);

    // 对于非日线周期，允许一定的误差（周期长度的一半）
    const result = Math.abs(timeDiff) <= periodLength / 2;
    //console.log(`[RealtimeKline] 小于日线周期时间比较: ${new Date(time).toISOString()} vs ${new Date(lastCachedTime).toISOString()}, timeDiff=${timeDiff}, periodLength/2=${periodLength/2} => 结果: ${result}`);
    return result;
  }

  /**
   * 验证并处理实时数据
   * 与缓存的K线数据进行对比，找到匹配点，提取有效数据
   */
  private validateAndProcessData(realtimeData: KLineData[], period: KLineInterval): [KLineData[], number] {
    if (!this.cachedKline || !this.cachedKline.data) {
        console.warn('[RealtimeKline] validateAndProcessData called with no cachedKline data.');
        return [[], -1]; // Should not happen if called correctly
    }
    const cachedData = this.cachedKline.data;
    const cachedLength = cachedData.length;

    console.log(`[RealtimeKline] 验证数据: 缓存长度=${cachedLength}, 实时数据长度=${realtimeData.length}, 周期=${period}`);

    // 空缓存直接返回实时数据
    if (cachedLength === 0) {
      console.log('[RealtimeKline] 缓存数据为空，返回全部实时数据');
      return [realtimeData, 0];
    }

    // --- 新增: 判断是否为周线或以上周期，并检查缓存长度 ---
    const isWeeklyOrGreater = period === KLineInterval.WEEK1; // Add future >= DAY1 periods here
    const useSecondLastLogic = isWeeklyOrGreater && cachedLength >= 2;

    if (isWeeklyOrGreater && cachedLength < 2) {
        console.log(`[RealtimeKline] ${period} 周期缓存数据少于2条 (${cachedLength}), 无法使用特殊匹配逻辑，尝试基本追加/更新`);
        // Fallback to simpler logic might be needed here, for now, let it proceed to standard check
    }
    // --- 结束 ---

    // --- 修改: 确定参考的缓存索引和时间 ---
    const refCachedIndex = useSecondLastLogic ? cachedLength - 2 : cachedLength - 1;
    const refCachedTime = cachedData[refCachedIndex].time;
    const refCachedBar = cachedData[refCachedIndex];
    console.log(`[RealtimeKline] 使用参考缓存索引: ${refCachedIndex}, 参考时间: ${new Date(refCachedTime * 1000).toISOString()}`);
    // --- 结束 ---

    const firstRealtimeTime = realtimeData[0]?.time;

    console.log('[RealtimeKline] 参考缓存时间:', refCachedTime, ' 参考缓存数据：', refCachedBar);
    console.log('[RealtimeKline] 实时数据第一条时间:', firstRealtimeTime, ' 数据：', realtimeData);

    // 场景1：实时数据是全新周期 (相对于参考时间)
    // 注意：对于周线逻辑，这可能意味着实时数据开始于最后一个（可能不完整）的缓存周期
    if (firstRealtimeTime > refCachedTime && !this.timeInRange(firstRealtimeTime, refCachedTime, period)) {
      console.log(`[RealtimeKline] 实时数据(${new Date(firstRealtimeTime*1000).toISOString()})在参考缓存时间(${new Date(refCachedTime*1000).toISOString()})之后`);

      // --- 修改：周线逻辑下，如果实时数据与 *最后* 一条缓存同周期，则合并更新最后一条 ---
      if (useSecondLastLogic) {
          const lastCachedBar = cachedData[cachedLength - 1];
          const lastCachedTime = lastCachedBar.time;

          // 找到实时数据中第一个与最后缓存数据同周期的数据
          let firstRealtimeInLastPeriodIndex = -1;
          for(let i=0; i < realtimeData.length; i++) {
              if (this.timeInRange(realtimeData[i].time, lastCachedTime, period)) {
                  firstRealtimeInLastPeriodIndex = i;
                  break;
              }
          }

          if (firstRealtimeInLastPeriodIndex !== -1) {
              console.log(`[RealtimeKline] ${period} 逻辑: 实时数据开始于最后一个缓存周期，更新最后一条并追加`);
              const mergedData = [
                  ...cachedData.slice(0, cachedLength - 1), // 保留到倒数第二条
                  ...realtimeData.slice(firstRealtimeInLastPeriodIndex) // 从第一个在最后周期的实时数据开始追加
              ];
              console.log('[RealtimeKline] 合并后数据：', mergedData);
              return [mergedData, cachedLength - 1]; // 新数据起始于被更新的最后一条
          } else {
              // 实时数据完全在新周期
               console.log(`[RealtimeKline] ${period} 逻辑: 实时数据在新周期，直接追加`);
               return [[...cachedData, ...realtimeData], cachedLength];
          }
      } else {
          // 标准逻辑：直接追加
          console.log('[RealtimeKline] 标准逻辑: 实时数据是全新周期，直接追加');
          return [[...cachedData, ...realtimeData], cachedLength];
      }
      // --- 结束修改 ---
    }


    // 场景2：需要寻找匹配点 (匹配 refCachedIndex)
    let matchedIndex = -1;
    for (let i = 0; i < realtimeData.length; i++) {
      // 比较实时数据点 `i` 与参考缓存点 `refCachedIndex` 的时间
      const isTimeMatch = this.timeInRange(realtimeData[i].time, refCachedTime, period);
      if (isTimeMatch) {

        // --- 修改: 价格检查也基于 refCachedIndex ---
        const checkCount = Math.min(3, i + 1, refCachedIndex + 1); // 能比较的数量取决于实时数据索引i 和 参考缓存索引refCachedIndex
        let dataValid = true;
        console.log(`[RealtimeKline] 时间匹配成功 (realtime[${i}] vs cached[${refCachedIndex}]), 检查前 ${checkCount} 条价格`);

        for (let j = 0; j < checkCount; j++) {
          const cachedIdx = refCachedIndex - j;
          const realtimeIdx = i - j;

          // 检查索引有效性
          if (cachedIdx < 0 || realtimeIdx < 0) {
              console.warn(`[RealtimeKline] 价格检查索引无效: cachedIdx=${cachedIdx}, realtimeIdx=${realtimeIdx}`);
              dataValid = false;
              break;
          }

          const cachedBar = cachedData[cachedIdx];
          const realtimeBar = realtimeData[realtimeIdx];

          // --- 新增：详细价格比较日志 ---
          console.log(`[RealtimeKline]   [j=${j}] 开始比较价格: cached[${cachedIdx}] vs realtime[${realtimeIdx}]`);
          const openMatch = this.pricesWithin(cachedBar.open, realtimeBar.open);
          const highMatch = this.pricesWithin(cachedBar.high, realtimeBar.high);
          const lowMatch = this.pricesWithin(cachedBar.low, realtimeBar.low);
          const closeMatch = this.pricesWithin(cachedBar.close, realtimeBar.close);
          console.log(`[RealtimeKline]     Cached: O=${cachedBar.open}, H=${cachedBar.high}, L=${cachedBar.low}, C=${cachedBar.close}`);
          console.log(`[RealtimeKline]     Realtime: O=${realtimeBar.open}, H=${realtimeBar.high}, L=${realtimeBar.low}, C=${realtimeBar.close}`);
          console.log(`[RealtimeKline]     匹配结果: Open=${openMatch}, High=${highMatch}, Low=${lowMatch}, Close=${closeMatch}`);
          // --- 结束：详细价格比较日志 ---

          let within = 0;
          if (openMatch) within++;
          if (highMatch) within++;
          if (lowMatch) within++;
          if (closeMatch) within++;

          console.log(`[RealtimeKline]   [j=${j}] 对比价格完成: cached[${cachedIdx}] vs realtime[${realtimeIdx}] => within=${within}`);

          // 价格相似度检查 (至少2个价格点相似)
          if (within < 2) {
            console.log(`[RealtimeKline]   [j=${j}] 价格不匹配 (within < 2)`);
            dataValid = false;
            break;
          }
        }
        // --- 结束修改 ---

        if (dataValid) {
          console.log(`[RealtimeKline] 价格匹配成功! matchedIndex=${i}`);
          matchedIndex = i;
          break; // 找到第一个匹配点即可
        } else {
          // 这个日志现在可能在价格检查循环内部打印过了，但保留以防万一
          console.log(`[RealtimeKline] 价格检查失败 (realtime[${i}] vs cached[${refCachedIndex}])`);
        }
      } // else: timeInRange returned false, continue loop
    }

    // 找不到匹配点
    if (matchedIndex === -1) {
      // --- 修改: 错误信息也基于 refCachedBar ---
      const lastCachedBarForError = cachedData[cachedLength - 1]; // 最后一条还是用于展示上下文
      const refCachedBarForError = refCachedBar; // 参考条用于说明匹配逻辑
      const firstRealtimeBars = realtimeData.slice(0, Math.min(5, realtimeData.length));
      const lastRealtimeBars = realtimeData.slice(-Math.min(3, realtimeData.length));

      let errorReason = "";
       if (realtimeData.length > 0) {
          errorReason = `原因：从接收到的实时数据开头(${new Date(realtimeData[0].time * 1000).toISOString()})开始，` +
                        `未能找到一个时间点，其时间与参考缓存时间(${new Date(refCachedTime * 1000).toISOString()})[索引${refCachedIndex}]足够接近，` +
                        `并且该点及其之前K线的价格与参考缓存点及其之前的K线的价格足够相似。`;
      } else {
          errorReason = "原因：接收到的实时数据为空，无法进行匹配。";
      }

      const errorDetail = `未能将收到的实时数据与缓存数据(参考索引 ${refCachedIndex})进行匹配。
        参考缓存时间: ${refCachedTime} (${new Date(refCachedTime * 1000).toISOString()}), 收盘价: ${refCachedBarForError?.close}
        缓存最后时间: ${lastCachedBarForError?.time} (${new Date(lastCachedBarForError?.time * 1000).toISOString()}), 收盘价: ${lastCachedBarForError?.close}
        收到实时数据起始(前${firstRealtimeBars.length}条): [
          ${firstRealtimeBars.map(b => `          { time: ${b?.time} (${new Date(b?.time * 1000).toISOString()}), close: ${b?.close} }`).join('\n')}
        ]
        收到实时数据末尾(后${lastRealtimeBars.length}条): [
          ${lastRealtimeBars.map(b => `          { time: ${b?.time} (${new Date(b?.time * 1000).toISOString()}), close: ${b?.close} }`).join('\n')}
        ]
        ${errorReason}`;
      // --- 结束修改 ---

      console.error('[RealtimeKline] 数据连续性验证失败:', errorDetail);

      EventBus.emit(RealtimeEvents.Types.REALTIME_ERROR, {
        message: '实时K线数据无法连续匹配缓存数据',
        detail: errorDetail
      });

      return [cachedData, -1]; // 返回原始缓存数据，标记为无更新
    }

    // 场景3：找到匹配点 `matchedIndex` (对应于 `refCachedIndex`)
    console.log(`[RealtimeKline] 找到匹配点: realtimeData[${matchedIndex}] 匹配 cachedData[${refCachedIndex}]`);

    // --- 新增: 处理周线及以上周期的合并逻辑 ---
    if (useSecondLastLogic) {
      // 周线逻辑: realtimeData[matchedIndex] 对应 cachedData[cachedLength - 2]
      // 我们需要用 realtimeData[matchedIndex + 1] 及之后的数据来更新/追加

      // 检查实时数据是否有下一条来更新最后一条缓存
      if (matchedIndex + 1 >= realtimeData.length) {
          console.log(`[RealtimeKline] ${period} 逻辑: 实时数据匹配到倒数第二条，但没有后续数据来更新最后一条。无更新。`);
          return [cachedData, -1]; // 没有足够的数据来更新最后那条不完整的bar
      }

      // 检查实时数据的下一条与缓存最后一条是否完全相同
      const nextRealtimeBar = realtimeData[matchedIndex + 1];
      const lastCachedBar = cachedData[cachedLength - 1];

      if (this.pricesExactlyEqual(nextRealtimeBar, lastCachedBar)) {
        console.log(`[RealtimeKline] ${period} 逻辑: 实时数据的下一条与缓存最后一条完全相同，跳过此条数据`);

        // 如果只有这一条数据，则无更新
        if (matchedIndex + 2 >= realtimeData.length) {
          console.log(`[RealtimeKline] ${period} 逻辑: 没有更多数据，无更新`);
          return [cachedData, -1];
        }

        // 否则从下下条开始追加
        const mergedData = [
          ...cachedData.slice(0, cachedLength), // 保留所有缓存数据
          ...realtimeData.slice(matchedIndex + 2) // 从匹配点的下下条开始追加
        ];
        const newDataIndex = cachedLength; // 更新从原缓存之后开始

        console.log(`[RealtimeKline] ${period} 逻辑合并完成(跳过相同数据)，新数据起始索引: ${newDataIndex}`);
        console.log('[RealtimeKline] 合并后数据：', mergedData);
        return [mergedData, newDataIndex];
      }

      // 保留旧数据到倒数第二条 + 从匹配点的下一条开始的实时数据
      const mergedData = [
        ...cachedData.slice(0, cachedLength - 1), // 保留到倒数第二条 (不包括最后一条)
        ...realtimeData.slice(matchedIndex + 1)   // 从匹配点的下一条开始追加
      ];
      const newDataIndex = cachedLength - 1; // 更新从原缓存的最后一条开始

      console.log(`[RealtimeKline] ${period} 逻辑合并完成，新数据起始索引: ${newDataIndex}`);
      console.log('[RealtimeKline] 合并后数据：', mergedData);
      return [mergedData, newDataIndex];

    }
    // --- 结束 ---
    else {
      // 标准逻辑 (日线及以下): realtimeData[matchedIndex] 对应 cachedData[cachedLength - 1]

      // 检查匹配点与缓存最后一条是否完全相同
      const matchedRealtimeBar = realtimeData[matchedIndex];
      const lastCachedBar = cachedData[cachedLength - 1];

      if (this.pricesExactlyEqual(matchedRealtimeBar, lastCachedBar)) {
        console.log(`[RealtimeKline] 标准逻辑: 实时数据的匹配点与缓存最后一条完全相同，跳过此条数据`);

        // 如果只有这一条数据，则无更新
        if (matchedIndex + 1 >= realtimeData.length) {
          console.log(`[RealtimeKline] 标准逻辑: 没有更多数据，无更新`);
          return [cachedData, -1];
        }

        // 否则从下一条开始追加
        const mergedData = [
          ...cachedData, // 保留所有缓存数据
          ...realtimeData.slice(matchedIndex + 1) // 从匹配点的下一条开始追加
        ];
        const newDataIndex = cachedLength; // 更新从原缓存之后开始

        console.log(`[RealtimeKline] 标准逻辑合并完成(跳过相同数据)，新数据起始索引: ${newDataIndex}`);
        console.log('[RealtimeKline] 合并后数据：', mergedData);
        return [mergedData, newDataIndex];
      }

      // 保留旧数据到倒数第一条之前 + 从匹配点开始的实时数据
      const mergedData = [
        ...cachedData.slice(0, cachedLength - 1), // 保留到倒数第一条之前 (不包括最后一条)
        ...realtimeData.slice(matchedIndex)       // 从匹配点开始追加
      ];
      const newDataIndex = cachedLength - 1; // 更新从原缓存的最后一条开始

      console.log(`[RealtimeKline] 标准逻辑合并完成，新数据起始索引: ${newDataIndex}`);
      console.log('[RealtimeKline] 合并后数据：', mergedData);
      return [mergedData, newDataIndex];
    }
  }

  /**
   * 断开连接
   */
  public async disconnect(): Promise<void> {
    if (this.socket) {
      console.log('[RealtimeKline] 断开WebSocket连接...');
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      console.log('[RealtimeKline] WebSocket连接已断开');

      // 释放WebSocket端口
      if (this.wsPort) {
        console.log(`[RealtimeKline] 正在释放WebSocket端口: ${this.wsPort}...`);
        await this.releaseWebSocketPort();
        console.log('[RealtimeKline] WebSocket端口已释放');
      }
    }
  }
}

// 创建单例实例
// 使用相对路径，让代理处理转发
const wsEndpoint = ''; // 空字符串表示使用当前站点的主机和端口
export const realtimeKline = new RealtimeKline(wsEndpoint);

// 导出初始化方法，便于在应用启动时调用
export const initRealtimeKline = async (): Promise<void> => {
  console.log('[RealtimeKline] 开始初始化实时K线模块，连接地址:', wsEndpoint || '当前站点');
  try {
    await realtimeKline.initialize();
    console.log('[RealtimeKline] 实时K线模块初始化完成');
  } catch (error) {
    console.error('[RealtimeKline] 实时K线模块初始化失败:', error);
  }
};
