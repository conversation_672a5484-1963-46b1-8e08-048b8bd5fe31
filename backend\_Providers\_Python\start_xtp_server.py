#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动XTP交易服务器的脚本
"""

import os
import sys
import json
import signal
import logging
import codecs

os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)


# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from xtp_trade_server import XTPTradeServer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在停止服务器...")
    sys.exit(0)

def load_config():
    """加载配置文件"""
    config_path = os.path.join(current_dir, 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        # 返回默认配置
        return {
            "xtp_trade_server": {
                "host": "127.0.0.1",
                "port": 6001,
                "target_url": "http://127.0.0.1:3000/api/trade/"
            }
        }

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 加载配置
    config = load_config()
    xtp_config = config.get('xtp_trade_server', {})
    
    host = xtp_config.get('host', '127.0.0.1')
    port = xtp_config.get('port', 6001)
    target_url = xtp_config.get('target_url', 'http://127.0.0.1:3000/api/trade/')
    
    # 创建并启动XTP服务器
    server = XTPTradeServer(host=host, port=port)
    
    logger.info("=" * 50)
    logger.info("启动自定义XTP交易服务器")
    logger.info(f"服务器地址: {host}:{port}")
    logger.info(f"转发目标: {target_url}")
    logger.info("按 Ctrl+C 停止服务器")
    logger.info("=" * 50)
    
    try:
        server.start()
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
