import os
import yaml
import json
import logging
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [ConfigGenerator] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('ConfigGenerator')

# 模板目录
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'live_configs', 'templates')
# 实例配置目录
INSTANCE_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'live_configs', 'instances')
# 日志目录
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'live_logs')
# 数据存储目录 - 指向数据引擎的存储目录
DATA_STORAGE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'utils', 'ext_data_engine', 'storage')
# 共享内存文件路径 - 指向数据引擎的共享内存文件
SHM_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'utils', 'ext_data_engine', 'exchange.membin')

def ensure_dirs():
    """确保必要的目录存在"""
    os.makedirs(TEMPLATE_DIR, exist_ok=True)
    os.makedirs(INSTANCE_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)

def read_template(template_name: str) -> str:
    """读取模板文件内容"""
    template_path = os.path.join(TEMPLATE_DIR, template_name)
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"读取模板文件 {template_name} 失败: {e}")
        raise

def write_config(strategy_id: str, config_name: str, content: str):
    """写入配置文件"""
    # 确保策略实例目录存在
    strategy_dir = os.path.join(INSTANCE_DIR, strategy_id)
    os.makedirs(strategy_dir, exist_ok=True)

    # 写入配置文件
    config_path = os.path.join(strategy_dir, config_name)
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"配置文件 {config_name} 已生成: {config_path}")
    except Exception as e:
        logger.error(f"写入配置文件 {config_name} 失败: {e}")
        raise

def replace_placeholders(template: str, variables: Dict[str, Any]) -> str:
    """替换模板中的占位符"""
    # 添加数据存储路径和共享内存文件路径
    variables['data_storage_dir'] = DATA_STORAGE_DIR
    variables['shm_file_path'] = SHM_FILE_PATH

    result = template
    for key, value in variables.items():
        placeholder = f"{{{{{key}}}}}"
        if isinstance(value, dict):
            # 如果值是字典，需要特殊处理
            for sub_key, sub_value in value.items():
                sub_placeholder = f"{{{{{key}.{sub_key}}}}}"
                result = result.replace(sub_placeholder, str(sub_value))
        else:
            result = result.replace(placeholder, str(value))
    return result

def determine_trading_type(strategy_yaml: str) -> str:
    """根据策略YAML确定交易类型"""
    try:
        strategy_config = yaml.safe_load(strategy_yaml)
        trading_type = strategy_config.get('trading_type', 'stk')

        # 标准化交易类型
        if 'stock' in trading_type.lower():
            return 'stk'
        elif 'etf' in trading_type.lower():
            return 'stk'  # ETF使用股票配置
        elif 'option' in trading_type.lower():
            return 'opt'
        elif 'crypto' in trading_type.lower():
            return 'cpt'
        else:
            return 'stk'  # 默认为股票
    except Exception as e:
        logger.error(f"解析策略YAML确定交易类型失败: {e}")
        return 'stk'  # 默认为股票

def determine_files(trading_type: str) -> Dict[str, str]:
    """根据交易类型确定使用的文件"""
    if trading_type == 'stk':
        return {
            'commodity_file': 'stk_comms.json',
            'contract_file': 'stocks.json',
            'session_file': 'stk_sessions.json',
            'fees_file': 'fees_stk.json'
        }
    elif trading_type == 'opt':
        return {
            'commodity_file': 'ifopt_comms.json',
            'contract_file': 'if_options.json',
            'session_file': 'sessions.json',
            'fees_file': 'fees.json'
        }
    elif trading_type == 'cpt':
        return {
            'commodity_file': 'cypto_comms.json',
            'contract_file': 'stocks.json',
            'session_file': 'btc_sessions.json',
            'fees_file': 'btc_fees.json'
        }
    else:
        return {
            'commodity_file': 'stk_comms.json',
            'contract_file': 'stocks.json',
            'session_file': 'stk_sessions.json',
            'fees_file': 'fees_stk.json'
        }

def generate_config_files(strategy_id: str, strategy_yaml: str, account_info: Dict[str, Any]) -> Dict[str, str]:
    """生成配置文件"""
    ensure_dirs()

    # 确定交易类型
    trading_type = determine_trading_type(strategy_yaml)
    logger.info(f"策略 {strategy_id} 的交易类型为: {trading_type}")

    # 确定使用的文件
    files = determine_files(trading_type)

    # 准备变量
    variables = {
        'strategy_id': strategy_id,
        'trading_type': trading_type,
        'account_id': account_info.get('account_id', 'easytrader_ths'),
        'account_host': account_info.get('account_host', '127.0.0.1'),
        'account_port': account_info.get('account_port', 8888),
        'account_user': account_info.get('account_user', 'user'),
        'account_pass': account_info.get('account_pass', 'pass'),
        'account_key': account_info.get('account_key', ''),
        'initial_capital': account_info.get('initial_capital', 100000),
        'risk_settings': account_info.get('risk_settings', {
            'max_order_size': 100,
            'max_daily_trades': 20,
            'stop_loss_percent': 5
        }),
        **files
    }

    # 生成配置文件
    config_files = {}

    # 主配置文件
    config_template = read_template('config_template.yaml')
    config_content = replace_placeholders(config_template, variables)
    write_config(strategy_id, 'config.yaml', config_content)
    config_files['config'] = config_content

    # 执行器配置文件
    executers_template = read_template('executers_template.yaml')
    executers_content = replace_placeholders(executers_template, variables)
    write_config(strategy_id, 'executers.yaml', executers_content)
    config_files['executers'] = executers_content

    # 交易通道配置文件
    tdtraders_template = read_template('tdtraders_template.yaml')
    tdtraders_content = replace_placeholders(tdtraders_template, variables)
    write_config(strategy_id, 'tdtraders.yaml', tdtraders_content)
    config_files['tdtraders'] = tdtraders_content

    # 行情解析器配置文件
    tdparsers_template = read_template('tdparsers_template.yaml')
    tdparsers_content = replace_placeholders(tdparsers_template, variables)
    write_config(strategy_id, 'tdparsers.yaml', tdparsers_content)
    config_files['tdparsers'] = tdparsers_content

    # 开平策略配置文件
    actpolicy_template = read_template('actpolicy_template.yaml')
    actpolicy_content = replace_placeholders(actpolicy_template, variables)
    write_config(strategy_id, 'actpolicy.yaml', actpolicy_content)
    config_files['actpolicy'] = actpolicy_content

    # 过滤器配置文件
    filters_template = read_template('filters_template.yaml')
    filters_content = replace_placeholders(filters_template, variables)
    write_config(strategy_id, 'filters.yaml', filters_content)
    config_files['filters'] = filters_content

    logger.info(f"策略 {strategy_id} 的配置文件已生成")
    return config_files

if __name__ == "__main__":
    # 测试配置生成
    test_strategy_id = "test_strategy"
    test_strategy_yaml = """
    trading_type: etf
    universe:
      - SSE.ETF.510300
      - SSE.ETF.510500
    """
    test_account_info = {
        'account_id': 'easytrader_ths',
        'account_host': '127.0.0.1',
        'account_port': 8888,
        'account_user': 'test_user',
        'account_pass': 'test_pass',
        'initial_capital': 100000,
        'risk_settings': {
            'max_order_size': 100,
            'max_daily_trades': 20,
            'stop_loss_percent': 5
        }
    }

    generate_config_files(test_strategy_id, test_strategy_yaml, test_account_info)

